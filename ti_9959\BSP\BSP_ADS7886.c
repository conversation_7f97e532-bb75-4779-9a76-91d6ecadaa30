/*
 * BSP_ADS7886.c
 *
 * 用于驱动ADS7886
 *
 *  Created on: 2024年8月19日
 *      Author: <PERSON><PERSON><PERSON>
 */
#include "BSP_ADS7886.h"

uint16_t ADS7886_Convert(void)
{
    uint16_t Data=0x00;

    MySPI_W_SS(0);
    for(uint8_t cnt = 0;cnt < 16;cnt ++)
    {
        MySPI_W_SCK(0);
        if(MySPI_R_MISO())
            Data |= 0x01;
        Data<<=1;
        MySPI_W_SCK(1);
    }
    MySPI_W_SS(1);

    return Data;
}

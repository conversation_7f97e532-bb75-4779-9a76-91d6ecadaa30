<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v3.2.2.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <link_time>0x669b55d0</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\empty_LP_MSPM0G3507_nortos_ticlang.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0xd49</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\OLED\</path>
         <kind>object</kind>
         <file>oled.o</file>
         <name>oled.o</name>
      </input_file>
      <input_file id="fl-11">
         <path>C:\Users\<USER>\workspace_ccstheia\empty_LP_MSPM0G3507_nortos_ticlang\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-12">
         <path>C:\ti\mspm0_sdk_2_00_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-13">
         <path>C:\ti\mspm0_sdk_2_00_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_i2c.o</name>
      </input_file>
      <input_file id="fl-22">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-23">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-24">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-25">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-26">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-27">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-28">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-29">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-2a">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-2b">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-2c">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-92">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-93">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-94">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-95">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-96">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-97">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.I2C0_IRQHandler</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.text.main</name>
         <load_address>0x1ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ec</run_address>
         <size>0x120</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.text.OLED_ShowChar</name>
         <load_address>0x30c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x30c</run_address>
         <size>0x118</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.text.OLED_Init</name>
         <load_address>0x424</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x424</run_address>
         <size>0x100</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.text.OLED_ShowNum</name>
         <load_address>0x524</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x524</run_address>
         <size>0xf6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-107">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0x61a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x61a</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-95"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.text.OLED_WR_Byte</name>
         <load_address>0x61c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x61c</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.text.OLED_ShowChinese</name>
         <load_address>0x6f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6f8</run_address>
         <size>0xa8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.text.OLED_DrawBMP</name>
         <load_address>0x7a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7a0</run_address>
         <size>0xa4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x844</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x844</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.text.OLED_ShowString</name>
         <load_address>0x8bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8bc</run_address>
         <size>0x70</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.text.OLED_Clear</name>
         <load_address>0x92c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x92c</run_address>
         <size>0x6a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.text.SYSCFG_DL_I2C_OLED_init</name>
         <load_address>0x998</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x998</run_address>
         <size>0x64</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.text.DL_I2C_fillControllerTXFIFO</name>
         <load_address>0x9fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9fc</run_address>
         <size>0x5e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-103">
         <name>.text.DL_I2C_startControllerTransfer</name>
         <load_address>0xa5c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa5c</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0xaac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xaac</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.text.__aeabi_uidivmod</name>
         <load_address>0xaf0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xaf0</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-94"/>
      </object_component>
      <object_component id="oc-ef">
         <name>.text.DL_GPIO_initPeripheralInputFunctionFeatures</name>
         <load_address>0xb30</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb30</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.text.OLED_Set_Pos</name>
         <load_address>0xb6c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb6c</run_address>
         <size>0x3c</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0xba8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xba8</run_address>
         <size>0x3c</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0xbe4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbe4</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.text.oled_pow</name>
         <load_address>0xc20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc20</run_address>
         <size>0x30</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0xc50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc50</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0xc7c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc7c</run_address>
         <size>0x2a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-117">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0xca6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xca6</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-116">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0xcce</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcce</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.text.DL_I2C_setControllerRXFIFOThreshold</name>
         <load_address>0xcf8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcf8</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.text.DL_I2C_setControllerTXFIFOThreshold</name>
         <load_address>0xd20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd20</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-54">
         <name>.text:_c_int00_noargs</name>
         <load_address>0xd48</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd48</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.text.DL_I2C_setAnalogGlitchFilterPulseWidth</name>
         <load_address>0xd70</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd70</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.text.DL_I2C_setClockConfig</name>
         <load_address>0xd96</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd96</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.text.delay_ms</name>
         <load_address>0xdbc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xdbc</run_address>
         <size>0x22</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f0">
         <name>.text.DL_GPIO_enableHiZ</name>
         <load_address>0xde0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xde0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.text.DL_I2C_disableInterrupt</name>
         <load_address>0xdfc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xdfc</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.text.DL_I2C_enableInterrupt</name>
         <load_address>0xe18</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe18</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.text.DL_I2C_isControllerRXFIFOEmpty</name>
         <load_address>0xe34</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe34</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-f6">
         <name>.text.DL_SYSCTL_setMCLKDivider</name>
         <load_address>0xe50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe50</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0xe6c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe6c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f5">
         <name>.text.DL_SYSCTL_setULPCLKDivider</name>
         <load_address>0xe88</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe88</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0xea4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xea4</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0xebc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xebc</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.text.DL_I2C_enableAnalogGlitchFilter</name>
         <load_address>0xed4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xed4</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-100">
         <name>.text.DL_I2C_enableController</name>
         <load_address>0xeec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xeec</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.text.DL_I2C_enableControllerClockStretching</name>
         <load_address>0xf04</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf04</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.text.DL_I2C_enablePower</name>
         <load_address>0xf1c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf1c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.text.DL_I2C_reset</name>
         <load_address>0xf34</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf34</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.text.DL_I2C_setTimerPeriod</name>
         <load_address>0xf4c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf4c</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0xf64</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf64</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0xf7c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf7c</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-102">
         <name>.text.DL_I2C_getControllerStatus</name>
         <load_address>0xf94</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf94</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.text.DL_I2C_receiveControllerData</name>
         <load_address>0xfa8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfa8</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.text.DL_I2C_resetControllerTransfer</name>
         <load_address>0xfbc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfbc</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.text.DL_SYSCTL_disableSYSPLL</name>
         <load_address>0xfd0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfd0</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-95">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0xfe4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfe4</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.text.DL_I2C_getPendingInterrupt</name>
         <load_address>0xff8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xff8</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.text:TI_memcpy_small</name>
         <load_address>0x100a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x100a</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-96"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x101c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x101c</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.text.DL_SYSCTL_disableHFXT</name>
         <load_address>0x1030</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1030</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x1040</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1040</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-44">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x104c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x104c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-92"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x1054</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1054</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x1058</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1058</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-77">
         <name>.text._system_pre_init</name>
         <load_address>0x105c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x105c</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.text:abort</name>
         <load_address>0x1060</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1060</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-15c">
         <name>.cinit..data.load</name>
         <load_address>0x1988</load_address>
         <readonly>true</readonly>
         <run_address>0x1988</run_address>
         <size>0x150</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-15a">
         <name>__TI_handler_table</name>
         <load_address>0x1ad8</load_address>
         <readonly>true</readonly>
         <run_address>0x1ad8</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-15d">
         <name>.cinit..bss.load</name>
         <load_address>0x1ae4</load_address>
         <readonly>true</readonly>
         <run_address>0x1ae4</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-15b">
         <name>__TI_cinit_table</name>
         <load_address>0x1aec</load_address>
         <readonly>true</readonly>
         <run_address>0x1aec</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-db">
         <name>.rodata.asc2_1608</name>
         <load_address>0x1068</load_address>
         <readonly>true</readonly>
         <run_address>0x1068</run_address>
         <size>0x5f0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.rodata.asc2_0806</name>
         <load_address>0x1658</load_address>
         <readonly>true</readonly>
         <run_address>0x1658</run_address>
         <size>0x228</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-da">
         <name>.rodata.Hzk</name>
         <load_address>0x1880</load_address>
         <readonly>true</readonly>
         <run_address>0x1880</run_address>
         <size>0xe0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.rodata.str1.162906114796922795351</name>
         <load_address>0x1960</load_address>
         <readonly>true</readonly>
         <run_address>0x1960</run_address>
         <size>0xe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.rodata.str1.92454112862644078121</name>
         <load_address>0x196e</load_address>
         <readonly>true</readonly>
         <run_address>0x196e</run_address>
         <size>0xb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.rodata.str1.7130503275715014631</name>
         <load_address>0x1979</load_address>
         <readonly>true</readonly>
         <run_address>0x1979</run_address>
         <size>0x7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.rodata.str1.161173632875555205051</name>
         <load_address>0x1980</load_address>
         <readonly>true</readonly>
         <run_address>0x1980</run_address>
         <size>0x6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-101">
         <name>.rodata.gI2C_OLEDClockConfig</name>
         <load_address>0x1986</load_address>
         <readonly>true</readonly>
         <run_address>0x1986</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-124">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-a2">
         <name>.data.BMP1</name>
         <load_address>0x20200000</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x400</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-65">
         <name>.common:gI2cControllerStatus</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200430</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-67">
         <name>.common:gTxLen</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020042c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-68">
         <name>.common:gTxPacket</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200410</run_address>
         <size>0x10</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-66">
         <name>.common:gTxCount</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200428</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-69">
         <name>.common:gRxCount</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200420</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-6a">
         <name>.common:gRxLen</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200424</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-6b">
         <name>.common:gRxPacket</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200400</run_address>
         <size>0x10</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xac</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_abbrev</name>
         <load_address>0xac</load_address>
         <run_address>0xac</run_address>
         <size>0x1a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-39">
         <name>.debug_abbrev</name>
         <load_address>0x252</load_address>
         <run_address>0x252</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_abbrev</name>
         <load_address>0x2bf</load_address>
         <run_address>0x2bf</run_address>
         <size>0x1c6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-104">
         <name>.debug_abbrev</name>
         <load_address>0x485</load_address>
         <run_address>0x485</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_abbrev</name>
         <load_address>0x4e7</load_address>
         <run_address>0x4e7</run_address>
         <size>0x1e7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_abbrev</name>
         <load_address>0x6ce</load_address>
         <run_address>0x6ce</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.debug_abbrev</name>
         <load_address>0x77d</load_address>
         <run_address>0x77d</run_address>
         <size>0x186</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_abbrev</name>
         <load_address>0x903</load_address>
         <run_address>0x903</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-70">
         <name>.debug_abbrev</name>
         <load_address>0x93c</load_address>
         <run_address>0x93c</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_abbrev</name>
         <load_address>0x9fe</load_address>
         <run_address>0x9fe</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_abbrev</name>
         <load_address>0xa6e</load_address>
         <run_address>0xa6e</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-de">
         <name>.debug_abbrev</name>
         <load_address>0xafb</load_address>
         <run_address>0xafb</run_address>
         <size>0xb3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_abbrev</name>
         <load_address>0xbae</load_address>
         <run_address>0xbae</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-92"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.debug_abbrev</name>
         <load_address>0xbd5</load_address>
         <run_address>0xbd5</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-94"/>
      </object_component>
      <object_component id="oc-118">
         <name>.debug_abbrev</name>
         <load_address>0xbfc</load_address>
         <run_address>0xbfc</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-95"/>
      </object_component>
      <object_component id="oc-c2">
         <name>.debug_abbrev</name>
         <load_address>0xc55</load_address>
         <run_address>0xc55</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-96"/>
      </object_component>
      <object_component id="oc-161">
         <name>.debug_abbrev</name>
         <load_address>0xc7a</load_address>
         <run_address>0xc7a</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1d0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_info</name>
         <load_address>0x1d0</load_address>
         <run_address>0x1d0</run_address>
         <size>0x29c4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x2b94</load_address>
         <run_address>0x2b94</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_info</name>
         <load_address>0x2c14</load_address>
         <run_address>0x2c14</run_address>
         <size>0x126c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.debug_info</name>
         <load_address>0x3e80</load_address>
         <run_address>0x3e80</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_info</name>
         <load_address>0x3ef5</load_address>
         <run_address>0x3ef5</run_address>
         <size>0xca0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0x4b95</load_address>
         <run_address>0x4b95</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.debug_info</name>
         <load_address>0x4fb8</load_address>
         <run_address>0x4fb8</run_address>
         <size>0x74a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_info</name>
         <load_address>0x5702</load_address>
         <run_address>0x5702</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_info</name>
         <load_address>0x5748</load_address>
         <run_address>0x5748</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_info</name>
         <load_address>0x58da</load_address>
         <run_address>0x58da</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_info</name>
         <load_address>0x59a0</load_address>
         <run_address>0x59a0</run_address>
         <size>0x180</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_info</name>
         <load_address>0x5b20</load_address>
         <run_address>0x5b20</run_address>
         <size>0xed</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_info</name>
         <load_address>0x5c0d</load_address>
         <run_address>0x5c0d</run_address>
         <size>0x19e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-92"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.debug_info</name>
         <load_address>0x5dab</load_address>
         <run_address>0x5dab</run_address>
         <size>0x1c1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-94"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.debug_info</name>
         <load_address>0x5f6c</load_address>
         <run_address>0x5f6c</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-95"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_info</name>
         <load_address>0x5ff1</load_address>
         <run_address>0x5ff1</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-96"/>
      </object_component>
      <object_component id="oc-160">
         <name>.debug_info</name>
         <load_address>0x62eb</load_address>
         <run_address>0x62eb</run_address>
         <size>0xbc</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x18b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_str</name>
         <load_address>0x18b</load_address>
         <run_address>0x18b</run_address>
         <size>0x1d0f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_str</name>
         <load_address>0x1e9a</load_address>
         <run_address>0x1e9a</run_address>
         <size>0x173</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_str</name>
         <load_address>0x200d</load_address>
         <run_address>0x200d</run_address>
         <size>0xe19</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-105">
         <name>.debug_str</name>
         <load_address>0x2e26</load_address>
         <run_address>0x2e26</run_address>
         <size>0x177</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_str</name>
         <load_address>0x2f9d</load_address>
         <run_address>0x2f9d</run_address>
         <size>0x8b9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_str</name>
         <load_address>0x3856</load_address>
         <run_address>0x3856</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_str</name>
         <load_address>0x3a7b</load_address>
         <run_address>0x3a7b</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_str</name>
         <load_address>0x3daa</load_address>
         <run_address>0x3daa</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_str</name>
         <load_address>0x3e9f</load_address>
         <run_address>0x3e9f</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_str</name>
         <load_address>0x403a</load_address>
         <run_address>0x403a</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_str</name>
         <load_address>0x41a2</load_address>
         <run_address>0x41a2</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-df">
         <name>.debug_str</name>
         <load_address>0x4377</load_address>
         <run_address>0x4377</run_address>
         <size>0x13f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-119">
         <name>.debug_str</name>
         <load_address>0x44b6</load_address>
         <run_address>0x44b6</run_address>
         <size>0x198</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-95"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_frame</name>
         <load_address>0x2c</load_address>
         <run_address>0x2c</run_address>
         <size>0x280</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0x2ac</load_address>
         <run_address>0x2ac</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_frame</name>
         <load_address>0x2dc</load_address>
         <run_address>0x2dc</run_address>
         <size>0x278</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.debug_frame</name>
         <load_address>0x554</load_address>
         <run_address>0x554</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_frame</name>
         <load_address>0x574</load_address>
         <run_address>0x574</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_frame</name>
         <load_address>0x6a0</load_address>
         <run_address>0x6a0</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.debug_frame</name>
         <load_address>0x730</load_address>
         <run_address>0x730</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-78">
         <name>.debug_frame</name>
         <load_address>0x830</load_address>
         <run_address>0x830</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_frame</name>
         <load_address>0x850</load_address>
         <run_address>0x850</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_frame</name>
         <load_address>0x888</load_address>
         <run_address>0x888</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_frame</name>
         <load_address>0x8b0</load_address>
         <run_address>0x8b0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.debug_frame</name>
         <load_address>0x8e0</load_address>
         <run_address>0x8e0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-109">
         <name>.debug_frame</name>
         <load_address>0x910</load_address>
         <run_address>0x910</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-95"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_line</name>
         <load_address>0x1a6</load_address>
         <run_address>0x1a6</run_address>
         <size>0x6af</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_line</name>
         <load_address>0x855</load_address>
         <run_address>0x855</run_address>
         <size>0xba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_line</name>
         <load_address>0x90f</load_address>
         <run_address>0x90f</run_address>
         <size>0xb3d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.debug_line</name>
         <load_address>0x144c</load_address>
         <run_address>0x144c</run_address>
         <size>0xe4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_line</name>
         <load_address>0x1530</load_address>
         <run_address>0x1530</run_address>
         <size>0x617</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_line</name>
         <load_address>0x1b47</load_address>
         <run_address>0x1b47</run_address>
         <size>0x1fe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.debug_line</name>
         <load_address>0x1d45</load_address>
         <run_address>0x1d45</run_address>
         <size>0x4fb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-79">
         <name>.debug_line</name>
         <load_address>0x2240</load_address>
         <run_address>0x2240</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-25"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_line</name>
         <load_address>0x227e</load_address>
         <run_address>0x227e</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_line</name>
         <load_address>0x2376</load_address>
         <run_address>0x2376</run_address>
         <size>0xbf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_line</name>
         <load_address>0x2435</load_address>
         <run_address>0x2435</run_address>
         <size>0x1c7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.debug_line</name>
         <load_address>0x25fc</load_address>
         <run_address>0x25fc</run_address>
         <size>0x6b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_line</name>
         <load_address>0x2667</load_address>
         <run_address>0x2667</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-92"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.debug_line</name>
         <load_address>0x270b</load_address>
         <run_address>0x270b</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-94"/>
      </object_component>
      <object_component id="oc-108">
         <name>.debug_line</name>
         <load_address>0x27cd</load_address>
         <run_address>0x27cd</run_address>
         <size>0xb7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-95"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_line</name>
         <load_address>0x2884</load_address>
         <run_address>0x2884</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-96"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xe0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_ranges</name>
         <load_address>0xe0</load_address>
         <run_address>0xe0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_ranges</name>
         <load_address>0xf8</load_address>
         <run_address>0xf8</run_address>
         <size>0xc8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_ranges</name>
         <load_address>0x1c0</load_address>
         <run_address>0x1c0</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_ranges</name>
         <load_address>0x398</load_address>
         <run_address>0x398</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_ranges</name>
         <load_address>0x3e0</load_address>
         <run_address>0x3e0</run_address>
         <size>0xa8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_ranges</name>
         <load_address>0x488</load_address>
         <run_address>0x488</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_ranges</name>
         <load_address>0x4a0</load_address>
         <run_address>0x4a0</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.debug_ranges</name>
         <load_address>0x4d0</load_address>
         <run_address>0x4d0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.debug_ranges</name>
         <load_address>0x4e8</load_address>
         <run_address>0x4e8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-95"/>
      </object_component>
      <object_component id="oc-8c">
         <name>.debug_ranges</name>
         <load_address>0x500</load_address>
         <run_address>0x500</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-96"/>
      </object_component>
      <object_component id="oc-106">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-12"/>
      </object_component>
      <object_component id="oc-62">
         <name>.debug_loc</name>
         <load_address>0x13</load_address>
         <run_address>0x13</run_address>
         <size>0x31c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-13"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_loc</name>
         <load_address>0x32f</load_address>
         <run_address>0x32f</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-22"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.debug_loc</name>
         <load_address>0x407</load_address>
         <run_address>0x407</run_address>
         <size>0x480</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-24"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_loc</name>
         <load_address>0x887</load_address>
         <run_address>0x887</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-27"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_loc</name>
         <load_address>0x9f3</load_address>
         <run_address>0x9f3</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-28"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_loc</name>
         <load_address>0xa62</load_address>
         <run_address>0xa62</run_address>
         <size>0x166</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-29"/>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_loc</name>
         <load_address>0xbc8</load_address>
         <run_address>0xbc8</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2a"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.debug_loc</name>
         <load_address>0xbee</load_address>
         <run_address>0xbee</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-95"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-92"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-94"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-96"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0xfa8</size>
         <contents>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-d0"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-ef"/>
            <object_component_ref idref="oc-d1"/>
            <object_component_ref idref="oc-ca"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-cf"/>
            <object_component_ref idref="oc-cb"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-f0"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-f6"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-f5"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-a9"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x1988</load_address>
         <run_address>0x1988</run_address>
         <size>0x178</size>
         <contents>
            <object_component_ref idref="oc-15c"/>
            <object_component_ref idref="oc-15a"/>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-15b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x1068</load_address>
         <run_address>0x1068</run_address>
         <size>0x920</size>
         <contents>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-101"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-124"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200000</run_address>
         <size>0x400</size>
         <contents>
            <object_component_ref idref="oc-a2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200400</run_address>
         <size>0x31</size>
         <contents>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-6b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-15f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11b" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11c" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11d" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11e" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11f" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-120" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-122" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13e" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc89</size>
         <contents>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-c2"/>
            <object_component_ref idref="oc-161"/>
         </contents>
      </logical_group>
      <logical_group id="lg-140" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x63a7</size>
         <contents>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-160"/>
         </contents>
      </logical_group>
      <logical_group id="lg-142" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x464e</size>
         <contents>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-119"/>
         </contents>
      </logical_group>
      <logical_group id="lg-144" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x940</size>
         <contents>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-109"/>
         </contents>
      </logical_group>
      <logical_group id="lg-146" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2924</size>
         <contents>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-8d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-148" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x528</size>
         <contents>
            <object_component_ref idref="oc-98"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-8c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-14a" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0e</size>
         <contents>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-11a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-154" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x68</size>
         <contents>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-8e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-15e" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-16b" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1b00</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-16c" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x431</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-e"/>
            <logical_group_ref idref="lg-f"/>
         </contents>
      </load_segment>
      <load_segment id="lg-16d" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x1b00</used_space>
         <unused_space>0x1e500</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0xfa8</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x1068</start_address>
               <size>0x920</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x1988</start_address>
               <size>0x178</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x1b00</start_address>
               <size>0x1e500</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x631</used_space>
         <unused_space>0x79cf</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-120"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-122"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x400</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200400</start_address>
               <size>0x31</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200431</start_address>
               <size>0x79cf</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x1988</load_address>
            <load_size>0x150</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x400</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x1ae4</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200400</run_address>
            <run_size>0x31</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x1aec</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x1afc</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x1afc</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x1ad8</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x1ae4</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3f">
         <name>main</name>
         <value>0x1ed</value>
         <object_component_ref idref="oc-7b"/>
      </symbol>
      <symbol id="sm-40">
         <name>BMP1</name>
         <value>0x20200000</value>
         <object_component_ref idref="oc-a2"/>
      </symbol>
      <symbol id="sm-aa">
         <name>SYSCFG_DL_init</name>
         <value>0xfe5</value>
         <object_component_ref idref="oc-95"/>
      </symbol>
      <symbol id="sm-ab">
         <name>SYSCFG_DL_initPower</name>
         <value>0xaad</value>
         <object_component_ref idref="oc-c9"/>
      </symbol>
      <symbol id="sm-ac">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0xba9</value>
         <object_component_ref idref="oc-ca"/>
      </symbol>
      <symbol id="sm-ad">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0xc7d</value>
         <object_component_ref idref="oc-cb"/>
      </symbol>
      <symbol id="sm-ae">
         <name>SYSCFG_DL_I2C_OLED_init</name>
         <value>0x999</value>
         <object_component_ref idref="oc-cc"/>
      </symbol>
      <symbol id="sm-b9">
         <name>Default_Handler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-ba">
         <name>Reset_Handler</name>
         <value>0x1059</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-bb">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-bc">
         <name>NMI_Handler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-bd">
         <name>HardFault_Handler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-be">
         <name>SVC_Handler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-bf">
         <name>PendSV_Handler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c0">
         <name>SysTick_Handler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c1">
         <name>GROUP0_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c2">
         <name>GROUP1_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c3">
         <name>TIMG8_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c4">
         <name>UART3_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c5">
         <name>ADC0_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c6">
         <name>ADC1_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c7">
         <name>CANFD0_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c8">
         <name>DAC0_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-c9">
         <name>SPI0_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-ca">
         <name>SPI1_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-cb">
         <name>UART1_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-cc">
         <name>UART2_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-cd">
         <name>UART0_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-ce">
         <name>TIMG0_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-cf">
         <name>TIMG6_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d0">
         <name>TIMA0_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d1">
         <name>TIMA1_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d2">
         <name>TIMG7_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d3">
         <name>TIMG12_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d4">
         <name>I2C1_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d5">
         <name>AES_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d6">
         <name>RTC_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-d7">
         <name>DMA_IRQHandler</name>
         <value>0x1055</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-11a">
         <name>delay_ms</name>
         <value>0xdbd</value>
         <object_component_ref idref="oc-9c"/>
      </symbol>
      <symbol id="sm-11b">
         <name>OLED_WR_Byte</name>
         <value>0x61d</value>
         <object_component_ref idref="oc-d0"/>
      </symbol>
      <symbol id="sm-11c">
         <name>gI2cControllerStatus</name>
         <value>0x20200430</value>
      </symbol>
      <symbol id="sm-11d">
         <name>gTxLen</name>
         <value>0x2020042c</value>
      </symbol>
      <symbol id="sm-11e">
         <name>gTxPacket</name>
         <value>0x20200410</value>
      </symbol>
      <symbol id="sm-11f">
         <name>gTxCount</name>
         <value>0x20200428</value>
      </symbol>
      <symbol id="sm-120">
         <name>OLED_Set_Pos</name>
         <value>0xb6d</value>
         <object_component_ref idref="oc-d1"/>
      </symbol>
      <symbol id="sm-121">
         <name>OLED_Clear</name>
         <value>0x92d</value>
         <object_component_ref idref="oc-9d"/>
      </symbol>
      <symbol id="sm-122">
         <name>OLED_ShowChar</name>
         <value>0x30d</value>
         <object_component_ref idref="oc-a0"/>
      </symbol>
      <symbol id="sm-123">
         <name>asc2_1608</name>
         <value>0x1068</value>
         <object_component_ref idref="oc-db"/>
      </symbol>
      <symbol id="sm-124">
         <name>asc2_0806</name>
         <value>0x1658</value>
         <object_component_ref idref="oc-dc"/>
      </symbol>
      <symbol id="sm-125">
         <name>oled_pow</name>
         <value>0xc21</value>
         <object_component_ref idref="oc-dd"/>
      </symbol>
      <symbol id="sm-126">
         <name>OLED_ShowNum</name>
         <value>0x525</value>
         <object_component_ref idref="oc-a1"/>
      </symbol>
      <symbol id="sm-127">
         <name>OLED_ShowString</name>
         <value>0x8bd</value>
         <object_component_ref idref="oc-9f"/>
      </symbol>
      <symbol id="sm-128">
         <name>OLED_ShowChinese</name>
         <value>0x6f9</value>
         <object_component_ref idref="oc-9e"/>
      </symbol>
      <symbol id="sm-129">
         <name>Hzk</name>
         <value>0x1880</value>
         <object_component_ref idref="oc-da"/>
      </symbol>
      <symbol id="sm-12a">
         <name>OLED_DrawBMP</name>
         <value>0x7a1</value>
         <object_component_ref idref="oc-9b"/>
      </symbol>
      <symbol id="sm-12b">
         <name>OLED_Init</name>
         <value>0x425</value>
         <object_component_ref idref="oc-9a"/>
      </symbol>
      <symbol id="sm-12c">
         <name>I2C0_IRQHandler</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-12d">
         <name>gRxCount</name>
         <value>0x20200420</value>
      </symbol>
      <symbol id="sm-12e">
         <name>gRxLen</name>
         <value>0x20200424</value>
      </symbol>
      <symbol id="sm-12f">
         <name>gRxPacket</name>
         <value>0x20200400</value>
      </symbol>
      <symbol id="sm-130">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-131">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-132">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-133">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-134">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-135">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-136">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-137">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-138">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-141">
         <name>DL_Common_delayCycles</name>
         <value>0x1041</value>
         <object_component_ref idref="oc-d2"/>
      </symbol>
      <symbol id="sm-14d">
         <name>DL_I2C_setClockConfig</name>
         <value>0xd97</value>
         <object_component_ref idref="oc-f7"/>
      </symbol>
      <symbol id="sm-14e">
         <name>DL_I2C_fillControllerTXFIFO</name>
         <value>0x9fd</value>
         <object_component_ref idref="oc-5f"/>
      </symbol>
      <symbol id="sm-159">
         <name>_c_int00_noargs</name>
         <value>0xd49</value>
         <object_component_ref idref="oc-54"/>
      </symbol>
      <symbol id="sm-15a">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-166">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0xbe5</value>
         <object_component_ref idref="oc-b0"/>
      </symbol>
      <symbol id="sm-16e">
         <name>_system_pre_init</name>
         <value>0x105d</value>
         <object_component_ref idref="oc-77"/>
      </symbol>
      <symbol id="sm-179">
         <name>__TI_zero_init_nomemset</name>
         <value>0xf7d</value>
         <object_component_ref idref="oc-4b"/>
      </symbol>
      <symbol id="sm-182">
         <name>__TI_decompress_none</name>
         <value>0x101d</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-18d">
         <name>__TI_decompress_lzss</name>
         <value>0x845</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-197">
         <name>abort</name>
         <value>0x1061</value>
         <object_component_ref idref="oc-a9"/>
      </symbol>
      <symbol id="sm-198">
         <name>C$$EXIT</name>
         <value>0x1060</value>
         <object_component_ref idref="oc-a9"/>
      </symbol>
      <symbol id="sm-1a0">
         <name>__aeabi_memcpy</name>
         <value>0x104d</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-1a1">
         <name>__aeabi_memcpy4</name>
         <value>0x104d</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-1a2">
         <name>__aeabi_memcpy8</name>
         <value>0x104d</value>
         <object_component_ref idref="oc-44"/>
      </symbol>
      <symbol id="sm-1a8">
         <name>__aeabi_uidiv</name>
         <value>0xaf1</value>
         <object_component_ref idref="oc-d6"/>
      </symbol>
      <symbol id="sm-1a9">
         <name>__aeabi_uidivmod</name>
         <value>0xaf1</value>
         <object_component_ref idref="oc-d6"/>
      </symbol>
      <symbol id="sm-1b3">
         <name>__aeabi_idiv0</name>
         <value>0x61b</value>
         <object_component_ref idref="oc-107"/>
      </symbol>
      <symbol id="sm-1bc">
         <name>TI_memcpy_small</name>
         <value>0x100b</value>
         <object_component_ref idref="oc-8b"/>
      </symbol>
      <symbol id="sm-1bd">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1c0">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1c1">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>

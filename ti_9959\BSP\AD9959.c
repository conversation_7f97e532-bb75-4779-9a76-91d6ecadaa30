#include "User_Header.h"
#include"AD9959.h"

uint8_t CSR_DATA0[1] = {0x10};     // 开 CH0
uint8_t CSR_DATA1[1] = {0x20};      // 开 CH1
uint8_t CSR_DATA2[1] = {0x40};      // 开 CH2
uint8_t CSR_DATA3[1] = {0x80};      // 开 CH3		
uint8_t CSR_DATAall[1] = {0xF0}; 		//															
 
uint8_t FR2_DATA[2] = {0x00,0x00};//default Value = 0x0000
uint8_t CFR_DATA[3] = {0x00,0x03,0x02};//default Value = 0x000302	   
																	
uint8_t CPOW0_DATA0[2] = {0x00,0x00};//default Value = 0x0000   @ = POW/2^14*360
uint8_t CPOW0_DATA1[2] = {0x00,0x00};																	
uint8_t CPOW0_DATA2[2] = {0x00,0x00};
uint8_t CPOW0_DATA3[2] = {0x00,0x00};

uint8_t LSRR_DATA[2] = {0x00,0x00};//default Value = 0x----
																	
uint8_t RDW_DATA[4] = {0x00,0x00,0x00,0x00};//default Value = 0x--------
																	
uint8_t FDW_DATA[4] = {0x00,0x00,0x00,0x00};//default Value = 0x--------

uint32_t SweepStepFre = 100;
float SweepData[100]={0};


//AD9959初始化
void Init_AD9959(void)  
{ 
	uint8_t CFTW0_DATA[4] ={0x00,0x00,0x00,0x00};	//中间变量
	uint32_t Temp;  
		
	uint32_t A_temp;//=0x23ff;
	uint8_t ACR_DATA[3] = {0x00,0x00,0x00};//default Value = 0x--0000 Rest = 18.91/Iout 

	uint16_t P_temp=0;
	   
 	uint8_t FR1_DATA[3] = {0xD0,0x00,0x00};//20

	Intserve();  //IO口初始化
    IntReset();  //AD9959复位  
	
	//写功能寄存器1  三个字节		写入0xD0,0x00,0x00  更新IO寄存器
     WriteData_AD9959(FR1_ADD,3,FR1_DATA,1);					//设置为高频率时钟并且锁相环设置为20

		Temp=(uint32_t)1000000*8.589934592;	   		//数字频率转化到32位寄存器数据对应  初始化输出1M的频率
        CFTW0_DATA[3]=(uint8_t)Temp;							//最低8位
        CFTW0_DATA[2]=(uint8_t)(Temp>>8);
        CFTW0_DATA[1]=(uint8_t)(Temp>>16);
        CFTW0_DATA[0]=(uint8_t)(Temp>>24);				//最高8位
		
		//1023为最大输出电压，及寄存器后十位为1 对应输出电压576mv需要根据板子来校准！！！！！！！！！！！！！
		A_temp=(uint32_t)((300.0/576.0f)*1023);//输出300mv
		A_temp|=0x1000;
		ACR_DATA[2] = (uint8_t)A_temp;  	//低位数据
		ACR_DATA[1] = (uint8_t)((A_temp>>8)|0x10); //高位数据
			
		WriteData_AD9959(CSR_ADD,1,CSR_DATAall,1);//选择所有通道
		WriteData_AD9959(CFTW0_ADD,4,CFTW0_DATA,1);//设置频率
		WriteData_AD9959(ACR_ADD,3,ACR_DATA,1);//设置幅度
		WriteData_AD9959(CPOW0_ADD,2,CPOW0_DATA0,1);//设置相位
		
		//初始化输出同向
		Write_Phase(0,30);
		Write_Phase(1,30);
		Write_Phase(2,30);
		Write_Phase(3,30);

		//初始化输出幅值为500mv
		Write_Amplitude(0, 500);
		Write_Amplitude(1, 500);
		Write_Amplitude(2, 500);
		Write_Amplitude(3, 500);

} 


//延时
void delay1 (uint32_t length)
{
	length = length*12;
   while(length--);
}

void Intserve(void)		   
{   
	AD9959_PWR_0;
    CS_1;							//文档中为CS非
    SCLK_0;
    UPDATE_0;
    PS0_0;
    PS1_0;
    PS2_0;
    PS3_0;
    SDIO0_0;
    SDIO1_0;
    SDIO2_0;
    SDIO3_0;
}

//AD9959复位
void IntReset(void)	  
{
  Reset_0;
	delay1(1);
	Reset_1;			//高电平复位引脚
	delay1(30);
	Reset_0;
}


 //AD9959更新数据
void IO_Update(void)  
{
	UPDATE_0;
	delay1(2);
	UPDATE_1;
	delay1(4);
	UPDATE_0;
}


/*--------------------------------------------
函数功能：控制器通过SPI向AD9959写数据
RegisterAddress: 寄存器地址
NumberofRegisters: 所含字节数
*RegisterData: 数据起始地址
temp: 是否更新IO寄存器
----------------------------------------------*/
void WriteData_AD9959(uint8_t RegisterAddress, uint8_t NumberofRegisters, uint8_t *RegisterData,uint8_t temp)
{
	uint8_t	ControlValue = 0;
	uint8_t	ValueToWrite = 0;
	uint8_t	RegisterIndex = 0;
	uint8_t	i = 0;

	ControlValue = RegisterAddress;
//写入地址
	SCLK_0;
	CS_0;	 
	for(i=0; i<8; i++)
	{
		SCLK_0;
		if(0x80 == (ControlValue & 0x80))
		SDIO0_1;	  
		else
		SDIO0_0;	  
		SCLK_1;
		ControlValue <<= 1;
	}
	SCLK_0;
//写入数据
	for (RegisterIndex=0; RegisterIndex<NumberofRegisters; RegisterIndex++)
	{
		ValueToWrite = RegisterData[RegisterIndex];
		for (i=0; i<8; i++)
		{
			SCLK_0;
			if(0x80 == (ValueToWrite & 0x80))
			SDIO0_1;	  
			else
			SDIO0_0;	  
			SCLK_1;
			ValueToWrite <<= 1;
		}
		SCLK_0;		
	}	
	if(temp==1)
		IO_Update();	
  CS_1;
} 



uint8_t CFTW0_DATA[4] ={0x00,0x00,0x00,0x00};	//中间变量
/*---------------------------------------
函数功能：设置通道输出频率
Channel:  输出通道
Freq:     输出频率
---------------------------------------*/
void Write_frequence(uint8_t Channel,uint32_t Freq)
{	 
		
	  uint32_t Temp;     

		if(Freq>500000000)
			Freq = 500000000;
		//将输入频率因子分为四个字节 8.58993459=(2^32)/500000000 u32
	  Temp=(uint32_t)Freq*8.589934592;	   			//数字频率转化到32位寄存器数据对应 
	  CFTW0_DATA[3]=(uint8_t)Temp;							//最低8位
	  CFTW0_DATA[2]=(uint8_t)(Temp>>8);
	  CFTW0_DATA[1]=(uint8_t)(Temp>>16);
	  CFTW0_DATA[0]=(uint8_t)(Temp>>24);				//最高8位
		
	  if(Channel==0)	  
	  {
			//通道0使能 但是频率控制字由传参fre控制
			WriteData_AD9959(CSR_ADD,1,CSR_DATA0,1);		//控制寄存器写入CH0通道
      WriteData_AD9959(CFTW0_ADD,4,CFTW0_DATA,1);	//CTW0 address 0x04.输出CH0设定频率
		}
	  else if(Channel==1)	
	  {//通道一
			WriteData_AD9959(CSR_ADD,1,CSR_DATA1,1);//控制寄存器写入CH1通道
      WriteData_AD9959(CFTW0_ADD,4,CFTW0_DATA,1);//CTW0 address 0x04.输出CH1设定频率	
	  }
	  else if(Channel==2)	
	  {//通道2
			WriteData_AD9959(CSR_ADD,1,CSR_DATA2,1);//控制寄存器写入CH2通道
      WriteData_AD9959(CFTW0_ADD,4,CFTW0_DATA,1);//CTW0 address 0x04.输出CH2设定频率	
	  }
	  else if(Channel==3)	
	  {//通道3
			WriteData_AD9959(CSR_ADD,1,CSR_DATA3,1);//控制寄存器写入CH3通道
      WriteData_AD9959(CFTW0_ADD,4,CFTW0_DATA,1);//CTW0 address 0x04.输出CH3设定频率	
	  }						

	
} 



void Write_frequence_no_update(uint8_t Channel,uint32_t Freq)
{	 
//		u8 CFTW0_DATA[4] ={0x00,0x00,0x00,0x00};	//中间变量
	  uint32_t Temp;     

		if(Freq>500000000)
			Freq = 500000000;
		//将输入频率因子分为四个字节 8.58993459=(2^32)/500000000
	  Temp=(uint32_t)Freq*8.589934592;	   			//数字频率转化到32位寄存器数据对应 
	  CFTW0_DATA[3]=(uint8_t)Temp;							//最低8位
	  CFTW0_DATA[2]=(uint8_t)(Temp>>8);
	  CFTW0_DATA[1]=(uint8_t)(Temp>>16);
	  CFTW0_DATA[0]=(uint8_t)(Temp>>24);				//最高8位
		
	  if(Channel==0)	  
	  {
			//通道0使能 但是频率控制字由传参fre控制
			WriteData_AD9959(CSR_ADD,1,CSR_DATA0,0);		//控制寄存器写入CH0通道
      WriteData_AD9959(CFTW0_ADD,4,CFTW0_DATA,0);	//CTW0 address 0x04.输出CH0设定频率
		}
	  else if(Channel==1)	
	  {//通道一
			WriteData_AD9959(CSR_ADD,1,CSR_DATA1,0);//控制寄存器写入CH1通道
      WriteData_AD9959(CFTW0_ADD,4,CFTW0_DATA,0);//CTW0 address 0x04.输出CH1设定频率	
	  }
	  else if(Channel==2)	
	  {//通道2
			WriteData_AD9959(CSR_ADD,1,CSR_DATA2,0);//控制寄存器写入CH2通道
      WriteData_AD9959(CFTW0_ADD,4,CFTW0_DATA,0);//CTW0 address 0x04.输出CH2设定频率	
	  }
	  else if(Channel==3)	
	  {//通道3
			WriteData_AD9959(CSR_ADD,1,CSR_DATA3,0);//控制寄存器写入CH3通道
      WriteData_AD9959(CFTW0_ADD,4,CFTW0_DATA,0);//CTW0 address 0x04.输出CH3设定频率	
	  }	
	  else if(Channel==4)	
	  {//全部通道
			WriteData_AD9959(CSR_ADD,1,CSR_DATAall,0);//控制寄存器写入CH3通道
      WriteData_AD9959(CFTW0_ADD,4,CFTW0_DATA,0);//CTW0 address 0x04.输出CH3设定频率	
	  }						

	
} 



/*---------------------------------------
函数功能：设置通道输出幅度
Channel:  输出通道
Ampli:    输出幅度
---------------------------------------*/
	uint8_t ACR_DATA[3] = {0x00,0x00,0x00};//default Value = 0x--0000 Rest = 18.91/Iout 
void Write_Amplitude(uint8_t Channel, uint16_t Ampli)
{ 
	uint32_t A_temp;//=0x23ff;

	
	if(Ampli>576)
			Ampli = 576;
	A_temp=(uint32_t)((Ampli/576.0f)*1023);//输出300mv		寄存器后十位都为1时幅值最大，需要校准每块板子！！！！！
	A_temp|=0x1000;
	ACR_DATA[2] = (uint8_t)A_temp;  	//低位数据
	ACR_DATA[1] = (uint8_t)((A_temp>>8)|0x10); //高位数据
	
  if(Channel==0)
  {
		WriteData_AD9959(CSR_ADD,1,CSR_DATA0,1); 
    WriteData_AD9959(ACR_ADD,3,ACR_DATA,1); 
	}
  else if(Channel==1)
  {
		WriteData_AD9959(CSR_ADD,1,CSR_DATA1,1); 
    WriteData_AD9959(ACR_ADD,3,ACR_DATA,1);
	}
  else if(Channel==2)
  {
	  WriteData_AD9959(CSR_ADD,1,CSR_DATA2,1); 
    WriteData_AD9959(ACR_ADD,3,ACR_DATA,1); 
	}
  else if(Channel==3)
  {
		WriteData_AD9959(CSR_ADD,1,CSR_DATA3,1); 
    WriteData_AD9959(ACR_ADD,3,ACR_DATA,1); 
	}
}



void Write_Amplitude_no_update(uint8_t Channel, uint16_t Ampli)
{ 
	uint32_t A_temp;//=0x23ff;
	uint8_t ACR_DATA[3] = {0x00,0x00,0x00};//default Value = 0x--0000 Rest = 18.91/Iout 
	
	if(Ampli>576)
			Ampli = 576;
	A_temp=(uint32_t)((Ampli/576.0f)*1023);//输出300mv		寄存器后十位都为1时幅值最大，需要校准每块板子！！！！！
	A_temp|=0x1000;
	ACR_DATA[2] = (uint8_t)A_temp;  	//低位数据
	ACR_DATA[1] = (uint8_t)((A_temp>>8)|0x10); //高位数据
	
  if(Channel==0)
  {
		WriteData_AD9959(CSR_ADD,1,CSR_DATA0,0); 
    WriteData_AD9959(ACR_ADD,3,ACR_DATA,0); 
	}
  else if(Channel==1)
  {
		WriteData_AD9959(CSR_ADD,1,CSR_DATA1,0); 
    WriteData_AD9959(ACR_ADD,3,ACR_DATA,0);
	}
  else if(Channel==2)
  {
	  WriteData_AD9959(CSR_ADD,1,CSR_DATA2,0); 
    WriteData_AD9959(ACR_ADD,3,ACR_DATA,0); 
	}
  else if(Channel==3)
  {
		WriteData_AD9959(CSR_ADD,1,CSR_DATA3,0); 
    WriteData_AD9959(ACR_ADD,3,ACR_DATA,0); 
	}
}


/*---------------------------------------
函数功能：设置通道输出相位
Channel:  输出通道
Phase:    输出相位,范围：0~16383(对应角度：0°~360°)
---------------------------------------*/
void Write_Phase(uint8_t Channel,uint16_t Phase)
{
	uint16_t P_temp=0;
	
	if(Phase > 360)
				Phase = 360;
	P_temp = (uint16_t)(Phase *45.511111 );
//	P_temp &= 0x3FFF;
	
	//加入转化的逻辑
	CPOW0_DATA0[1]=(uint8_t)P_temp;
	CPOW0_DATA0[0]=(uint8_t)(P_temp>>8);
	
	
	if(Channel==0)
  {//使能通道1		写入相位控制字
		WriteData_AD9959(CSR_ADD,1,CSR_DATA0,1); 
    WriteData_AD9959(CPOW0_ADD,2,CPOW0_DATA0,1);
  }
  else if(Channel==1)
  {
		WriteData_AD9959(CSR_ADD,1,CSR_DATA1,1); 
    WriteData_AD9959(CPOW0_ADD,2,CPOW0_DATA0,1);
  }
  else if(Channel==2)
  {
		WriteData_AD9959(CSR_ADD,1,CSR_DATA2,1); 
    WriteData_AD9959(CPOW0_ADD,2,CPOW0_DATA0,1);
  }
  else if(Channel==3)
  {
		WriteData_AD9959(CSR_ADD,1,CSR_DATA3,1); 
    WriteData_AD9959(CPOW0_ADD,2,CPOW0_DATA0,1);
  }
	else if(Channel==4)	//用于所有通道先同步相位
  {
		WriteData_AD9959(CSR_ADD,1,CSR_DATAall,1); 
    WriteData_AD9959(CPOW0_ADD,2,CPOW0_DATA0,1);
  }
	
}	 




void Write_Phase_no_update(uint8_t Channel,uint16_t Phase)
{
	uint16_t P_temp=0;
	
	if(Phase > 360)
				Phase = 360;
	P_temp = (uint16_t)(Phase *45.511111 );
//	P_temp &= 0x3FFF;
	
	//加入转化的逻辑
	CPOW0_DATA0[1]=(uint8_t)P_temp;
	CPOW0_DATA0[0]=(uint8_t)(P_temp>>8);
	
	
	if(Channel==0)
  {//使能通道1		写入相位控制字
		WriteData_AD9959(CSR_ADD,1,CSR_DATA0,0); 
    WriteData_AD9959(CPOW0_ADD,2,CPOW0_DATA0,0);
  }
  else if(Channel==1)
  {
		WriteData_AD9959(CSR_ADD,1,CSR_DATA1,0); 
    WriteData_AD9959(CPOW0_ADD,2,CPOW0_DATA0,0);
  }
  else if(Channel==2)
  {
		WriteData_AD9959(CSR_ADD,1,CSR_DATA2,0); 
    WriteData_AD9959(CPOW0_ADD,2,CPOW0_DATA0,0);
  }
  else if(Channel==3)
  {
		WriteData_AD9959(CSR_ADD,1,CSR_DATA3,0); 
    WriteData_AD9959(CPOW0_ADD,2,CPOW0_DATA0,0);
  }
	else if(Channel==4)	//用于所有通道先同步相位
  {
		WriteData_AD9959(CSR_ADD,1,CSR_DATAall,0); 
    WriteData_AD9959(CPOW0_ADD,2,CPOW0_DATA0,0);
  }
	
}


void SweepFre(uint32_t SweepMinFre,uint32_t SweepMaxFre,uint16_t SweepTime)//扫频
{
		float ex,P,T,Frecurrent,temp;
		uint16_t i=0;
		
		if(SweepTime > 10)
			SweepTime =10;
		// Timerx_Init(625*SweepTime-1,1151);	
	
		temp=log10((SweepMaxFre/SweepMinFre)/1);
		P=pow(10,temp);
		
		for(i=0;i<100;i++)
		{
													//计算T
			SweepData[i]=pow(P,(0.01*i+log10(SweepMinFre)/log10(P)));
		}

}





/*
 * BSP_4x4KEY.c
 *
 * 用于驱动4x4矩阵键盘
 *
 *  Created on: 2024年8月19日
 *      Author: <PERSON><PERSON><PERSON>
 */
#include "BSP_4x4KEY.h"

#define key_timer 10

extern uint8_t ImageBW[2888];

uint8_t Read4X4KEY( void )
{
    uint8_t key_val = KEY_NULL;

    // 拉低行,列为低则选中,以此类推
    ROW1_RESET;
    if( !COL1_READ )
    {
        delay_ms(key_timer);
        if( !COL1_READ )key_val = KEY_NUM1;
    }
    else if( !COL2_READ )
    {
        delay_ms(key_timer);
        if( !COL2_READ )key_val = KEY_NUM2;
    }
    else if( !COL3_READ )
    {
        delay_ms(key_timer);
        if( !COL3_READ )key_val = KEY_NUM3;
    }
    else if( !COL4_READ )
    {
        delay_ms(key_timer);
        if( !COL4_READ )key_val = KEY_A;
    }
        
    ROW1_SET;

    ROW2_RESET;
    if( !COL1_READ )
    {
        delay_ms(key_timer);
        if( !COL1_READ )key_val = KEY_NUM4;
    }
    else if( !COL2_READ )
    {
        delay_ms(key_timer);
        if( !COL2_READ )key_val = KEY_NUM5;
    }
    else if( !COL3_READ )
    {
        delay_ms(key_timer);
        if( !COL3_READ )key_val = KEY_NUM6;
    }
    else if( !COL4_READ )
    {
        delay_ms(key_timer);
        if( !COL4_READ )key_val = KEY_B;
    }
        
    ROW2_SET;

    ROW3_RESET;
    if( !COL1_READ )
    {
        delay_ms(key_timer);
        if( !COL1_READ )key_val = KEY_NUM7;
    }
    else if( !COL2_READ )
    {
        delay_ms(key_timer);
        if( !COL2_READ )key_val = KEY_NUM8;
    }
    else if( !COL3_READ )
    {
        delay_ms(key_timer);
        if( !COL3_READ )key_val = KEY_NUM9;
    }
    else if( !COL4_READ )
    {
        delay_ms(key_timer);
        if( !COL4_READ )key_val = KEY_C;
    }
        
    ROW3_SET;

    ROW4_RESET;
    if( !COL1_READ )
    {
        delay_ms(key_timer);
        if( !COL1_READ )key_val = KEY_STAR;
    }
    else if( !COL2_READ )
    {
        delay_ms(key_timer);
        if( !COL2_READ )key_val = KEY_NUM0;
    } 
    else if( !COL3_READ )
    {
        delay_ms(key_timer);
        if( !COL3_READ )key_val = KEY_HASH;
    }
    else if( !COL4_READ )
    {
        delay_ms(key_timer);
        if( !COL4_READ )key_val = KEY_D;
    }
        
    ROW4_SET;
    delay_ms(50);
    return key_val;
}

// 将按键映射为相应的数字
uint8_t keyToUint(uint8_t key)
{
    switch (key) {
        case KEY_NUM0: return 0;
        case KEY_NUM1: return 1;
        case KEY_NUM2: return 2;
        case KEY_NUM3: return 3;
        case KEY_NUM4: return 4;
        case KEY_NUM5: return 5;
        case KEY_NUM6: return 6;
        case KEY_NUM7: return 7;
        case KEY_NUM8: return 8;
        case KEY_NUM9: return 9;
        case KEY_STAR: return 10;
        default:       return 255;
    }
}

float keyboard_readnum(void)
{
    float number;
    uint8_t num, point_flag, point_cnt, key_index;
    uint8_t key_val = KEY_NULL;
    char format[10];

    key_index = 0;
    point_cnt = 0;
    point_flag = 0;
    number = 0;

    EPD_ShowNum(0,1,0,"Enter number:");
    EPD_ShowNum(0,8,number,"%.0f");
    EPD_Display(ImageBW);
    EPD_PartUpdate();
    while (key_val != KEY_C && key_index < 7) // 按D确认
    {
        key_val = Read4X4KEY();

        if (key_val == KEY_NUM1 || key_val == KEY_NUM2 || key_val == KEY_NUM3 ||
            key_val == KEY_NUM4 || key_val == KEY_NUM5 || key_val == KEY_NUM6 ||
            key_val == KEY_NUM7 || key_val == KEY_NUM8 || key_val == KEY_NUM9 ||
            key_val == KEY_NUM0 || key_val == KEY_STAR)
        {
            num = keyToUint(key_val);

            if (num <= 9)
            {
                if (point_flag == 0) // 输入整数
                {
                    number = number * 10 + num;
                }
                else if (point_flag == 1) // 输入小数
                {
                    point_cnt++;
                    number += num * pow(10, -point_cnt);
                }
                key_index++;
            }
            else if (num == 10) // 按下小数点
            {
                if (point_flag == 0) // 只处理第一次按下的小数点
                {
                    point_flag = 1;
                    strcpy(format, "%.0f.  "); // 显示整数部分加上小数点
                    EPD_ShowNum(0,8,number,format);
                    EPD_Display(ImageBW);
                    EPD_PartUpdate();
                }
                continue; // 忽略后续的多次小数点输入
            }

            // 根据小数点前后动态调整显示格式
            if (point_flag == 0)
            {
                strcpy(format, "%.0f  ");
            }
            else
            {
                sprintf(format, "%%.%df  ", point_cnt);
            }
            EPD_ShowNum(0,8,number,format);
            EPD_Display(ImageBW);
            EPD_PartUpdate();
        }
    }
    EPD_ShowNum(0,1,0,"              ");
    EPD_ShowNum(0,8,0,"              ");
    EPD_Display(ImageBW);
    EPD_PartUpdate();
    return number;
}

/*
 * BSP_ADS112C04.h
 *
 *  Created on: 2024年8月19日
 *      Author: <PERSON><PERSON><PERSON>
 */
#ifndef __BSP_ADS112C4_H_
#define __BSP_ADS112C4_H_

#include "User_Header.h"

#define ADS112_RESET        0X06
#define ADS112_START        0X08
#define ADS112_POWERDOWM    0X02
#define ADS112_RDATA        0X10

void ADS112_WriteReg(uint8_t RegAddress,uint8_t Data);
uint8_t ADS112_ReadReg(uint8_t RegAddress);
void ADS112_Reset(void);
void ADS112_Start(void);
void ADS112_Powerdowm(void);
uint16_t ADS112_Get_Data(void);
void ADS112_Set(void);
void ADS112_Init(void);
uint16_t ADS112_Get_Once_Data(void);
uint16_t ADS112_Get_Continuous_Data(void);
void ADS112_Reg_Init(void);

#endif /* __BSP_ADS112C4_H_ */
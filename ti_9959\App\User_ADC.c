/*
 * User_ADC.c
 *
 * ADC+DMA+Timer
 *
 *  Created on: 2024年8月19日
 *      Author: <PERSON><PERSON><PERSON>
 */
#include "User_ADC.h"

uint16_t gADCSamples[SampNum];
bool adc_flag = true;

// 1:采样率；2：采样点数
void User_ADC_Init(float fs)
{
    DL_DMA_setSrcAddr(DMA, DMA_CH0_CHAN_ID,(uint32_t) 0x40556280);
    DL_DMA_setDestAddr(DMA, DMA_CH0_CHAN_ID, (uint32_t) &gADCSamples[0]);
    DL_DMA_setTransferSize(DMA, DMA_CH0_CHAN_ID, SampNum);
    DL_DMA_enableChannel(DMA, DMA_CH0_CHAN_ID);

    Set_Fs(fs);
    NVIC_EnableIRQ(ADC12_0_INST_INT_IRQN);
}

float Set_Fs(float fs)
{
    float real_fs;
    uint16_t cnt;
    cnt = 32000000/fs;
    real_fs = 32000000/cnt;
    DL_TimerA_setLoadValue(TIMER_0_INST,cnt);

    return real_fs;
}

void Get_AC_Vol(void)
{
    uint16_t cnt;
    // 启动转换
    DL_TimerA_startCounter(TIMER_0_INST);

    // 等待转换完成
    adc_flag = false;
    while(adc_flag == false);

    // 数字量转模拟量
    for( cnt = 0; cnt < SampNum;cnt ++)
    {
        FFT_Data[cnt].real = gADCSamples[cnt]*3.3/4096;
        FFT_Data[cnt].imag = 0;
    }
}

void ADC12_0_INST_IRQHandler(void)
{
    switch (DL_ADC12_getPendingInterrupt(ADC12_0_INST)) {
        case DL_ADC12_IIDX_DMA_DONE:
            DL_TimerA_stopCounter(TIMER_0_INST);
            adc_flag = true;
            break;
        default:
            break;
    }
}
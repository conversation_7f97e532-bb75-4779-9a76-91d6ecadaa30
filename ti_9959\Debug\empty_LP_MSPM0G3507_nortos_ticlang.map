******************************************************************************
            TI ARM Clang Linker PC v3.2.2                      
******************************************************************************
>> Linked Sat Jul 20 14:14:40 2024

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00000d49


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00001b00  0001e500  R  X
  SRAM                  20200000   00008000  00000631  000079cf  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00001b00   00001b00    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000fa8   00000fa8    r-x .text
  00001068    00001068    00000920   00000920    r-- .rodata
  00001988    00001988    00000178   00000178    r-- .cinit
20200000    20200000    00000431   00000000    rw-
  20200000    20200000    00000400   00000000    rw- .data
  20200400    20200400    00000031   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000fa8     
                  000000c0    0000012c     oled.o (.text.I2C0_IRQHandler)
                  000001ec    00000120     empty.o (.text.main)
                  0000030c    00000118     oled.o (.text.OLED_ShowChar)
                  00000424    00000100     oled.o (.text.OLED_Init)
                  00000524    000000f6     oled.o (.text.OLED_ShowNum)
                  0000061a    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  0000061c    000000dc     oled.o (.text.OLED_WR_Byte)
                  000006f8    000000a8     oled.o (.text.OLED_ShowChinese)
                  000007a0    000000a4     oled.o (.text.OLED_DrawBMP)
                  00000844    00000078     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000008bc    00000070     oled.o (.text.OLED_ShowString)
                  0000092c    0000006a     oled.o (.text.OLED_Clear)
                  00000996    00000002     --HOLE-- [fill = 0]
                  00000998    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000009fc    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00000a5a    00000002     --HOLE-- [fill = 0]
                  00000a5c    00000050     oled.o (.text.DL_I2C_startControllerTransfer)
                  00000aac    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000af0    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00000b30    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  00000b6c    0000003c     oled.o (.text.OLED_Set_Pos)
                  00000ba8    0000003c     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000be4    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000c20    00000030     oled.o (.text.oled_pow)
                  00000c50    0000002c     oled.o (.text.__NVIC_EnableIRQ)
                  00000c7c    0000002a     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000ca6    00000028     oled.o (.text.DL_Common_updateReg)
                  00000cce    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00000cf6    00000002     --HOLE-- [fill = 0]
                  00000cf8    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00000d20    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  00000d48    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00000d70    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  00000d96    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00000dbc    00000022     oled.o (.text.delay_ms)
                  00000dde    00000002     --HOLE-- [fill = 0]
                  00000de0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00000dfc    0000001c     oled.o (.text.DL_I2C_disableInterrupt)
                  00000e18    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00000e34    0000001c     oled.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00000e50    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setMCLKDivider)
                  00000e6c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00000e88    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00000ea4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00000ebc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00000ed4    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  00000eec    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00000f04    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00000f1c    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00000f34    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00000f4c    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00000f64    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00000f7c    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00000f92    00000002     --HOLE-- [fill = 0]
                  00000f94    00000014     oled.o (.text.DL_I2C_getControllerStatus)
                  00000fa8    00000014     oled.o (.text.DL_I2C_receiveControllerData)
                  00000fbc    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00000fd0    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00000fe4    00000014     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000ff8    00000012     oled.o (.text.DL_I2C_getPendingInterrupt)
                  0000100a    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  0000101c    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  0000102e    00000002     --HOLE-- [fill = 0]
                  00001030    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00001040    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  0000104a    00000002     --HOLE-- [fill = 0]
                  0000104c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00001054    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001058    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  0000105c    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00001060    00000004            : exit.c.obj (.text:abort)
                  00001064    00000004     --HOLE-- [fill = 0]

.cinit     0    00001988    00000178     
                  00001988    00000150     (.cinit..data.load) [load image, compression = lzss]
                  00001ad8    0000000c     (__TI_handler_table)
                  00001ae4    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00001aec    00000010     (__TI_cinit_table)
                  00001afc    00000004     --HOLE-- [fill = 0]

.rodata    0    00001068    00000920     
                  00001068    000005f0     oled.o (.rodata.asc2_1608)
                  00001658    00000228     oled.o (.rodata.asc2_0806)
                  00001880    000000e0     oled.o (.rodata.Hzk)
                  00001960    0000000e     empty.o (.rodata.str1.162906114796922795351)
                  0000196e    0000000b     empty.o (.rodata.str1.92454112862644078121)
                  00001979    00000007     empty.o (.rodata.str1.7130503275715014631)
                  00001980    00000006     empty.o (.rodata.str1.161173632875555205051)
                  00001986    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    00000400     UNINITIALIZED
                  20200000    00000400     empty.o (.data.BMP1)

.bss       0    20200400    00000031     UNINITIALIZED
                  20200400    00000010     (.common:gRxPacket)
                  20200410    00000010     (.common:gTxPacket)
                  20200420    00000004     (.common:gRxCount)
                  20200424    00000004     (.common:gRxLen)
                  20200428    00000004     (.common:gTxCount)
                  2020042c    00000004     (.common:gTxLen)
                  20200430    00000001     (.common:gI2cControllerStatus)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       empty.o                        288    38        1024   
       ti_msp_dl_config.o             920    2         0      
       startup_mspm0g350x_ticlang.o   8      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         1216   232       1024   
                                                              
    .\OLED\
       oled.o                         2272   2296      49     
    +--+------------------------------+------+---------+---------+
       Total:                         2272   2296      49     
                                                              
    C:/ti/mspm0_sdk_2_00_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_i2c.o                       132    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         142    0         0      
                                                              
    C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     120    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       exit.c.obj                     4      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         286    0         0      
                                                              
    C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang/15.0.7/lib/armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_uidivmod.S.obj           64     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         74     0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      372       0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   3990   2900      1585   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00001aec records: 2, size/record: 8, table size: 16
	.data: load addr=00001988, load size=00000150 bytes, run addr=20200000, run size=00000400 bytes, compression=lzss
	.bss: load addr=00001ae4, load size=00000008 bytes, run addr=20200400, run size=00000031 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00001ad8 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
00001055  ADC0_IRQHandler               
00001055  ADC1_IRQHandler               
00001055  AES_IRQHandler                
20200000  BMP1                          
00001060  C$$EXIT                       
00001055  CANFD0_IRQHandler             
00001055  DAC0_IRQHandler               
00001041  DL_Common_delayCycles         
000009fd  DL_I2C_fillControllerTXFIFO   
00000d97  DL_I2C_setClockConfig         
00001055  DMA_IRQHandler                
00001055  Default_Handler               
00001055  GROUP0_IRQHandler             
00001055  GROUP1_IRQHandler             
00001055  HardFault_Handler             
00001880  Hzk                           
000000c1  I2C0_IRQHandler               
00001055  I2C1_IRQHandler               
00001055  NMI_Handler                   
0000092d  OLED_Clear                    
000007a1  OLED_DrawBMP                  
00000425  OLED_Init                     
00000b6d  OLED_Set_Pos                  
0000030d  OLED_ShowChar                 
000006f9  OLED_ShowChinese              
00000525  OLED_ShowNum                  
000008bd  OLED_ShowString               
0000061d  OLED_WR_Byte                  
00001055  PendSV_Handler                
00001055  RTC_IRQHandler                
00001059  Reset_Handler                 
00001055  SPI0_IRQHandler               
00001055  SPI1_IRQHandler               
00001055  SVC_Handler                   
00000ba9  SYSCFG_DL_GPIO_init           
00000999  SYSCFG_DL_I2C_OLED_init       
00000c7d  SYSCFG_DL_SYSCTL_init         
00000fe5  SYSCFG_DL_init                
00000aad  SYSCFG_DL_initPower           
00001055  SysTick_Handler               
00001055  TIMA0_IRQHandler              
00001055  TIMA1_IRQHandler              
00001055  TIMG0_IRQHandler              
00001055  TIMG12_IRQHandler             
00001055  TIMG6_IRQHandler              
00001055  TIMG7_IRQHandler              
00001055  TIMG8_IRQHandler              
0000100b  TI_memcpy_small               
00001055  UART0_IRQHandler              
00001055  UART1_IRQHandler              
00001055  UART2_IRQHandler              
00001055  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00001aec  __TI_CINIT_Base               
00001afc  __TI_CINIT_Limit              
00001afc  __TI_CINIT_Warm               
00001ad8  __TI_Handler_Table_Base       
00001ae4  __TI_Handler_Table_Limit      
00000be5  __TI_auto_init_nobinit_nopinit
00000845  __TI_decompress_lzss          
0000101d  __TI_decompress_none          
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
00000f7d  __TI_zero_init_nomemset       
0000061b  __aeabi_idiv0                 
0000104d  __aeabi_memcpy                
0000104d  __aeabi_memcpy4               
0000104d  __aeabi_memcpy8               
00000af1  __aeabi_uidiv                 
00000af1  __aeabi_uidivmod              
ffffffff  __binit__                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
00000d49  _c_int00_noargs               
UNDEFED   _system_post_cinit            
0000105d  _system_pre_init              
00001061  abort                         
00001658  asc2_0806                     
00001068  asc2_1608                     
ffffffff  binit                         
00000dbd  delay_ms                      
20200430  gI2cControllerStatus          
20200420  gRxCount                      
20200424  gRxLen                        
20200400  gRxPacket                     
20200428  gTxCount                      
2020042c  gTxLen                        
20200410  gTxPacket                     
00000000  interruptVectors              
000001ed  main                          
00000c21  oled_pow                      


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  I2C0_IRQHandler               
000001ed  main                          
00000200  __STACK_SIZE                  
0000030d  OLED_ShowChar                 
00000425  OLED_Init                     
00000525  OLED_ShowNum                  
0000061b  __aeabi_idiv0                 
0000061d  OLED_WR_Byte                  
000006f9  OLED_ShowChinese              
000007a1  OLED_DrawBMP                  
00000845  __TI_decompress_lzss          
000008bd  OLED_ShowString               
0000092d  OLED_Clear                    
00000999  SYSCFG_DL_I2C_OLED_init       
000009fd  DL_I2C_fillControllerTXFIFO   
00000aad  SYSCFG_DL_initPower           
00000af1  __aeabi_uidiv                 
00000af1  __aeabi_uidivmod              
00000b6d  OLED_Set_Pos                  
00000ba9  SYSCFG_DL_GPIO_init           
00000be5  __TI_auto_init_nobinit_nopinit
00000c21  oled_pow                      
00000c7d  SYSCFG_DL_SYSCTL_init         
00000d49  _c_int00_noargs               
00000d97  DL_I2C_setClockConfig         
00000dbd  delay_ms                      
00000f7d  __TI_zero_init_nomemset       
00000fe5  SYSCFG_DL_init                
0000100b  TI_memcpy_small               
0000101d  __TI_decompress_none          
00001041  DL_Common_delayCycles         
0000104d  __aeabi_memcpy                
0000104d  __aeabi_memcpy4               
0000104d  __aeabi_memcpy8               
00001055  ADC0_IRQHandler               
00001055  ADC1_IRQHandler               
00001055  AES_IRQHandler                
00001055  CANFD0_IRQHandler             
00001055  DAC0_IRQHandler               
00001055  DMA_IRQHandler                
00001055  Default_Handler               
00001055  GROUP0_IRQHandler             
00001055  GROUP1_IRQHandler             
00001055  HardFault_Handler             
00001055  I2C1_IRQHandler               
00001055  NMI_Handler                   
00001055  PendSV_Handler                
00001055  RTC_IRQHandler                
00001055  SPI0_IRQHandler               
00001055  SPI1_IRQHandler               
00001055  SVC_Handler                   
00001055  SysTick_Handler               
00001055  TIMA0_IRQHandler              
00001055  TIMA1_IRQHandler              
00001055  TIMG0_IRQHandler              
00001055  TIMG12_IRQHandler             
00001055  TIMG6_IRQHandler              
00001055  TIMG7_IRQHandler              
00001055  TIMG8_IRQHandler              
00001055  UART0_IRQHandler              
00001055  UART1_IRQHandler              
00001055  UART2_IRQHandler              
00001055  UART3_IRQHandler              
00001059  Reset_Handler                 
0000105d  _system_pre_init              
00001060  C$$EXIT                       
00001061  abort                         
00001068  asc2_1608                     
00001658  asc2_0806                     
00001880  Hzk                           
00001ad8  __TI_Handler_Table_Base       
00001ae4  __TI_Handler_Table_Limit      
00001aec  __TI_CINIT_Base               
00001afc  __TI_CINIT_Limit              
00001afc  __TI_CINIT_Warm               
20200000  BMP1                          
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200400  gRxPacket                     
20200410  gTxPacket                     
20200420  gRxCount                      
20200424  gRxLen                        
20200428  gTxCount                      
2020042c  gTxLen                        
20200430  gI2cControllerStatus          
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[107 symbols]

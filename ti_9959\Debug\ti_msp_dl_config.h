/*
 * Copyright (c) 2023, Texas Instruments Incorporated - http://www.ti.com
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, IN<PERSON>DENT<PERSON>, SPECIAL,
 * EXEMPLARY, OR <PERSON><PERSON><PERSON><PERSON>UENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/*
 *  ============ ti_msp_dl_config.h =============
 *  Configured MSPM0 DriverLib module declarations
 *
 *  DO NOT EDIT - This file is generated for the MSPM0G350X
 *  by the SysConfig tool.
 */
#ifndef ti_msp_dl_config_h
#define ti_msp_dl_config_h

#define CONFIG_MSPM0G350X
#define CONFIG_MSPM0G3505

#if defined(__ti_version__) || defined(__TI_COMPILER_VERSION__)
#define SYSCONFIG_WEAK __attribute__((weak))
#elif defined(__IAR_SYSTEMS_ICC__)
#define SYSCONFIG_WEAK __weak
#elif defined(__GNUC__)
#define SYSCONFIG_WEAK __attribute__((weak))
#endif

#include <ti/devices/msp/msp.h>
#include <ti/driverlib/driverlib.h>
#include <ti/driverlib/m0p/dl_core.h>

#ifdef __cplusplus
extern "C" {
#endif

/*
 *  ======== SYSCFG_DL_init ========
 *  Perform all required MSP DL initialization
 *
 *  This function should be called once at a point before any use of
 *  MSP DL.
 */


/* clang-format off */

#define POWER_STARTUP_DELAY                                                (16)


#define CPUCLK_FREQ                                                     32000000



/* Defines for PWM_0 */
#define PWM_0_INST                                                         TIMG8
#define PWM_0_INST_IRQHandler                                   TIMG8_IRQHandler
#define PWM_0_INST_INT_IRQN                                     (TIMG8_INT_IRQn)
#define PWM_0_INST_CLK_FREQ                                             32000000
/* GPIO defines for channel 0 */
#define GPIO_PWM_0_C0_PORT                                                 GPIOA
#define GPIO_PWM_0_C0_PIN                                          DL_GPIO_PIN_7
#define GPIO_PWM_0_C0_IOMUX                                      (IOMUX_PINCM14)
#define GPIO_PWM_0_C0_IOMUX_FUNC                     IOMUX_PINCM14_PF_TIMG8_CCP0
#define GPIO_PWM_0_C0_IDX                                    DL_TIMER_CC_0_INDEX



/* Defines for TIMER_0 */
#define TIMER_0_INST                                                     (TIMA1)
#define TIMER_0_INST_IRQHandler                                 TIMA1_IRQHandler
#define TIMER_0_INST_INT_IRQN                                   (TIMA1_INT_IRQn)
#define TIMER_0_INST_LOAD_VALUE                                            (31U)
#define TIMER_0_INST_PUB_0_CH                                                (1)
/* Defines for TIMER_9959 */
#define TIMER_9959_INST                                                  (TIMA0)
#define TIMER_9959_INST_IRQHandler                              TIMA0_IRQHandler
#define TIMER_9959_INST_INT_IRQN                                (TIMA0_INT_IRQn)
#define TIMER_9959_INST_LOAD_VALUE                                       (2499U)



/* Defines for UART_0 */
#define UART_0_INST                                                        UART0
#define UART_0_INST_FREQUENCY                                           32000000
#define UART_0_INST_IRQHandler                                  UART0_IRQHandler
#define UART_0_INST_INT_IRQN                                      UART0_INT_IRQn
#define GPIO_UART_0_RX_PORT                                                GPIOA
#define GPIO_UART_0_TX_PORT                                                GPIOA
#define GPIO_UART_0_RX_PIN                                        DL_GPIO_PIN_11
#define GPIO_UART_0_TX_PIN                                        DL_GPIO_PIN_10
#define GPIO_UART_0_IOMUX_RX                                     (IOMUX_PINCM22)
#define GPIO_UART_0_IOMUX_TX                                     (IOMUX_PINCM21)
#define GPIO_UART_0_IOMUX_RX_FUNC                      IOMUX_PINCM22_PF_UART0_RX
#define GPIO_UART_0_IOMUX_TX_FUNC                      IOMUX_PINCM21_PF_UART0_TX
#define UART_0_BAUD_RATE                                                  (9600)
#define UART_0_IBRD_32_MHZ_9600_BAUD                                       (208)
#define UART_0_FBRD_32_MHZ_9600_BAUD                                        (21)





/* Defines for ADC12_0 */
#define ADC12_0_INST                                                        ADC0
#define ADC12_0_INST_IRQHandler                                  ADC0_IRQHandler
#define ADC12_0_INST_INT_IRQN                                    (ADC0_INT_IRQn)
#define ADC12_0_ADCMEM_0                                      DL_ADC12_MEM_IDX_0
#define ADC12_0_ADCMEM_0_REF                     DL_ADC12_REFERENCE_VOLTAGE_VDDA
#define ADC12_0_ADCMEM_0_REF_VOLTAGE_V                                       3.3
#define ADC12_0_INST_SUB_CH                                                  (1)
#define GPIO_ADC12_0_C3_PORT                                               GPIOA
#define GPIO_ADC12_0_C3_PIN                                       DL_GPIO_PIN_24



/* Defines for DMA_CH0 */
#define DMA_CH0_CHAN_ID                                                      (1)
#define ADC12_0_INST_DMA_TRIGGER                      (DMA_ADC0_EVT_GEN_BD_TRIG)
/* Defines for DMA_CH1 */
#define DMA_CH1_CHAN_ID                                                      (0)
#define DAC12_INST_DMA_TRIGGER                          (DMA_DAC0_EVT_BD_1_TRIG)


/* Port definition for Pin Group LED */
#define LED_PORT                                                         (GPIOB)

/* Defines for BULE: GPIOB.22 with pinCMx 50 on package pin 21 */
#define LED_BULE_PIN                                            (DL_GPIO_PIN_22)
#define LED_BULE_IOMUX                                           (IOMUX_PINCM50)
/* Port definition for Pin Group Button */
#define Button_PORT                                                      (GPIOB)

/* Defines for S2: GPIOB.21 with pinCMx 49 on package pin 20 */
// groups represented: ["KeyBoard","Button"]
// pins affected: ["COL1","COL2","COL3","COL4","S2"]
#define GPIO_MULTIPLE_GPIOB_INT_IRQN                            (GPIOB_INT_IRQn)
#define GPIO_MULTIPLE_GPIOB_INT_IIDX            (DL_INTERRUPT_GROUP1_IIDX_GPIOB)
#define Button_S2_IIDX                                      (DL_GPIO_IIDX_DIO21)
#define Button_S2_PIN                                           (DL_GPIO_PIN_21)
#define Button_S2_IOMUX                                          (IOMUX_PINCM49)
/* Port definition for Pin Group KeyBoard */
#define KeyBoard_PORT                                                    (GPIOB)

/* Defines for COL1: GPIOB.12 with pinCMx 29 on package pin 64 */
#define KeyBoard_COL1_IIDX                                  (DL_GPIO_IIDX_DIO12)
#define KeyBoard_COL1_PIN                                       (DL_GPIO_PIN_12)
#define KeyBoard_COL1_IOMUX                                      (IOMUX_PINCM29)
/* Defines for COL2: GPIOB.17 with pinCMx 43 on package pin 14 */
#define KeyBoard_COL2_IIDX                                  (DL_GPIO_IIDX_DIO17)
#define KeyBoard_COL2_PIN                                       (DL_GPIO_PIN_17)
#define KeyBoard_COL2_IOMUX                                      (IOMUX_PINCM43)
/* Defines for COL3: GPIOB.15 with pinCMx 32 on package pin 3 */
#define KeyBoard_COL3_IIDX                                  (DL_GPIO_IIDX_DIO15)
#define KeyBoard_COL3_PIN                                       (DL_GPIO_PIN_15)
#define KeyBoard_COL3_IOMUX                                      (IOMUX_PINCM32)
/* Defines for COL4: GPIOB.8 with pinCMx 25 on package pin 60 */
#define KeyBoard_COL4_IIDX                                   (DL_GPIO_IIDX_DIO8)
#define KeyBoard_COL4_PIN                                        (DL_GPIO_PIN_8)
#define KeyBoard_COL4_IOMUX                                      (IOMUX_PINCM25)
/* Defines for ROW1: GPIOB.7 with pinCMx 24 on package pin 59 */
#define KeyBoard_ROW1_PIN                                        (DL_GPIO_PIN_7)
#define KeyBoard_ROW1_IOMUX                                      (IOMUX_PINCM24)
/* Defines for ROW2: GPIOB.6 with pinCMx 23 on package pin 58 */
#define KeyBoard_ROW2_PIN                                        (DL_GPIO_PIN_6)
#define KeyBoard_ROW2_IOMUX                                      (IOMUX_PINCM23)
/* Defines for ROW3: GPIOB.0 with pinCMx 12 on package pin 47 */
#define KeyBoard_ROW3_PIN                                        (DL_GPIO_PIN_0)
#define KeyBoard_ROW3_IOMUX                                      (IOMUX_PINCM12)
/* Defines for ROW4: GPIOB.16 with pinCMx 33 on package pin 4 */
#define KeyBoard_ROW4_PIN                                       (DL_GPIO_PIN_16)
#define KeyBoard_ROW4_IOMUX                                      (IOMUX_PINCM33)
/* Port definition for Pin Group MYI2C */
#define MYI2C_PORT                                                       (GPIOA)

/* Defines for SCL: GPIOA.0 with pinCMx 1 on package pin 33 */
#define MYI2C_SCL_PIN                                            (DL_GPIO_PIN_0)
#define MYI2C_SCL_IOMUX                                           (IOMUX_PINCM1)
/* Defines for SDA: GPIOA.1 with pinCMx 2 on package pin 34 */
#define MYI2C_SDA_PIN                                            (DL_GPIO_PIN_1)
#define MYI2C_SDA_IOMUX                                           (IOMUX_PINCM2)
/* Defines for EPD_SCL: GPIOA.8 with pinCMx 19 on package pin 54 */
#define EPD_EPD_SCL_PORT                                                 (GPIOA)
#define EPD_EPD_SCL_PIN                                          (DL_GPIO_PIN_8)
#define EPD_EPD_SCL_IOMUX                                        (IOMUX_PINCM19)
/* Defines for EPD_SDA: GPIOA.26 with pinCMx 59 on package pin 30 */
#define EPD_EPD_SDA_PORT                                                 (GPIOA)
#define EPD_EPD_SDA_PIN                                         (DL_GPIO_PIN_26)
#define EPD_EPD_SDA_IOMUX                                        (IOMUX_PINCM59)
/* Defines for EPD_RES: GPIOB.24 with pinCMx 52 on package pin 23 */
#define EPD_EPD_RES_PORT                                                 (GPIOB)
#define EPD_EPD_RES_PIN                                         (DL_GPIO_PIN_24)
#define EPD_EPD_RES_IOMUX                                        (IOMUX_PINCM52)
/* Defines for EPD_DC: GPIOB.9 with pinCMx 26 on package pin 61 */
#define EPD_EPD_DC_PORT                                                  (GPIOB)
#define EPD_EPD_DC_PIN                                           (DL_GPIO_PIN_9)
#define EPD_EPD_DC_IOMUX                                         (IOMUX_PINCM26)
/* Defines for EPD_CS: GPIOB.2 with pinCMx 15 on package pin 50 */
#define EPD_EPD_CS_PORT                                                  (GPIOB)
#define EPD_EPD_CS_PIN                                           (DL_GPIO_PIN_2)
#define EPD_EPD_CS_IOMUX                                         (IOMUX_PINCM15)
/* Defines for EPD_BUSY: GPIOB.3 with pinCMx 16 on package pin 51 */
#define EPD_EPD_BUSY_PORT                                                (GPIOB)
#define EPD_EPD_BUSY_PIN                                         (DL_GPIO_PIN_3)
#define EPD_EPD_BUSY_IOMUX                                       (IOMUX_PINCM16)
/* Defines for SS: GPIOB.1 with pinCMx 13 on package pin 48 */
#define MYSPI_SS_PORT                                                    (GPIOB)
#define MYSPI_SS_PIN                                             (DL_GPIO_PIN_1)
#define MYSPI_SS_IOMUX                                           (IOMUX_PINCM13)
/* Defines for SCK: GPIOA.30 with pinCMx 5 on package pin 37 */
#define MYSPI_SCK_PORT                                                   (GPIOA)
#define MYSPI_SCK_PIN                                           (DL_GPIO_PIN_30)
#define MYSPI_SCK_IOMUX                                           (IOMUX_PINCM5)
/* Defines for MOSI: GPIOB.20 with pinCMx 48 on package pin 19 */
#define MYSPI_MOSI_PORT                                                  (GPIOB)
#define MYSPI_MOSI_PIN                                          (DL_GPIO_PIN_20)
#define MYSPI_MOSI_IOMUX                                         (IOMUX_PINCM48)
/* Defines for MISO: GPIOB.11 with pinCMx 28 on package pin 63 */
#define MYSPI_MISO_PORT                                                  (GPIOB)
#define MYSPI_MISO_PIN                                          (DL_GPIO_PIN_11)
#define MYSPI_MISO_IOMUX                                         (IOMUX_PINCM28)
/* Defines for PS0: GPIOA.2 with pinCMx 7 on package pin 42 */
#define GPIO_9959_PS0_PORT                                               (GPIOA)
#define GPIO_9959_PS0_PIN                                        (DL_GPIO_PIN_2)
#define GPIO_9959_PS0_IOMUX                                       (IOMUX_PINCM7)
/* Defines for PS1: GPIOB.26 with pinCMx 57 on package pin 28 */
#define GPIO_9959_PS1_PORT                                               (GPIOB)
#define GPIO_9959_PS1_PIN                                       (DL_GPIO_PIN_26)
#define GPIO_9959_PS1_IOMUX                                      (IOMUX_PINCM57)
/* Defines for PS2: GPIOB.27 with pinCMx 58 on package pin 29 */
#define GPIO_9959_PS2_PORT                                               (GPIOB)
#define GPIO_9959_PS2_PIN                                       (DL_GPIO_PIN_27)
#define GPIO_9959_PS2_IOMUX                                      (IOMUX_PINCM58)
/* Defines for PS3: GPIOB.25 with pinCMx 56 on package pin 27 */
#define GPIO_9959_PS3_PORT                                               (GPIOB)
#define GPIO_9959_PS3_PIN                                       (DL_GPIO_PIN_25)
#define GPIO_9959_PS3_IOMUX                                      (IOMUX_PINCM56)
/* Defines for SDIO0: GPIOA.16 with pinCMx 38 on package pin 9 */
#define GPIO_9959_SDIO0_PORT                                             (GPIOA)
#define GPIO_9959_SDIO0_PIN                                     (DL_GPIO_PIN_16)
#define GPIO_9959_SDIO0_IOMUX                                    (IOMUX_PINCM38)
/* Defines for SDIO1: GPIOB.23 with pinCMx 51 on package pin 22 */
#define GPIO_9959_SDIO1_PORT                                             (GPIOB)
#define GPIO_9959_SDIO1_PIN                                     (DL_GPIO_PIN_23)
#define GPIO_9959_SDIO1_IOMUX                                    (IOMUX_PINCM51)
/* Defines for SDIO2: GPIOB.19 with pinCMx 45 on package pin 16 */
#define GPIO_9959_SDIO2_PORT                                             (GPIOB)
#define GPIO_9959_SDIO2_PIN                                     (DL_GPIO_PIN_19)
#define GPIO_9959_SDIO2_IOMUX                                    (IOMUX_PINCM45)
/* Defines for SDIO3: GPIOB.18 with pinCMx 44 on package pin 15 */
#define GPIO_9959_SDIO3_PORT                                             (GPIOB)
#define GPIO_9959_SDIO3_PIN                                     (DL_GPIO_PIN_18)
#define GPIO_9959_SDIO3_IOMUX                                    (IOMUX_PINCM44)
/* Defines for SCLK: GPIOB.14 with pinCMx 31 on package pin 2 */
#define GPIO_9959_SCLK_PORT                                              (GPIOB)
#define GPIO_9959_SCLK_PIN                                      (DL_GPIO_PIN_14)
#define GPIO_9959_SCLK_IOMUX                                     (IOMUX_PINCM31)
/* Defines for CS: GPIOB.13 with pinCMx 30 on package pin 1 */
#define GPIO_9959_CS_PORT                                                (GPIOB)
#define GPIO_9959_CS_PIN                                        (DL_GPIO_PIN_13)
#define GPIO_9959_CS_IOMUX                                       (IOMUX_PINCM30)
/* Defines for UPDATA: GPIOA.12 with pinCMx 34 on package pin 5 */
#define GPIO_9959_UPDATA_PORT                                            (GPIOA)
#define GPIO_9959_UPDATA_PIN                                    (DL_GPIO_PIN_12)
#define GPIO_9959_UPDATA_IOMUX                                   (IOMUX_PINCM34)
/* Defines for RESET: GPIOA.28 with pinCMx 3 on package pin 35 */
#define GPIO_9959_RESET_PORT                                             (GPIOA)
#define GPIO_9959_RESET_PIN                                     (DL_GPIO_PIN_28)
#define GPIO_9959_RESET_IOMUX                                     (IOMUX_PINCM3)
/* Defines for PWR: GPIOA.31 with pinCMx 6 on package pin 39 */
#define GPIO_9959_PWR_PORT                                               (GPIOA)
#define GPIO_9959_PWR_PIN                                       (DL_GPIO_PIN_31)
#define GPIO_9959_PWR_IOMUX                                       (IOMUX_PINCM6)



/* Defines for DAC12 */
#define DAC12_IRQHandler                                         DAC0_IRQHandler
#define DAC12_INT_IRQN                                           (DAC0_INT_IRQn)
#define GPIO_DAC12_OUT_PORT                                                GPIOA
#define GPIO_DAC12_OUT_PIN                                        DL_GPIO_PIN_15
#define GPIO_DAC12_IOMUX_OUT                                     (IOMUX_PINCM37)
#define GPIO_DAC12_IOMUX_OUT_FUNC                   IOMUX_PINCM37_PF_UNCONNECTED

/* clang-format on */

void SYSCFG_DL_init(void);
void SYSCFG_DL_initPower(void);
void SYSCFG_DL_GPIO_init(void);
void SYSCFG_DL_SYSCTL_init(void);
void SYSCFG_DL_PWM_0_init(void);
void SYSCFG_DL_TIMER_0_init(void);
void SYSCFG_DL_TIMER_9959_init(void);
void SYSCFG_DL_UART_0_init(void);
void SYSCFG_DL_ADC12_0_init(void);
void SYSCFG_DL_DMA_init(void);

void SYSCFG_DL_DAC12_init(void);

bool SYSCFG_DL_saveConfiguration(void);
bool SYSCFG_DL_restoreConfiguration(void);

#ifdef __cplusplus
}
#endif

#endif /* ti_msp_dl_config_h */

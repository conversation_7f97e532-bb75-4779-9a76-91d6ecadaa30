################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
App/EPD/%.o: ../App/EPD/%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"C:/ti/ccstheia140/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507" -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507/App" -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507/App/EPD" -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507/OLED" -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507/Debug" -I"C:/ti/mspm0_sdk_2_00_01_00/source/third_party/CMSIS/Core/Include" -I"C:/ti/mspm0_sdk_2_00_01_00/source" -I"C:/ti/mspm0_sdk_2_00_01_00/source/third_party/CMSIS/DSP/lib/ticlang/m0p/arm_cortexM0l_math.a" -I"C:/ti/mspm0_sdk_2_00_01_00/source/third_party/CMSIS/DSP/Include" -gdwarf-3 -MMD -MP -MF"App/EPD/$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '



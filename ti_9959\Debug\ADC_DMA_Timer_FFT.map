******************************************************************************
            TI ARM Clang Linker PC v3.2.2                      
******************************************************************************
>> Linked Tue Aug 20 19:30:29 2024

OUTPUT FILE NAME:   <ADC_DMA_Timer_FFT.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000062a5


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000076e0  00018920  R  X
  SRAM                  20200000   00008000  000018e7  00006719  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000076e0   000076e0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00006b60   00006b60    r-x .text
  00006c20    00006c20    00000a90   00000a90    r-- .rodata
  000076b0    000076b0    00000030   00000030    r-- .cinit
20200000    20200000    000016e7   00000000    rw-
  20200000    20200000    00000e18   00000000    rw- .bss
  20200e18    20200e18    000008cf   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00006b60     
                  000000c0    00000a70     libc.a : e_pow.c.obj (.text.pow)
                  00000b30    00000a00            : _printfi.c.obj (.text:__TI_printfi)
                  00001530    00000658            : k_rem_pio2.c.obj (.text.__kernel_rem_pio2)
                  00001b88    00000458            : s_sin.c.obj (.text.sin)
                  00001fe0    00000440            : s_cos.c.obj (.text.cos)
                  00002420    00000384     BSP_4x4KEY.o (.text.Read4X4KEY)
                  000027a4    000002f8     libc.a : s_atan.c.obj (.text.atan)
                  00002a9c    00000220            : _printfi.c.obj (.text._pconv_a)
                  00002cbc    000001de     User_FFT.o (.text.fft)
                  00002e9a    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002e9c    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00003078    000001ac     User_FFT.o (.text.User_GetSpectrum)
                  00003224    00000198     libc.a : e_atan2.c.obj (.text.atan2)
                  000033bc    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000354e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00003550    00000190     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000036e0    00000184     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00003864    00000168            : k_sin.c.obj (.text.__kernel_sin)
                  000039cc    00000158     EPD_GUI.o (.text.EPD_ShowChar)
                  00003b24    00000150     libc.a : k_cos.c.obj (.text.__kernel_cos)
                  00003c74    00000144            : s_floor.c.obj (.text.floor)
                  00003db8    0000013c            : _printfi.c.obj (.text.fcvt)
                  00003ef4    00000120            : _printfi.c.obj (.text._pconv_e)
                  00004014    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00004120    000000f4     EPD_GUI.o (.text.Paint_SetPixel)
                  00004214    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000042fc    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  000043e0    000000d8     main.o (.text.TaskA_Handler)
                  000044b8    000000d8     User_FFT.o (.text.Wn_i)
                  00004590    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  00004668    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00004740    000000c4     driverlib.a : dl_timer.o (.text.DL_Timer_initPWMMode)
                  00004804    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  000048a6    00000002     --HOLE-- [fill = 0]
                  000048a8    0000009c     User_ADC.o (.text.Get_AC_Vol)
                  00004944    00000094     EPD_GUI.o (.text.Paint_NewImage)
                  000049d8    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00004a68    0000008c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00004af4    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00004b80    00000088     driverlib.a : dl_dac12.o (.text.DL_DAC12_init)
                  00004c08    00000084     SPI_Init.o (.text.EPD_WR_Bus)
                  00004c8c    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00004d0e    00000002     --HOLE-- [fill = 0]
                  00004d10    00000080     main.o (.text.main)
                  00004d90    0000007c     EPD_GUI.o (.text.EPD_ClearAll)
                  00004e0c    00000078     EPD.o (.text.EPD_Display)
                  00004e84    00000078     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00004efc    00000074     EPD.o (.text.EPD_Display_Clear)
                  00004f70    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00004fe4    00000070     main.o (.text.Show_menu)
                  00005054    00000070     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  000050c4    0000006c     EPD_GUI.o (.text.Paint_Clear)
                  00005130    0000006c     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  0000519c    00000068     EPD.o (.text.EPD_FastMode2Init)
                  00005204    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  0000526a    00000002     --HOLE-- [fill = 0]
                  0000526c    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  000052d0    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  00005332    00000002     --HOLE-- [fill = 0]
                  00005334    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00005396    00000002     --HOLE-- [fill = 0]
                  00005398    00000060     main.o (.text.TaskB_Handler)
                  000053f8    00000060     main.o (.text.TaskC_Handler)
                  00005458    00000060     main.o (.text.TaskD_Handler)
                  000054b8    0000005e     EPD_GUI.o (.text.EPD_ShowString)
                  00005516    0000005e     User_FFT.o (.text.c_mul)
                  00005574    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  000055d0    00000058            : _ltoa.c.obj (.text.__TI_ltoa)
                  00005628    00000058            : _printfi.c.obj (.text._pconv_f)
                  00005680    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  000056d6    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00005728    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00005774    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  000057c0    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  0000580c    0000004c     main.o (.text.Init_All)
                  00005858    0000004c     BSP_Spwm.o (.text.TIMG8_IRQHandler)
                  000058a4    0000004c     User_ADC.o (.text.User_ADC_Init)
                  000058f0    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  0000593a    00000002     --HOLE-- [fill = 0]
                  0000593c    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00005986    00000002     --HOLE-- [fill = 0]
                  00005988    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000059d0    00000048     User_ADC.o (.text.Set_Fs)
                  00005a18    00000046     EPD_GUI.o (.text.EPD_ShowNum)
                  00005a5e    00000002     --HOLE-- [fill = 0]
                  00005a60    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00005aa4    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00005ae4    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00005b24    00000040     User_DAC.o (.text.User_DAC_Init)
                  00005b64    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00005ba4    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00005be4    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00005c24    0000003c     EPD.o (.text.EPD_Clear_R26H)
                  00005c60    0000003c     EPD.o (.text.EPD_HW_RESET)
                  00005c9c    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00005cd8    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00005d14    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00005d50    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00005d8a    00000002     --HOLE-- [fill = 0]
                  00005d8c    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00005dc6    00000002     --HOLE-- [fill = 0]
                  00005dc8    00000038     ti_msp_dl_config.o (.text.DL_Timer_setPublisherChanID)
                  00005e00    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00005e38    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00005e70    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00005ea4    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00005ed8    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00005f0c    00000030     User_ADC.o (.text.DL_DMA_setTransferSize)
                  00005f3c    00000030     User_DAC.o (.text.DL_DMA_setTransferSize)
                  00005f6c    00000030     ti_msp_dl_config.o (.text.DL_DMA_setTransferSize)
                  00005f9c    00000030     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutputFeatures)
                  00005fcc    00000030     SPI_Init.o (.text.EPD_WR_DATA8)
                  00005ffc    00000030     SPI_Init.o (.text.EPD_WR_REG)
                  0000602c    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  0000605c    0000002c     User_ADC.o (.text.ADC0_IRQHandler)
                  00006088    0000002c     ti_msp_dl_config.o (.text.DL_ADC12_setDMASamplesCnt)
                  000060b4    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_DAC12_init)
                  000060e0    0000002c     User_ADC.o (.text.__NVIC_EnableIRQ)
                  0000610c    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00006138    0000002a     User_FFT.o (.text.c_plus)
                  00006162    0000002a     User_FFT.o (.text.c_sub)
                  0000618c    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000061b4    00000028     User_ADC.o (.text.DL_DMA_setDestAddr)
                  000061dc    00000028     User_DAC.o (.text.DL_DMA_setDestAddr)
                  00006204    00000028     User_ADC.o (.text.DL_DMA_setSrcAddr)
                  0000622c    00000028     User_DAC.o (.text.DL_DMA_setSrcAddr)
                  00006254    00000028     ti_msp_dl_config.o (.text.DL_Timer_enableEvent)
                  0000627c    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH0_init)
                  000062a4    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000062cc    00000026     User_ADC.o (.text.DL_DMA_enableChannel)
                  000062f2    00000026     User_DAC.o (.text.DL_DMA_enableChannel)
                  00006318    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  0000633c    00000022     Delay.o (.text.delay_ms)
                  0000635e    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00006380    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  000063a0    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setMFPCLKSource)
                  000063c0    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  000063de    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  000063fc    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  0000641a    00000002     --HOLE-- [fill = 0]
                  0000641c    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_clearInterruptStatus)
                  00006438    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableDMA)
                  00006454    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableDMATrigger)
                  00006470    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableInterrupt)
                  0000648c    0000001c     ti_msp_dl_config.o (.text.DL_DAC12_enableInterrupt)
                  000064a8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000064c4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000064e0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000064fc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00006518    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setMCLKDivider)
                  00006534    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00006550    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  0000656c    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00006588    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000065a4    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000065c0    0000001c     EPD.o (.text.EPD_READBUSY)
                  000065dc    0000001a     ti_msp_dl_config.o (.text.DL_ADC12_setSubscriberChanID)
                  000065f6    0000001a     EPD.o (.text.EPD_FastUpdate)
                  00006610    0000001a     EPD.o (.text.EPD_PartUpdate)
                  0000662a    00000002     --HOLE-- [fill = 0]
                  0000662c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00006644    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  0000665c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00006674    00000018     ti_msp_dl_config.o (.text.DL_DAC12_enablePower)
                  0000668c    00000018     ti_msp_dl_config.o (.text.DL_DAC12_reset)
                  000066a4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000066bc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000066d4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000066ec    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00006704    00000018     BSP_4x4KEY.o (.text.DL_GPIO_setPins)
                  0000671c    00000018     EPD.o (.text.DL_GPIO_setPins)
                  00006734    00000018     SPI_Init.o (.text.DL_GPIO_setPins)
                  0000674c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00006764    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  0000677c    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00006794    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000067ac    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000067c4    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000067dc    00000018     User_ADC.o (.text.DL_Timer_setLoadValue)
                  000067f4    00000018     User_ADC.o (.text.DL_Timer_startCounter)
                  0000680c    00000018     User_ADC.o (.text.DL_Timer_stopCounter)
                  00006824    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  0000683c    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00006854    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH1_init)
                  0000686c    00000018     libc.a : sprintf.c.obj (.text._outs)
                  00006884    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  0000689a    00000016     ti_msp_dl_config.o (.text.DL_DAC12_enable)
                  000068b0    00000016     BSP_4x4KEY.o (.text.DL_GPIO_readPins)
                  000068c6    00000016     EPD.o (.text.DL_GPIO_readPins)
                  000068dc    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  000068f2    00000016     libc.a : k_rem_pio2.c.obj (.text.OUTLINED_FUNCTION_0)
                  00006908    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00006918    00000014     BSP_4x4KEY.o (.text.DL_GPIO_clearPins)
                  0000692c    00000014     EPD.o (.text.DL_GPIO_clearPins)
                  00006940    00000014     SPI_Init.o (.text.DL_GPIO_clearPins)
                  00006954    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00006968    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  0000697c    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00006990    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000069a4    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000069b8    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000069cc    00000012     User_ADC.o (.text.DL_ADC12_getPendingInterrupt)
                  000069de    00000012     BSP_Spwm.o (.text.DL_Timer_getPendingInterrupt)
                  000069f0    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00006a02    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00006a14    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00006a26    00000002     --HOLE-- [fill = 0]
                  00006a28    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00006a38    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_enableMFPCLK)
                  00006a48    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00006a58    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00006a68    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  00006a78    0000000e            : s_cos.c.obj (.text.OUTLINED_FUNCTION_0)
                  00006a86    00000002     --HOLE-- [fill = 0]
                  00006a88    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00006a98    0000000e     libc.a : s_sin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00006aa6    0000000e            : k_rem_pio2.c.obj (.text.OUTLINED_FUNCTION_2)
                  00006ab4    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00006ac2    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00006ad0    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00006ade    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00006aea    00000002     --HOLE-- [fill = 0]
                  00006aec    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00006af8    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00006b02    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00006b0c    0000000a            : e_pow.c.obj (.text.OUTLINED_FUNCTION_0)
                  00006b16    00000002     --HOLE-- [fill = 0]
                  00006b18    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00006b28    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00006b32    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00006b3c    0000000a            : e_pow.c.obj (.text.OUTLINED_FUNCTION_1)
                  00006b46    0000000a            : k_rem_pio2.c.obj (.text.OUTLINED_FUNCTION_1)
                  00006b50    0000000a            : s_cos.c.obj (.text.OUTLINED_FUNCTION_6)
                  00006b5a    00000002     --HOLE-- [fill = 0]
                  00006b5c    00000010            : k_sin.c.obj (.tramp.__kernel_sin.1)
                  00006b6c    0000000a            : sprintf.c.obj (.text._outc)
                  00006b76    00000008            : e_pow.c.obj (.text.OUTLINED_FUNCTION_2)
                  00006b7e    00000008            : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  00006b86    00000002     --HOLE-- [fill = 0]
                  00006b88    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00006b90    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00006b98    00000006     libc.a : k_cos.c.obj (.text.OUTLINED_FUNCTION_0)
                  00006b9e    00000006            : k_sin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00006ba4    00000006            : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00006baa    00000006            : s_cos.c.obj (.text.OUTLINED_FUNCTION_1)
                  00006bb0    00000006            : s_sin.c.obj (.text.OUTLINED_FUNCTION_1)
                  00006bb6    00000006            : e_pow.c.obj (.text.OUTLINED_FUNCTION_3)
                  00006bbc    00000006            : s_cos.c.obj (.text.OUTLINED_FUNCTION_3)
                  00006bc2    00000006            : s_sin.c.obj (.text.OUTLINED_FUNCTION_3)
                  00006bc8    00000006            : e_pow.c.obj (.text.OUTLINED_FUNCTION_5)
                  00006bce    00000006            : s_cos.c.obj (.text.OUTLINED_FUNCTION_5)
                  00006bd4    00000006            : s_sin.c.obj (.text.OUTLINED_FUNCTION_5)
                  00006bda    00000006            : e_pow.c.obj (.text.OUTLINED_FUNCTION_6)
                  00006be0    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00006be4    00000004     libc.a : s_cos.c.obj (.text.OUTLINED_FUNCTION_2)
                  00006be8    00000004            : s_sin.c.obj (.text.OUTLINED_FUNCTION_2)
                  00006bec    00000004            : e_pow.c.obj (.text.OUTLINED_FUNCTION_4)
                  00006bf0    00000004            : s_cos.c.obj (.text.OUTLINED_FUNCTION_4)
                  00006bf4    00000004            : s_sin.c.obj (.text.OUTLINED_FUNCTION_4)
                  00006bf8    00000004            : e_pow.c.obj (.text.OUTLINED_FUNCTION_7)
                  00006bfc    00000004            : e_pow.c.obj (.text.OUTLINED_FUNCTION_8)
                  00006c00    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00006c04    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00006c14    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00006c18    00000004            : exit.c.obj (.text:abort)
                  00006c1c    00000004     --HOLE-- [fill = 0]

.cinit     0    000076b0    00000030     
                  000076b0    0000000c     (__TI_handler_table)
                  000076bc    0000000b     (.cinit..data.load) [load image, compression = lzss]
                  000076c7    00000001     --HOLE-- [fill = 0]
                  000076c8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000076d0    00000010     (__TI_cinit_table)

.rodata    0    00006c20    00000a90     
                  00006c20    000005f0     EPD_GUI.o (.rodata.asc2_1608)
                  00007210    00000108     libc.a : k_rem_pio2.c.obj (.rodata.ipio2)
                  00007318    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00007320    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00007421    00000001     --HOLE-- [fill = 0]
                  00007422    00000080     User_DAC.o (.rodata.DAC_Buff)
                  000074a2    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  000074a5    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  000074a8    00000040     libc.a : k_rem_pio2.c.obj (.rodata.PIo2)
                  000074e8    00000040            : s_atan.c.obj (.rodata.cst32)
                  00007528    00000030            : e_pow.c.obj (.rodata.cst16)
                  00007558    00000020     ti_msp_dl_config.o (.rodata.gDAC12Config)
                  00007578    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH0Config)
                  00007590    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH1Config)
                  000075a8    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  000075bc    00000014     EPD_GUI.o (.rodata.str1.160496125614851016371)
                  000075d0    00000011     libc.a : _printfi.c.obj (.rodata.str1.11645776875810915891)
                  000075e1    00000011     main.o (.rodata.str1.120848099768473620991)
                  000075f2    00000011     main.o (.rodata.str1.122786433597999586301)
                  00007603    00000011     libc.a : _printfi.c.obj (.rodata.str1.44690500295887128011)
                  00007614    00000011     main.o (.rodata.str1.57136986666369238191)
                  00007625    00000011     main.o (.rodata.str1.93435692222562013921)
                  00007636    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00007638    00000010     libc.a : k_rem_pio2.c.obj (.rodata.cst16)
                  00007648    0000000f     main.o (.rodata.str1.18934544870302961421)
                  00007657    0000000d     main.o (.rodata.str1.179480043780824248221)
                  00007664    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  0000766e    0000000a     main.o (.rodata.str1.182859457380636363961)
                  00007678    0000000a     main.o (.rodata.str1.74852076718367031741)
                  00007682    0000000a     main.o (.rodata.str1.80750631935809179731)
                  0000768c    0000000a     main.o (.rodata.str1.89219620533961422581)
                  00007696    00000009     main.o (.rodata.str1.123866747220708346941)
                  0000769f    00000001     --HOLE-- [fill = 0]
                  000076a0    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  000076a8    00000008     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000e18     UNINITIALIZED
                  20200000    00000b48     (.common:ImageBW)
                  20200b48    00000200     (.common:gADCSamples)
                  20200d48    000000bc     (.common:gTIMER_0Backup)
                  20200e04    00000014     (.common:Paint)

.data      0    20200e18    000008cf     UNINITIALIZED
                  20200e18    00000800     User_FFT.o (.data.FFT_Data)
                  20201618    000000c8     BSP_Spwm.o (.data.Pwm_Data)
                  202016e0    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  202016e4    00000001     BSP_Spwm.o (.data.Spwm_cnt)
                  202016e5    00000001     User_ADC.o (.data.adc_flag)
                  202016e6    00000001     main.o (.data.key_val)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             2770    134       188    
    +--+------------------------------+-------+---------+---------+
       Total:                         2770    134       188    
                                                               
    .\App\
       User_FFT.o                     1300    0         2048   
       User_ADC.o                     648     0         513    
       User_DAC.o                     230     128       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2178    128       2561   
                                                               
    .\BSP\
       BSP_4x4KEY.o                   966     0         0      
       BSP_Spwm.o                     94      0         201    
    +--+------------------------------+-------+---------+---------+
       Total:                         1060    0         201    
                                                               
    .\EPD\
       EPD_GUI.o                      1132    1540      20     
       EPD.o                          606     0         0      
       SPI_Init.o                     272     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2010    1540      20     
                                                               
    .\System\
       Delay.o                        34      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         34      0         0      
                                                               
    .\User\
       main.o                         820     145       2889   
       startup_mspm0g350x_ticlang.o   8       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         828     337       2889   
                                                               
    C:/ti/mspm0_sdk_2_00_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     524     0         0      
       dl_dac12.o                     136     0         0      
       dl_uart.o                      90      0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         900     0         0      
                                                               
    C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4558    34        0      
       e_pow.c.obj                    2730    48        0      
       k_rem_pio2.c.obj               1670    344       0      
       s_sin.c.obj                    1152    0         0      
       s_cos.c.obj                    1138    0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  408     0         0      
       e_sqrt.c.obj                   388     0         0      
       k_sin.c.obj                    382     0         0      
       k_cos.c.obj                    342     0         0      
       s_floor.c.obj                  324     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     120     0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         14794   747       4      
                                                               
    C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang/15.0.7/lib/armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2874    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       47        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   27448   2933      6375   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000076d0 records: 2, size/record: 8, table size: 16
	.data: load addr=000076bc, load size=0000000b bytes, run addr=20200e18, run size=000008cf bytes, compression=lzss
	.bss: load addr=000076c8, load size=00000008 bytes, run addr=20200000, run size=00000e18 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000076b0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   000033c7     00006908     00006906   libc.a : k_rem_pio2.c.obj (.text.OUTLINED_FUNCTION_0)
                             00006b4e          : k_rem_pio2.c.obj (.text.OUTLINED_FUNCTION_1)
                             00006ba8          : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             00006bae          : s_cos.c.obj (.text.OUTLINED_FUNCTION_1)
                             00006bb4          : s_sin.c.obj (.text.OUTLINED_FUNCTION_1)
                             00006bcc          : e_pow.c.obj (.text.OUTLINED_FUNCTION_5)
                             00006be6          : s_cos.c.obj (.text.OUTLINED_FUNCTION_2)
                             00006bea          : s_sin.c.obj (.text.OUTLINED_FUNCTION_2)
                             00006bee          : e_pow.c.obj (.text.OUTLINED_FUNCTION_4)
                             00006bfe          : e_pow.c.obj (.text.OUTLINED_FUNCTION_8)
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000033bd     00006a88     00006a84   libc.a : s_cos.c.obj (.text.OUTLINED_FUNCTION_0)
                             00006aa4          : s_sin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00006b0a          : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                             00006b7c          : e_pow.c.obj (.text.OUTLINED_FUNCTION_2)
                             00006bd2          : s_cos.c.obj (.text.OUTLINED_FUNCTION_5)
                             00006bd8          : s_sin.c.obj (.text.OUTLINED_FUNCTION_5)
                             00006bf2          : s_cos.c.obj (.text.OUTLINED_FUNCTION_4)
                             00006bf6          : s_sin.c.obj (.text.OUTLINED_FUNCTION_4)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   000042fd     00006b18     00006b14   libc.a : e_pow.c.obj (.text.OUTLINED_FUNCTION_0)
                             00006b30          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00006b44          : e_pow.c.obj (.text.OUTLINED_FUNCTION_1)
                             00006b84          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00006b9c          : k_cos.c.obj (.text.OUTLINED_FUNCTION_0)
                             00006ba2          : k_sin.c.obj (.text.OUTLINED_FUNCTION_0)
                             00006bba          : e_pow.c.obj (.text.OUTLINED_FUNCTION_3)
                             00006bc0          : s_cos.c.obj (.text.OUTLINED_FUNCTION_3)
                             00006bc6          : s_sin.c.obj (.text.OUTLINED_FUNCTION_3)
                             00006bde          : e_pow.c.obj (.text.OUTLINED_FUNCTION_6)
                             00006bfa          : e_pow.c.obj (.text.OUTLINED_FUNCTION_7)
__kernel_sin              $Tramp$TT$L$PI$$__kernel_sin
   00003865     00006b5c     00006b58   libc.a : s_cos.c.obj (.text.OUTLINED_FUNCTION_6)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   000062a5     00006c04     00006c00   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[31 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
0000605d  ADC0_IRQHandler                 
00006be1  ADC1_IRQHandler                 
00006be1  AES_IRQHandler                  
00006c18  C$$EXIT                         
00006be1  CANFD0_IRQHandler               
00006be1  DAC0_IRQHandler                 
00007422  DAC_Buff                        
00005aa5  DL_ADC12_setClockConfig         
00006af9  DL_Common_delayCycles           
00004b81  DL_DAC12_init                   
00005775  DL_DMA_initChannel              
00004741  DL_Timer_initPWMMode            
00004215  DL_Timer_initTimerMode          
00006589  DL_Timer_setCaptCompUpdateMethod
000067c5  DL_Timer_setCaptureCompareOutCtl
00006a49  DL_Timer_setCaptureCompareValue 
000065a5  DL_Timer_setClockConfig         
00005989  DL_UART_init                    
000069f1  DL_UART_setClockConfig          
00006be1  DMA_IRQHandler                  
00006be1  Default_Handler                 
00004d91  EPD_ClearAll                    
00005c25  EPD_Clear_R26H                  
00004e0d  EPD_Display                     
00004efd  EPD_Display_Clear               
0000519d  EPD_FastMode2Init               
000065f7  EPD_FastUpdate                  
00005c61  EPD_HW_RESET                    
00006611  EPD_PartUpdate                  
000065c1  EPD_READBUSY                    
000039cd  EPD_ShowChar                    
00005a19  EPD_ShowNum                     
000054b9  EPD_ShowString                  
00004c09  EPD_WR_Bus                      
00005fcd  EPD_WR_DATA8                    
00005ffd  EPD_WR_REG                      
20200e18  FFT_Data                        
00006be1  GROUP0_IRQHandler               
00006be1  GROUP1_IRQHandler               
000048a9  Get_AC_Vol                      
00006be1  HardFault_Handler               
00006be1  I2C0_IRQHandler                 
00006be1  I2C1_IRQHandler                 
20200000  ImageBW                         
0000580d  Init_All                        
00006be1  NMI_Handler                     
20200e04  Paint                           
000050c5  Paint_Clear                     
00004945  Paint_NewImage                  
00004121  Paint_SetPixel                  
00006be1  PendSV_Handler                  
20201618  Pwm_Data                        
00006be1  RTC_IRQHandler                  
00002421  Read4X4KEY                      
00006c01  Reset_Handler                   
00006be1  SPI0_IRQHandler                 
00006be1  SPI1_IRQHandler                 
00006be1  SVC_Handler                     
000049d9  SYSCFG_DL_ADC12_0_init          
000060b5  SYSCFG_DL_DAC12_init            
0000627d  SYSCFG_DL_DMA_CH0_init          
00006855  SYSCFG_DL_DMA_CH1_init          
00006adf  SYSCFG_DL_DMA_init              
00003551  SYSCFG_DL_GPIO_init             
0000526d  SYSCFG_DL_PWM_0_init            
00005ea5  SYSCFG_DL_SYSCTL_init           
00005a61  SYSCFG_DL_TIMER_0_init          
00005ae5  SYSCFG_DL_UART_0_init           
00005ed9  SYSCFG_DL_init                  
00004a69  SYSCFG_DL_initPower             
000059d1  Set_Fs                          
00004fe5  Show_menu                       
202016e4  Spwm_cnt                        
00006be1  SysTick_Handler                 
00006be1  TIMA0_IRQHandler                
00006be1  TIMA1_IRQHandler                
00006be1  TIMG0_IRQHandler                
00006be1  TIMG12_IRQHandler               
00006be1  TIMG6_IRQHandler                
00006be1  TIMG7_IRQHandler                
00005859  TIMG8_IRQHandler                
00006a03  TI_memcpy_small                 
00006ad1  TI_memset_small                 
00006be1  UART0_IRQHandler                
00006be1  UART1_IRQHandler                
00006be1  UART2_IRQHandler                
00006be1  UART3_IRQHandler                
000058a5  User_ADC_Init                   
00005b25  User_DAC_Init                   
00003079  User_GetSpectrum                
20208000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
000076d0  __TI_CINIT_Base                 
000076e0  __TI_CINIT_Limit                
000076e0  __TI_CINIT_Warm                 
000076b0  __TI_Handler_Table_Base         
000076bc  __TI_Handler_Table_Limit        
00005d15  __TI_auto_init_nobinit_nopinit  
00004e85  __TI_decompress_lzss            
00006a15  __TI_decompress_none            
000055d1  __TI_ltoa                       
ffffffff  __TI_pprof_out_hndl             
00000b31  __TI_printfi                    
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00006a69  __TI_zero_init                  
000033c7  __adddf3                        
00004673  __addsf3                        
00007320  __aeabi_ctype_table_            
00007320  __aeabi_ctype_table_C           
00004f71  __aeabi_d2f                     
0000593d  __aeabi_d2iz                    
000033c7  __aeabi_dadd                    
000052d1  __aeabi_dcmpeq                  
0000530d  __aeabi_dcmpge                  
00005321  __aeabi_dcmpgt                  
000052f9  __aeabi_dcmple                  
000052e5  __aeabi_dcmplt                  
00004015  __aeabi_ddiv                    
000042fd  __aeabi_dmul                    
000033bd  __aeabi_dsub                    
202016e0  __aeabi_errno                   
00006b89  __aeabi_errno_addr              
00005ba5  __aeabi_f2d                     
00005e01  __aeabi_f2iz                    
00004673  __aeabi_fadd                    
00005335  __aeabi_fcmpeq                  
00005371  __aeabi_fcmpge                  
00005385  __aeabi_fcmpgt                  
0000535d  __aeabi_fcmple                  
00005349  __aeabi_fcmplt                  
00004c8d  __aeabi_fdiv                    
00004af5  __aeabi_fmul                    
00004669  __aeabi_fsub                    
0000610d  __aeabi_i2d                     
00005c9d  __aeabi_i2f                     
00005681  __aeabi_idiv                    
00002e9b  __aeabi_idiv0                   
00005681  __aeabi_idivmod                 
0000354f  __aeabi_ldiv0                   
000063fd  __aeabi_llsl                    
00006319  __aeabi_lmul                    
00006aed  __aeabi_memclr                  
00006aed  __aeabi_memclr4                 
00006aed  __aeabi_memclr8                 
00006b91  __aeabi_memcpy                  
00006b91  __aeabi_memcpy4                 
00006b91  __aeabi_memcpy8                 
00006ab5  __aeabi_memset                  
00006ab5  __aeabi_memset4                 
00006ab5  __aeabi_memset8                 
00005b65  __aeabi_uidiv                   
00005b65  __aeabi_uidivmod                
000069a5  __aeabi_uldivmod                
000063fd  __ashldi3                       
ffffffff  __binit__                       
00005131  __cmpdf2                        
00005d51  __cmpsf2                        
00004015  __divdf3                        
00004c8d  __divsf3                        
00005131  __eqdf2                         
00005d51  __eqsf2                         
00005ba5  __extendsfdf2                   
0000593d  __fixdfsi                       
00005e01  __fixsfsi                       
0000610d  __floatsidf                     
00005c9d  __floatsisf                     
00005055  __gedf2                         
00005cd9  __gesf2                         
00005055  __gtdf2                         
00005cd9  __gtsf2                         
00003b25  __kernel_cos                    
00001531  __kernel_rem_pio2               
00003865  __kernel_sin                    
00005131  __ledf2                         
00005d51  __lesf2                         
00005131  __ltdf2                         
00005d51  __ltsf2                         
UNDEFED   __mpu_init                      
000042fd  __muldf3                        
00006319  __muldi3                        
00005d8d  __muldsi3                       
00004af5  __mulsf3                        
00005131  __nedf2                         
00005d51  __nesf2                         
20207e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
000033bd  __subdf3                        
00004669  __subsf3                        
00004f71  __truncdfsf2                    
00004805  __udivmoddi4                    
000062a5  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
00006c15  _system_pre_init                
00006c19  abort                           
202016e5  adc_flag                        
00006c20  asc2_1608                       
000027a5  atan                            
00003225  atan2                           
00003225  atan2l                          
000027a5  atanl                           
00005be5  atoi                            
ffffffff  binit                           
00001fe1  cos                             
00001fe1  cosl                            
0000633d  delay_ms                        
00002cbd  fft                             
00003c75  floor                           
00003c75  floorl                          
00005575  frexp                           
00005575  frexpl                          
20200b48  gADCSamples                     
20200d48  gTIMER_0Backup                  
00000000  interruptVectors                
202016e6  key_val                         
00004591  ldexp                           
00004591  ldexpl                          
00004d11  main                            
0000635f  memccpy                         
000000c1  pow                             
000000c1  powl                            
00004591  scalbn                          
00004591  scalbnl                         
00001b89  sin                             
00001b89  sinl                            
00005e39  sprintf                         
000036e1  sqrt                            
000036e1  sqrtl                           
00006a59  wcslen                          


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  pow                             
000000c1  powl                            
00000200  __STACK_SIZE                    
00000b31  __TI_printfi                    
00001531  __kernel_rem_pio2               
00001b89  sin                             
00001b89  sinl                            
00001fe1  cos                             
00001fe1  cosl                            
00002421  Read4X4KEY                      
000027a5  atan                            
000027a5  atanl                           
00002cbd  fft                             
00002e9b  __aeabi_idiv0                   
00003079  User_GetSpectrum                
00003225  atan2                           
00003225  atan2l                          
000033bd  __aeabi_dsub                    
000033bd  __subdf3                        
000033c7  __adddf3                        
000033c7  __aeabi_dadd                    
0000354f  __aeabi_ldiv0                   
00003551  SYSCFG_DL_GPIO_init             
000036e1  sqrt                            
000036e1  sqrtl                           
00003865  __kernel_sin                    
000039cd  EPD_ShowChar                    
00003b25  __kernel_cos                    
00003c75  floor                           
00003c75  floorl                          
00004015  __aeabi_ddiv                    
00004015  __divdf3                        
00004121  Paint_SetPixel                  
00004215  DL_Timer_initTimerMode          
000042fd  __aeabi_dmul                    
000042fd  __muldf3                        
00004591  ldexp                           
00004591  ldexpl                          
00004591  scalbn                          
00004591  scalbnl                         
00004669  __aeabi_fsub                    
00004669  __subsf3                        
00004673  __addsf3                        
00004673  __aeabi_fadd                    
00004741  DL_Timer_initPWMMode            
00004805  __udivmoddi4                    
000048a9  Get_AC_Vol                      
00004945  Paint_NewImage                  
000049d9  SYSCFG_DL_ADC12_0_init          
00004a69  SYSCFG_DL_initPower             
00004af5  __aeabi_fmul                    
00004af5  __mulsf3                        
00004b81  DL_DAC12_init                   
00004c09  EPD_WR_Bus                      
00004c8d  __aeabi_fdiv                    
00004c8d  __divsf3                        
00004d11  main                            
00004d91  EPD_ClearAll                    
00004e0d  EPD_Display                     
00004e85  __TI_decompress_lzss            
00004efd  EPD_Display_Clear               
00004f71  __aeabi_d2f                     
00004f71  __truncdfsf2                    
00004fe5  Show_menu                       
00005055  __gedf2                         
00005055  __gtdf2                         
000050c5  Paint_Clear                     
00005131  __cmpdf2                        
00005131  __eqdf2                         
00005131  __ledf2                         
00005131  __ltdf2                         
00005131  __nedf2                         
0000519d  EPD_FastMode2Init               
0000526d  SYSCFG_DL_PWM_0_init            
000052d1  __aeabi_dcmpeq                  
000052e5  __aeabi_dcmplt                  
000052f9  __aeabi_dcmple                  
0000530d  __aeabi_dcmpge                  
00005321  __aeabi_dcmpgt                  
00005335  __aeabi_fcmpeq                  
00005349  __aeabi_fcmplt                  
0000535d  __aeabi_fcmple                  
00005371  __aeabi_fcmpge                  
00005385  __aeabi_fcmpgt                  
000054b9  EPD_ShowString                  
00005575  frexp                           
00005575  frexpl                          
000055d1  __TI_ltoa                       
00005681  __aeabi_idiv                    
00005681  __aeabi_idivmod                 
00005775  DL_DMA_initChannel              
0000580d  Init_All                        
00005859  TIMG8_IRQHandler                
000058a5  User_ADC_Init                   
0000593d  __aeabi_d2iz                    
0000593d  __fixdfsi                       
00005989  DL_UART_init                    
000059d1  Set_Fs                          
00005a19  EPD_ShowNum                     
00005a61  SYSCFG_DL_TIMER_0_init          
00005aa5  DL_ADC12_setClockConfig         
00005ae5  SYSCFG_DL_UART_0_init           
00005b25  User_DAC_Init                   
00005b65  __aeabi_uidiv                   
00005b65  __aeabi_uidivmod                
00005ba5  __aeabi_f2d                     
00005ba5  __extendsfdf2                   
00005be5  atoi                            
00005c25  EPD_Clear_R26H                  
00005c61  EPD_HW_RESET                    
00005c9d  __aeabi_i2f                     
00005c9d  __floatsisf                     
00005cd9  __gesf2                         
00005cd9  __gtsf2                         
00005d15  __TI_auto_init_nobinit_nopinit  
00005d51  __cmpsf2                        
00005d51  __eqsf2                         
00005d51  __lesf2                         
00005d51  __ltsf2                         
00005d51  __nesf2                         
00005d8d  __muldsi3                       
00005e01  __aeabi_f2iz                    
00005e01  __fixsfsi                       
00005e39  sprintf                         
00005ea5  SYSCFG_DL_SYSCTL_init           
00005ed9  SYSCFG_DL_init                  
00005fcd  EPD_WR_DATA8                    
00005ffd  EPD_WR_REG                      
0000605d  ADC0_IRQHandler                 
000060b5  SYSCFG_DL_DAC12_init            
0000610d  __aeabi_i2d                     
0000610d  __floatsidf                     
0000627d  SYSCFG_DL_DMA_CH0_init          
000062a5  _c_int00_noargs                 
00006319  __aeabi_lmul                    
00006319  __muldi3                        
0000633d  delay_ms                        
0000635f  memccpy                         
000063fd  __aeabi_llsl                    
000063fd  __ashldi3                       
00006589  DL_Timer_setCaptCompUpdateMethod
000065a5  DL_Timer_setClockConfig         
000065c1  EPD_READBUSY                    
000065f7  EPD_FastUpdate                  
00006611  EPD_PartUpdate                  
000067c5  DL_Timer_setCaptureCompareOutCtl
00006855  SYSCFG_DL_DMA_CH1_init          
000069a5  __aeabi_uldivmod                
000069f1  DL_UART_setClockConfig          
00006a03  TI_memcpy_small                 
00006a15  __TI_decompress_none            
00006a49  DL_Timer_setCaptureCompareValue 
00006a59  wcslen                          
00006a69  __TI_zero_init                  
00006ab5  __aeabi_memset                  
00006ab5  __aeabi_memset4                 
00006ab5  __aeabi_memset8                 
00006ad1  TI_memset_small                 
00006adf  SYSCFG_DL_DMA_init              
00006aed  __aeabi_memclr                  
00006aed  __aeabi_memclr4                 
00006aed  __aeabi_memclr8                 
00006af9  DL_Common_delayCycles           
00006b89  __aeabi_errno_addr              
00006b91  __aeabi_memcpy                  
00006b91  __aeabi_memcpy4                 
00006b91  __aeabi_memcpy8                 
00006be1  ADC1_IRQHandler                 
00006be1  AES_IRQHandler                  
00006be1  CANFD0_IRQHandler               
00006be1  DAC0_IRQHandler                 
00006be1  DMA_IRQHandler                  
00006be1  Default_Handler                 
00006be1  GROUP0_IRQHandler               
00006be1  GROUP1_IRQHandler               
00006be1  HardFault_Handler               
00006be1  I2C0_IRQHandler                 
00006be1  I2C1_IRQHandler                 
00006be1  NMI_Handler                     
00006be1  PendSV_Handler                  
00006be1  RTC_IRQHandler                  
00006be1  SPI0_IRQHandler                 
00006be1  SPI1_IRQHandler                 
00006be1  SVC_Handler                     
00006be1  SysTick_Handler                 
00006be1  TIMA0_IRQHandler                
00006be1  TIMA1_IRQHandler                
00006be1  TIMG0_IRQHandler                
00006be1  TIMG12_IRQHandler               
00006be1  TIMG6_IRQHandler                
00006be1  TIMG7_IRQHandler                
00006be1  UART0_IRQHandler                
00006be1  UART1_IRQHandler                
00006be1  UART2_IRQHandler                
00006be1  UART3_IRQHandler                
00006c01  Reset_Handler                   
00006c15  _system_pre_init                
00006c18  C$$EXIT                         
00006c19  abort                           
00006c20  asc2_1608                       
00007320  __aeabi_ctype_table_            
00007320  __aeabi_ctype_table_C           
00007422  DAC_Buff                        
000076b0  __TI_Handler_Table_Base         
000076bc  __TI_Handler_Table_Limit        
000076d0  __TI_CINIT_Base                 
000076e0  __TI_CINIT_Limit                
000076e0  __TI_CINIT_Warm                 
20200000  ImageBW                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200b48  gADCSamples                     
20200d48  gTIMER_0Backup                  
20200e04  Paint                           
20200e18  FFT_Data                        
20201618  Pwm_Data                        
202016e0  __aeabi_errno                   
202016e4  Spwm_cnt                        
202016e5  adc_flag                        
202016e6  key_val                         
20207e00  __stack                         
20208000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[242 symbols]

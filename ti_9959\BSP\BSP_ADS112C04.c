/*
 * BSP_ADS112C04.c
 *
 * ADS112C04驱动
 * 
 *  Created on: 2024年8月19日
 *      Author: <PERSON><PERSON><PERSON> <PERSON>
 */
#include "BSP_ADS112C04.h"

#define ADS112_ADDRESS 0x80

uint8_t DRDY_Flag = 0;

//寄存器配置
uint8_t reg0=0x00,reg1=0x00,reg2=0x00,reg3=0x00;
uint8_t ADS112_Channel,Gain,PGA_BYPASS;       //reg0
uint8_t Rate,Mode,CM,ADS112_VREF,TS;          //reg1
uint8_t DCNT,ADS112_CRC,BCS,IDAC;             //reg2
uint8_t I1MUX,I2MUX;                          //reg3
//启动标志位
uint8_t Start_Flag = 0;

void ADS112_WriteReg(uint8_t RegAddress,uint8_t Data)
{
	I2C_Start();
	I2C_SendByte(ADS112_ADDRESS);
	I2C_ReceiveAck();
	I2C_SendByte(0x40 | (RegAddress<<2));
	I2C_ReceiveAck();
	I2C_SendByte(Data);
	I2C_ReceiveAck();
	I2C_Stop();
}

uint8_t ADS112_ReadReg(uint8_t RegAddress)
{
	uint8_t Data;
	I2C_Start();
	I2C_SendByte(ADS112_ADDRESS);
	I2C_ReceiveAck();
	I2C_SendByte(0x20 | (RegAddress<<2));
	I2C_ReceiveAck();

	I2C_Start();
	I2C_SendByte(ADS112_ADDRESS | 0x01);
	I2C_ReceiveAck();
	Data = I2C_ReceiveByte();
	I2C_SendAck(1);
	I2C_Stop();
	
	return Data;	
}

void ADS112_Reset(void)
{
	I2C_Start();
	I2C_SendByte(ADS112_ADDRESS);
	I2C_ReceiveAck();
	I2C_SendByte(ADS112_RESET);
    I2C_ReceiveAck();
    I2C_Stop();
}

void ADS112_Start(void)
{
	I2C_Start();
	I2C_SendByte(ADS112_ADDRESS);
	I2C_ReceiveAck();
	I2C_SendByte(ADS112_START);
    I2C_ReceiveAck();
    I2C_Stop();
}

void ADS112_Powerdowm(void)
{
	I2C_Start();
	I2C_SendByte(ADS112_ADDRESS);
	I2C_ReceiveAck();
	I2C_SendByte(ADS112_POWERDOWM);
    I2C_ReceiveAck();
    I2C_Stop();
}

uint16_t ADS112_Get_Data(void)
{
    uint8_t MSB,LSB;
    I2C_Start();
    I2C_SendByte(ADS112_ADDRESS);
    I2C_ReceiveAck();
    I2C_SendByte(ADS112_RDATA);
    I2C_ReceiveAck();

    I2C_Start();
    I2C_SendByte(ADS112_ADDRESS | 0x01);
    I2C_ReceiveAck();
    MSB = I2C_ReceiveByte();
    I2C_SendAck(0);
    LSB = I2C_ReceiveByte();
    I2C_SendAck(1);
    I2C_Stop();
    return ((MSB<<8)+LSB);
}

void ADS112_Reg_Init(void)
{
    //配置reg0
    ADS112_Channel = AIN0_and_AIN1;
    Gain = GAIN_1;
    PGA_BYPASS = PGA_EN;

    //配置reg1
    Rate = RATE_NORMAL1000_TURBO2000;
    Mode = MODE_TURBO;
    CM = CM_CONTINUOUS;
    ADS112_VREF = VREF_Internal;
    TS = TS_DIS;
    
    //配置reg2
    DCNT = COUNTER_DIS;
    ADS112_CRC = CRC_DIS;
    BCS = BCS_OFF;
    IDAC = IDAC_OFF;

    //配置reg3
    I1MUX = IDAC1_DIS;
    I2MUX = IDAC2_DIS;
}


void ADS112_Set(void)
{
    reg0 = ADS112_Channel<<4 | Gain<<1 | PGA_BYPASS;
    reg1 = Rate<<5 | Mode<<4 | CM<<3 | ADS112_VREF<<1 | TS;
    reg2 = DCNT<<6 | ADS112_CRC<<4 | BCS<<3 | IDAC;
    reg3 = I1MUX<<5 | I2MUX<<2;

    ADS112_WriteReg(0,reg0);
    ADS112_WriteReg(1,reg1);
    ADS112_WriteReg(2,reg2);
    ADS112_WriteReg(3,reg3);
}

void ADS112_Init(void)
{
    //复位
    ADS112_Reset();

    //配置寄存器
    ADS112_Reg_Init();
    ADS112_Set();
    ADS112_Start();
}

// 单次采样
uint16_t ADS112_Get_Once_Data(void)
{
    ADS112_Start();
    DRDY_Flag = 0;
    while(DRDY_Flag == 0);//等待转换完成
    return ADS112_Get_Data();
}

// 连续采样
uint16_t ADS112_Get_Continuous_Data(void)
{
    uint32_t TimeOut = 3200000;
    DRDY_Flag = 0;
    while(DRDY_Flag == 0)//等待转换完成
    {
        TimeOut --;
        if(TimeOut == 0)break;//超时退出
    }
    return ADS112_Get_Data();
}

// 输入捕获中断服务函数
void CAPTURE_1_INST_IRQHandler(void)
{
    // switch (DL_TimerG_getPendingInterrupt(CAPTURE_1_INST)) {
    //     case DL_TIMERG_IIDX_CC0_DN:
    //         DRDY_Flag = 1;
    //         break;
    //     default:
    //         break;
    // }
}
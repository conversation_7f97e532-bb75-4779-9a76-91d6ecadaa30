/*
 * User_FFT.h
 *
 *  Created on: 2024年8月19日
 *      Author: <PERSON><PERSON><PERSON>
 */

#ifndef __USER_FFT_H_
#define __USER_FFT_H_

#include "User_Header.h"

#include "math.h"

#ifndef PI
#define PI 3.1415926535897932384626433832795028841971
#endif

#ifndef SampNum
#define SampNum 256
#endif

#ifndef Complex
#define Complex
typedef struct complex1 //复数类型
{
    float real;       //实部
    float imag;       //虚部
}complex;
#endif

void fft(int Num, complex f[]);

uint16_t User_GetSpectrum( uint16_t d_len );
float User_FixPha( float pha );

extern complex FFT_Data[SampNum];

#endif /* __USER_FFT_H_ */

******************************************************************************
            TI ARM Clang Linker PC v3.2.2                      
******************************************************************************
>> Linked Fri Aug  9 15:24:09 2024

OUTPUT FILE NAME:   <Template.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00001655


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000025c0  0001da40  R  X
  SRAM                  20200000   00008000  000012ef  00006d11  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000025c0   000025c0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00001bc8   00001bc8    r-x .text
  00001c88    00001c88    00000908   00000908    r-- .rodata
  00002590    00002590    00000030   00000030    r-- .cinit
20200000    20200000    000010ef   00000000    rw-
  20200000    20200000    000010ed   00000000    rw- .bss
  202010ed    202010ed    00000002   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00001bc8     
                  000000c0    00000384     BSP_4x4KEY.o (.text.Read4X4KEY)
                  00000444    0000012c     oled.o (.text.I2C0_IRQHandler)
                  00000570    00000118     oled.o (.text.OLED_ShowChar)
                  00000688    00000100     oled.o (.text.OLED_Init)
                  00000788    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00000870    000000dc     oled.o (.text.OLED_WR_Byte)
                  0000094c    000000dc     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000a28    000000b4     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00000adc    000000a8     oled.o (.text.OLED_ShowChinese)
                  00000b84    00000098     main.o (.text.main)
                  00000c1c    00000078     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000c94    00000078     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00000d0c    00000074     main.o (.text.Show_menu)
                  00000d80    00000070     oled.o (.text.OLED_ShowString)
                  00000df0    0000006a     oled.o (.text.OLED_Clear)
                  00000e5a    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000e5c    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00000ec0    00000060     ti_msp_dl_config.o (.text.DL_ADC12_initSeqSample)
                  00000f20    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00000f7e    00000002     --HOLE-- [fill = 0]
                  00000f80    00000050     oled.o (.text.DL_I2C_startControllerTransfer)
                  00000fd0    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  0000101c    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00001068    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  000010b2    00000002     --HOLE-- [fill = 0]
                  000010b4    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000010fc    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00001140    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00001180    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  000011c0    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00001200    0000003c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunctionFeatures)
                  0000123c    0000003c     oled.o (.text.OLED_Set_Pos)
                  00001278    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  000012b4    00000038     ti_msp_dl_config.o (.text.DL_Timer_setPublisherChanID)
                  000012ec    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00001320    00000030     ti_msp_dl_config.o (.text.DL_DMA_setTransferSize)
                  00001350    00000030     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutputFeatures)
                  00001380    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  000013b0    0000002c     main.o (.text.ADC0_IRQHandler)
                  000013dc    0000002c     ti_msp_dl_config.o (.text.DL_ADC12_setDMASamplesCnt)
                  00001408    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH0_init)
                  00001434    0000002c     main.o (.text.TaskA_Handler)
                  00001460    0000002c     main.o (.text.TaskB_Handler)
                  0000148c    0000002c     main.o (.text.TaskC_Handler)
                  000014b8    0000002c     main.o (.text.__NVIC_EnableIRQ)
                  000014e4    0000002c     oled.o (.text.__NVIC_EnableIRQ)
                  00001510    0000002a     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000153a    00000028     oled.o (.text.DL_Common_updateReg)
                  00001562    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  0000158a    00000002     --HOLE-- [fill = 0]
                  0000158c    00000028     main.o (.text.DL_DMA_setDestAddr)
                  000015b4    00000028     main.o (.text.DL_DMA_setSrcAddr)
                  000015dc    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerRXFIFOThreshold)
                  00001604    00000028     ti_msp_dl_config.o (.text.DL_I2C_setControllerTXFIFOThreshold)
                  0000162c    00000028     ti_msp_dl_config.o (.text.DL_Timer_enableEvent)
                  00001654    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  0000167c    00000026     main.o (.text.DL_DMA_enableChannel)
                  000016a2    00000026     ti_msp_dl_config.o (.text.DL_I2C_setAnalogGlitchFilterPulseWidth)
                  000016c8    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000016ee    00000022     oled.o (.text.delay_ms)
                  00001710    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00001730    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  0000174e    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  0000176c    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_clearInterruptStatus)
                  00001788    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableDMA)
                  000017a4    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableDMATrigger)
                  000017c0    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableFIFO)
                  000017dc    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableInterrupt)
                  000017f8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableHiZ)
                  00001814    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00001830    0000001c     oled.o (.text.DL_I2C_disableInterrupt)
                  0000184c    0000001c     ti_msp_dl_config.o (.text.DL_I2C_enableInterrupt)
                  00001868    0000001c     oled.o (.text.DL_I2C_isControllerRXFIFOEmpty)
                  00001884    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setMCLKDivider)
                  000018a0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000018bc    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000018d8    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000018f4    0000001a     ti_msp_dl_config.o (.text.DL_ADC12_setSubscriberChanID)
                  0000190e    00000002     --HOLE-- [fill = 0]
                  00001910    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00001928    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00001940    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00001958    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  00001970    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00001988    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000019a0    00000018     BSP_4x4KEY.o (.text.DL_GPIO_setPins)
                  000019b8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000019d0    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableAnalogGlitchFilter)
                  000019e8    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableController)
                  00001a00    00000018     ti_msp_dl_config.o (.text.DL_I2C_enableControllerClockStretching)
                  00001a18    00000018     ti_msp_dl_config.o (.text.DL_I2C_enablePower)
                  00001a30    00000018     ti_msp_dl_config.o (.text.DL_I2C_reset)
                  00001a48    00000018     ti_msp_dl_config.o (.text.DL_I2C_setTimerPeriod)
                  00001a60    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  00001a78    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  00001a90    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00001aa8    00000018     main.o (.text.DL_Timer_stopCounter)
                  00001ac0    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00001ad8    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00001af0    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  00001b06    00000016     BSP_4x4KEY.o (.text.DL_GPIO_readPins)
                  00001b1c    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00001b32    00000002     --HOLE-- [fill = 0]
                  00001b34    00000014     main.o (.text.DL_ADC12_getFIFOAddress)
                  00001b48    00000014     BSP_4x4KEY.o (.text.DL_GPIO_clearPins)
                  00001b5c    00000014     oled.o (.text.DL_I2C_getControllerStatus)
                  00001b70    00000014     oled.o (.text.DL_I2C_receiveControllerData)
                  00001b84    00000014     ti_msp_dl_config.o (.text.DL_I2C_resetControllerTransfer)
                  00001b98    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  00001bac    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  00001bc0    00000012     main.o (.text.DL_ADC12_getPendingInterrupt)
                  00001bd2    00000012     oled.o (.text.DL_I2C_getPendingInterrupt)
                  00001be4    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00001bf6    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00001c08    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00001c1a    00000002     --HOLE-- [fill = 0]
                  00001c1c    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00001c2c    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  00001c3c    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00001c4a    00000002     --HOLE-- [fill = 0]
                  00001c4c    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00001c58    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00001c62    00000008     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00001c6a    00000002     --HOLE-- [fill = 0]
                  00001c6c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00001c74    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00001c78    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00001c7c    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00001c80    00000004            : exit.c.obj (.text:abort)
                  00001c84    00000004     --HOLE-- [fill = 0]

.cinit     0    00002590    00000030     
                  00002590    0000000c     (__TI_handler_table)
                  0000259c    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000025a4    00000006     (.cinit..data.load) [load image, compression = lzss]
                  000025aa    00000002     --HOLE-- [fill = 0]
                  000025ac    00000010     (__TI_cinit_table)
                  000025bc    00000004     --HOLE-- [fill = 0]

.rodata    0    00001c88    00000908     
                  00001c88    000005f0     oled.o (.rodata.asc2_1608)
                  00002278    00000228     oled.o (.rodata.asc2_0806)
                  000024a0    00000080     oled.o (.rodata.Hzk)
                  00002520    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH0Config)
                  00002538    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  0000254c    0000000e     main.o (.rodata.str1.171135724027876188451)
                  0000255a    0000000e     main.o (.rodata.str1.66840473539710954921)
                  00002568    0000000e     main.o (.rodata.str1.88864587114646350841)
                  00002576    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00002580    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00002588    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  0000258b    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  0000258d    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  0000258f    00000001     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    000010ed     UNINITIALIZED
                  20200000    00001000     (.common:gADCSamples)
                  20201000    000000bc     (.common:gTIMER_0Backup)
                  202010bc    00000010     (.common:gRxPacket)
                  202010cc    00000010     (.common:gTxPacket)
                  202010dc    00000004     (.common:gRxCount)
                  202010e0    00000004     (.common:gRxLen)
                  202010e4    00000004     (.common:gTxCount)
                  202010e8    00000004     (.common:gTxLen)
                  202010ec    00000001     (.common:gI2cControllerStatus)

.data      0    202010ed    00000002     UNINITIALIZED
                  202010ed    00000001     main.o (.data.ad_over)
                  202010ee    00000001     main.o (.data.key_val)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       main.o                         668    42        4098   
       ti_msp_dl_config.o             2624   69        188    
       startup_mspm0g350x_ticlang.o   8      192       0      
    +--+------------------------------+------+---------+---------+
       Total:                         3300   303       4286   
                                                              
    .\App\
       BSP_4x4KEY.o                   966    0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         966    0         0      
                                                              
    .\OLED\
       oled.o                         1814   2200      49     
    +--+------------------------------+------+---------+---------+
       Total:                         1814   2200      49     
                                                              
    C:/ti/mspm0_sdk_2_00_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     260    0         0      
       dl_i2c.o                       132    0         0      
       dl_uart.o                      90     0         0      
       dl_dma.o                       76     0         0      
       dl_adc12.o                     64     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         632    0         0      
                                                              
    C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       copy_decompress_lzss.c.obj     120    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_decompress_none.c.obj     18     0         0      
       memcpy16.S.obj                 18     0         0      
       copy_zero_init.c.obj           16     0         0      
       memset16.S.obj                 14     0         0      
       exit.c.obj                     4      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         294    0         0      
                                                              
    C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang/15.0.7/lib/armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_uidivmod.S.obj           64     0         0      
       aeabi_memset.S.obj             12     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         86     0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      42        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   7092   2545      4847   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000025ac records: 2, size/record: 8, table size: 16
	.bss: load addr=0000259c, load size=00000008 bytes, run addr=20200000, run size=000010ed bytes, compression=zero_init
	.data: load addr=000025a4, load size=00000006 bytes, run addr=202010ed, run size=00000002 bytes, compression=lzss


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00002590 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
000013b1  ADC0_IRQHandler               
00001c75  ADC1_IRQHandler               
00001c75  AES_IRQHandler                
00001c80  C$$EXIT                       
00001c75  CANFD0_IRQHandler             
00001c75  DAC0_IRQHandler               
00001141  DL_ADC12_setClockConfig       
00001c59  DL_Common_delayCycles         
00000fd1  DL_DMA_initChannel            
00000f21  DL_I2C_fillControllerTXFIFO   
000016c9  DL_I2C_setClockConfig         
00000789  DL_Timer_initTimerMode        
000018d9  DL_Timer_setClockConfig       
000010b5  DL_UART_init                  
00001be5  DL_UART_setClockConfig        
00001c75  DMA_IRQHandler                
00001c75  Default_Handler               
00001c75  GROUP0_IRQHandler             
00001c75  GROUP1_IRQHandler             
00001c75  HardFault_Handler             
000024a0  Hzk                           
00000445  I2C0_IRQHandler               
00001c75  I2C1_IRQHandler               
00001c75  NMI_Handler                   
00000df1  OLED_Clear                    
00000689  OLED_Init                     
0000123d  OLED_Set_Pos                  
00000571  OLED_ShowChar                 
00000add  OLED_ShowChinese              
00000d81  OLED_ShowString               
00000871  OLED_WR_Byte                  
00001c75  PendSV_Handler                
00001c75  RTC_IRQHandler                
000000c1  Read4X4KEY                    
00001c79  Reset_Handler                 
00001c75  SPI0_IRQHandler               
00001c75  SPI1_IRQHandler               
00001c75  SVC_Handler                   
00000a29  SYSCFG_DL_ADC12_0_init        
00001409  SYSCFG_DL_DMA_CH0_init        
00001c63  SYSCFG_DL_DMA_init            
0000094d  SYSCFG_DL_GPIO_init           
00000e5d  SYSCFG_DL_I2C_OLED_init       
00001511  SYSCFG_DL_SYSCTL_init         
000010fd  SYSCFG_DL_TIMER_0_init        
00001181  SYSCFG_DL_UART_0_init         
00001381  SYSCFG_DL_init                
00000c1d  SYSCFG_DL_initPower           
00000d0d  Show_menu                     
00001c75  SysTick_Handler               
00001c75  TIMA0_IRQHandler              
00001c75  TIMA1_IRQHandler              
00001c75  TIMG0_IRQHandler              
00001c75  TIMG12_IRQHandler             
00001c75  TIMG6_IRQHandler              
00001c75  TIMG7_IRQHandler              
00001c75  TIMG8_IRQHandler              
00001bf7  TI_memcpy_small               
00001c3d  TI_memset_small               
00001c75  UART0_IRQHandler              
00001c75  UART1_IRQHandler              
00001c75  UART2_IRQHandler              
00001c75  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
000025ac  __TI_CINIT_Base               
000025bc  __TI_CINIT_Limit              
000025bc  __TI_CINIT_Warm               
00002590  __TI_Handler_Table_Base       
0000259c  __TI_Handler_Table_Limit      
00001279  __TI_auto_init_nobinit_nopinit
00000c95  __TI_decompress_lzss          
00001c09  __TI_decompress_none          
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
00001c2d  __TI_zero_init                
00000e5b  __aeabi_idiv0                 
00001c4d  __aeabi_memclr                
00001c4d  __aeabi_memclr4               
00001c4d  __aeabi_memclr8               
00001c6d  __aeabi_memcpy                
00001c6d  __aeabi_memcpy4               
00001c6d  __aeabi_memcpy8               
000011c1  __aeabi_uidiv                 
000011c1  __aeabi_uidivmod              
ffffffff  __binit__                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
00001655  _c_int00_noargs               
UNDEFED   _system_post_cinit            
00001c7d  _system_pre_init              
00001c81  abort                         
202010ed  ad_over                       
00002278  asc2_0806                     
00001c88  asc2_1608                     
ffffffff  binit                         
000016ef  delay_ms                      
20200000  gADCSamples                   
202010ec  gI2cControllerStatus          
202010dc  gRxCount                      
202010e0  gRxLen                        
202010bc  gRxPacket                     
20201000  gTIMER_0Backup                
202010e4  gTxCount                      
202010e8  gTxLen                        
202010cc  gTxPacket                     
00000000  interruptVectors              
202010ee  key_val                       
00000b85  main                          


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  Read4X4KEY                    
00000200  __STACK_SIZE                  
00000445  I2C0_IRQHandler               
00000571  OLED_ShowChar                 
00000689  OLED_Init                     
00000789  DL_Timer_initTimerMode        
00000871  OLED_WR_Byte                  
0000094d  SYSCFG_DL_GPIO_init           
00000a29  SYSCFG_DL_ADC12_0_init        
00000add  OLED_ShowChinese              
00000b85  main                          
00000c1d  SYSCFG_DL_initPower           
00000c95  __TI_decompress_lzss          
00000d0d  Show_menu                     
00000d81  OLED_ShowString               
00000df1  OLED_Clear                    
00000e5b  __aeabi_idiv0                 
00000e5d  SYSCFG_DL_I2C_OLED_init       
00000f21  DL_I2C_fillControllerTXFIFO   
00000fd1  DL_DMA_initChannel            
000010b5  DL_UART_init                  
000010fd  SYSCFG_DL_TIMER_0_init        
00001141  DL_ADC12_setClockConfig       
00001181  SYSCFG_DL_UART_0_init         
000011c1  __aeabi_uidiv                 
000011c1  __aeabi_uidivmod              
0000123d  OLED_Set_Pos                  
00001279  __TI_auto_init_nobinit_nopinit
00001381  SYSCFG_DL_init                
000013b1  ADC0_IRQHandler               
00001409  SYSCFG_DL_DMA_CH0_init        
00001511  SYSCFG_DL_SYSCTL_init         
00001655  _c_int00_noargs               
000016c9  DL_I2C_setClockConfig         
000016ef  delay_ms                      
000018d9  DL_Timer_setClockConfig       
00001be5  DL_UART_setClockConfig        
00001bf7  TI_memcpy_small               
00001c09  __TI_decompress_none          
00001c2d  __TI_zero_init                
00001c3d  TI_memset_small               
00001c4d  __aeabi_memclr                
00001c4d  __aeabi_memclr4               
00001c4d  __aeabi_memclr8               
00001c59  DL_Common_delayCycles         
00001c63  SYSCFG_DL_DMA_init            
00001c6d  __aeabi_memcpy                
00001c6d  __aeabi_memcpy4               
00001c6d  __aeabi_memcpy8               
00001c75  ADC1_IRQHandler               
00001c75  AES_IRQHandler                
00001c75  CANFD0_IRQHandler             
00001c75  DAC0_IRQHandler               
00001c75  DMA_IRQHandler                
00001c75  Default_Handler               
00001c75  GROUP0_IRQHandler             
00001c75  GROUP1_IRQHandler             
00001c75  HardFault_Handler             
00001c75  I2C1_IRQHandler               
00001c75  NMI_Handler                   
00001c75  PendSV_Handler                
00001c75  RTC_IRQHandler                
00001c75  SPI0_IRQHandler               
00001c75  SPI1_IRQHandler               
00001c75  SVC_Handler                   
00001c75  SysTick_Handler               
00001c75  TIMA0_IRQHandler              
00001c75  TIMA1_IRQHandler              
00001c75  TIMG0_IRQHandler              
00001c75  TIMG12_IRQHandler             
00001c75  TIMG6_IRQHandler              
00001c75  TIMG7_IRQHandler              
00001c75  TIMG8_IRQHandler              
00001c75  UART0_IRQHandler              
00001c75  UART1_IRQHandler              
00001c75  UART2_IRQHandler              
00001c75  UART3_IRQHandler              
00001c79  Reset_Handler                 
00001c7d  _system_pre_init              
00001c80  C$$EXIT                       
00001c81  abort                         
00001c88  asc2_1608                     
00002278  asc2_0806                     
000024a0  Hzk                           
00002590  __TI_Handler_Table_Base       
0000259c  __TI_Handler_Table_Limit      
000025ac  __TI_CINIT_Base               
000025bc  __TI_CINIT_Limit              
000025bc  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  gADCSamples                   
20201000  gTIMER_0Backup                
202010bc  gRxPacket                     
202010cc  gTxPacket                     
202010dc  gRxCount                      
202010e0  gRxLen                        
202010e4  gTxCount                      
202010e8  gTxLen                        
202010ec  gI2cControllerStatus          
202010ed  ad_over                       
202010ee  key_val                       
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[124 symbols]

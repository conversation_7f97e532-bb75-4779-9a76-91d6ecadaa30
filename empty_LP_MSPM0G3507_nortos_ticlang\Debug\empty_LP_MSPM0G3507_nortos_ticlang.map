******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Fri Jul 25 20:35:59 2025

OUTPUT FILE NAME:   <empty_LP_MSPM0G3507_nortos_ticlang.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00000c11


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00000dc8  0001f238  R  X
  SRAM                  20200000   00008000  00001348  00006cb8  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00000dc8   00000dc8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000c60   00000c60    r-x .text
  00000d20    00000d20    00000068   00000068    r-- .rodata
  00000d88    00000d88    00000040   00000040    r-- .cinit
20200000    20200000    0000114b   00000000    rw-
  20200000    20200000    00001111   00000000    rw- .bss
  20201114    20201114    00000037   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000c60     
                  000000c0    00000104     Driver_key.o (.text.key_read)
                  000001c4    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  000002ac    000000a4     Driver_uart.o (.text.uart_proc)
                  00000350    0000009c     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_1_init)
                  000003ec    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00000486    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000488    00000098     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00000520    00000084     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  000005a4    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00000626    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000628    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000006a4    0000006c     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000710    00000068     Driver_adc.o (.text.User_ADC_Init)
                  00000778    00000062     libc.a : memset16.S.obj (.text:memset)
                  000007da    00000002     Driver_adc.o (.text.adc_proc)
                  000007dc    00000058     Driver_uart.o (.text.UART0_IRQHandler)
                  00000834    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000888    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  000008d4    0000004c     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00000920    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00000968    00000044     schedule.o (.text.scheduler_run)
                  000009ac    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  000009ec    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000a2c    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00000a6c    00000040     Driver_key.o (.text.key_proc)
                  00000aac    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000ae8    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00000b20    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000b54    00000030     Driver_adc.o (.text.ADC0_IRQHandler)
                  00000b84    00000030     Driver_adc.o (.text.ADC1_IRQHandler)
                  00000bb4    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00000be4    0000002c     empty.o (.text.main)
                  00000c10    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00000c38    00000024     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH1_init)
                  00000c5c    00000020     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH0_init)
                  00000c7c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setClockConfig)
                  00000c98    00000012                 : dl_uart.o (.text.DL_UART_setClockConfig)
                  00000caa    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  00000cbc    00000010     schedule.o (.text.SysTick_Handler)
                  00000ccc    00000010     libc.a : copy_zero_init.c.obj (.text:decompress:ZI)
                  00000cdc    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00000ce8    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00000cf4    0000000c     schedule.o (.text.scheduler_init)
                  00000d00    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00000d0a    00000002     --HOLE-- [fill = 0]
                  00000d0c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00000d14    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00000d18    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00000d1c    00000004            : exit.c.obj (.text:abort)

.cinit     0    00000d88    00000040     
                  00000d88    0000001c     (.cinit..data.load) [load image, compression = lzss]
                  00000da4    0000000c     (__TI_handler_table)
                  00000db0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00000db8    00000010     (__TI_cinit_table)

.rodata    0    00000d20    00000068     
                  00000d20    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH0Config)
                  00000d38    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH1Config)
                  00000d50    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  00000d64    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00000d6e    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00000d70    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00000d78    00000008     ti_msp_dl_config.o (.rodata.gADC12_1ClockConfig)
                  00000d80    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00000d83    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00001111     UNINITIALIZED
                  20200000    00000800     (.common:gADC1Samples)
                  20200800    00000800     (.common:gADCSamples)
                  20201000    000000bc     (.common:gTIMER_0Backup)
                  202010bc    00000028     (.common:uart_data_buffer)
                  202010e4    00000028     (.common:uart_rx_buffer)
                  2020110c    00000004     (.common:system_tick_ms)
                  20201110    00000001     (.common:task_num)

.data      0    20201114    00000037     UNINITIALIZED
                  20201114    00000024     schedule.o (.data.scheduler_task)
                  20201138    00000004     Driver_uart.o (.data.last_rx_time)
                  2020113c    00000002     Driver_uart.o (.data.packet_length)
                  2020113e    00000002     Driver_uart.o (.data.rx_read_index)
                  20201140    00000002     Driver_uart.o (.data.rx_write_index)
                  20201142    00000001     Driver_adc.o (.data.adc1_flag)
                  20201143    00000001     Driver_adc.o (.data.adc_done_count)
                  20201144    00000001     Driver_adc.o (.data.adc_flag)
                  20201145    00000001     Driver_key.o (.data.key_down)
                  20201146    00000001     Driver_key.o (.data.key_early)
                  20201147    00000001     Driver_key.o (.data.key_old)
                  20201148    00000001     Driver_key.o (.data.key_up)
                  20201149    00000001     Driver_key.o (.data.key_val)
                  2020114a    00000001     Driver_uart.o (.data.packet_ready)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             952    99        188    
       startup_mspm0g350x_ticlang.o   6      192       0      
       empty.o                        44     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         1002   291       188    
                                                              
    .\APP\
       Driver_adc.o                   202    0         4099   
       Driver_uart.o                  252    0         91     
       Driver_key.o                   324    0         5      
       schedule.o                     96     0         41     
    +--+------------------------------+------+---------+---------+
       Total:                         874    0         4236   
                                                              
    D:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     260    0         0      
       dl_uart.o                      90     0         0      
       dl_dma.o                       76     0         0      
       dl_adc12.o                     64     0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         500    0         0      
                                                              
    D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       memset16.S.obj                 98     0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_decompress_none.c.obj     18     0         0      
       copy_zero_init.c.obj           16     0         0      
       exit.c.obj                     4      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         518    0         0      
                                                              
    D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       divsf3.S.obj                   130    0         0      
       aeabi_uidivmod.S.obj           64     0         0      
       fixsfsi.S.obj                  56     0         0      
       aeabi_memset.S.obj             12     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         272    0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      64        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   3166   355       4936   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00000db8 records: 2, size/record: 8, table size: 16
	.data: load addr=00000d88, load size=0000001c bytes, run addr=20201114, run size=00000037 bytes, compression=lzss
	.bss: load addr=00000db0, load size=00000008 bytes, run addr=20200000, run size=00001111 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00000da4 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
00000b55  ADC0_IRQHandler               
00000b85  ADC1_IRQHandler               
00000487  AES_IRQHandler                
00000d1c  C$$EXIT                       
00000487  CANFD0_IRQHandler             
00000487  DAC0_IRQHandler               
000009ad  DL_ADC12_setClockConfig       
00000d01  DL_Common_delayCycles         
00000889  DL_DMA_initChannel            
000001c5  DL_Timer_initTimerMode        
00000c7d  DL_Timer_setClockConfig       
00000921  DL_UART_init                  
00000c99  DL_UART_setClockConfig        
00000487  DMA_IRQHandler                
00000487  Default_Handler               
00000487  GROUP0_IRQHandler             
00000487  GROUP1_IRQHandler             
00000487  HardFault_Handler             
00000487  I2C0_IRQHandler               
00000487  I2C1_IRQHandler               
00000487  NMI_Handler                   
00000487  PendSV_Handler                
00000487  RTC_IRQHandler                
00000d15  Reset_Handler                 
00000487  SPI0_IRQHandler               
00000487  SPI1_IRQHandler               
00000487  SVC_Handler                   
00000489  SYSCFG_DL_ADC12_0_init        
00000351  SYSCFG_DL_ADC12_1_init        
00000c5d  SYSCFG_DL_DMA_CH0_init        
00000c39  SYSCFG_DL_DMA_CH1_init        
00000cdd  SYSCFG_DL_DMA_init            
000006a5  SYSCFG_DL_GPIO_init           
000009ed  SYSCFG_DL_SYSCTL_init         
00000bb5  SYSCFG_DL_SYSTICK_init        
000008d5  SYSCFG_DL_TIMER_0_init        
00000521  SYSCFG_DL_UART_0_init         
00000b21  SYSCFG_DL_init                
00000835  SYSCFG_DL_initPower           
00000cbd  SysTick_Handler               
00000487  TIMA0_IRQHandler              
00000487  TIMA1_IRQHandler              
00000487  TIMG0_IRQHandler              
00000487  TIMG12_IRQHandler             
00000487  TIMG6_IRQHandler              
00000487  TIMG7_IRQHandler              
00000487  TIMG8_IRQHandler              
000007dd  UART0_IRQHandler              
00000487  UART1_IRQHandler              
00000487  UART2_IRQHandler              
00000487  UART3_IRQHandler              
00000711  User_ADC_Init                 
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000db8  __TI_CINIT_Base               
00000dc8  __TI_CINIT_Limit              
00000dc8  __TI_CINIT_Warm               
00000da4  __TI_Handler_Table_Base       
00000db0  __TI_Handler_Table_Limit      
00000aad  __TI_auto_init_nobinit_nopinit
00000629  __TI_decompress_lzss          
00000cab  __TI_decompress_none          
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
00000ccd  __TI_zero_init                
00000ae9  __aeabi_f2iz                  
000005a5  __aeabi_fdiv                  
00000627  __aeabi_idiv0                 
00000ce9  __aeabi_memclr                
00000ce9  __aeabi_memclr4               
00000ce9  __aeabi_memclr8               
00000d0d  __aeabi_memcpy                
00000d0d  __aeabi_memcpy4               
00000d0d  __aeabi_memcpy8               
00000a2d  __aeabi_uidiv                 
00000a2d  __aeabi_uidivmod              
ffffffff  __binit__                     
000005a5  __divsf3                      
00000ae9  __fixsfsi                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
00000c11  _c_int00_noargs               
UNDEFED   _system_post_cinit            
00000d19  _system_pre_init              
00000d1d  abort                         
20201142  adc1_flag                     
20201143  adc_done_count                
20201144  adc_flag                      
000007db  adc_proc                      
ffffffff  binit                         
20200000  gADC1Samples                  
20200800  gADCSamples                   
20201000  gTIMER_0Backup                
00000000  interruptVectors              
20201145  key_down                      
20201146  key_early                     
20201147  key_old                       
00000a6d  key_proc                      
000000c1  key_read                      
20201148  key_up                        
20201149  key_val                       
20201138  last_rx_time                  
00000be5  main                          
000003ed  memcpy                        
00000779  memset                        
2020113c  packet_length                 
2020114a  packet_ready                  
2020113e  rx_read_index                 
20201140  rx_write_index                
00000cf5  scheduler_init                
00000969  scheduler_run                 
2020110c  system_tick_ms                
20201110  task_num                      
202010bc  uart_data_buffer              
000002ad  uart_proc                     
202010e4  uart_rx_buffer                


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  key_read                      
000001c5  DL_Timer_initTimerMode        
00000200  __STACK_SIZE                  
000002ad  uart_proc                     
00000351  SYSCFG_DL_ADC12_1_init        
000003ed  memcpy                        
00000487  AES_IRQHandler                
00000487  CANFD0_IRQHandler             
00000487  DAC0_IRQHandler               
00000487  DMA_IRQHandler                
00000487  Default_Handler               
00000487  GROUP0_IRQHandler             
00000487  GROUP1_IRQHandler             
00000487  HardFault_Handler             
00000487  I2C0_IRQHandler               
00000487  I2C1_IRQHandler               
00000487  NMI_Handler                   
00000487  PendSV_Handler                
00000487  RTC_IRQHandler                
00000487  SPI0_IRQHandler               
00000487  SPI1_IRQHandler               
00000487  SVC_Handler                   
00000487  TIMA0_IRQHandler              
00000487  TIMA1_IRQHandler              
00000487  TIMG0_IRQHandler              
00000487  TIMG12_IRQHandler             
00000487  TIMG6_IRQHandler              
00000487  TIMG7_IRQHandler              
00000487  TIMG8_IRQHandler              
00000487  UART1_IRQHandler              
00000487  UART2_IRQHandler              
00000487  UART3_IRQHandler              
00000489  SYSCFG_DL_ADC12_0_init        
00000521  SYSCFG_DL_UART_0_init         
000005a5  __aeabi_fdiv                  
000005a5  __divsf3                      
00000627  __aeabi_idiv0                 
00000629  __TI_decompress_lzss          
000006a5  SYSCFG_DL_GPIO_init           
00000711  User_ADC_Init                 
00000779  memset                        
000007db  adc_proc                      
000007dd  UART0_IRQHandler              
00000835  SYSCFG_DL_initPower           
00000889  DL_DMA_initChannel            
000008d5  SYSCFG_DL_TIMER_0_init        
00000921  DL_UART_init                  
00000969  scheduler_run                 
000009ad  DL_ADC12_setClockConfig       
000009ed  SYSCFG_DL_SYSCTL_init         
00000a2d  __aeabi_uidiv                 
00000a2d  __aeabi_uidivmod              
00000a6d  key_proc                      
00000aad  __TI_auto_init_nobinit_nopinit
00000ae9  __aeabi_f2iz                  
00000ae9  __fixsfsi                     
00000b21  SYSCFG_DL_init                
00000b55  ADC0_IRQHandler               
00000b85  ADC1_IRQHandler               
00000bb5  SYSCFG_DL_SYSTICK_init        
00000be5  main                          
00000c11  _c_int00_noargs               
00000c39  SYSCFG_DL_DMA_CH1_init        
00000c5d  SYSCFG_DL_DMA_CH0_init        
00000c7d  DL_Timer_setClockConfig       
00000c99  DL_UART_setClockConfig        
00000cab  __TI_decompress_none          
00000cbd  SysTick_Handler               
00000ccd  __TI_zero_init                
00000cdd  SYSCFG_DL_DMA_init            
00000ce9  __aeabi_memclr                
00000ce9  __aeabi_memclr4               
00000ce9  __aeabi_memclr8               
00000cf5  scheduler_init                
00000d01  DL_Common_delayCycles         
00000d0d  __aeabi_memcpy                
00000d0d  __aeabi_memcpy4               
00000d0d  __aeabi_memcpy8               
00000d15  Reset_Handler                 
00000d19  _system_pre_init              
00000d1c  C$$EXIT                       
00000d1d  abort                         
00000da4  __TI_Handler_Table_Base       
00000db0  __TI_Handler_Table_Limit      
00000db8  __TI_CINIT_Base               
00000dc8  __TI_CINIT_Limit              
00000dc8  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200000  gADC1Samples                  
20200800  gADCSamples                   
20201000  gTIMER_0Backup                
202010bc  uart_data_buffer              
202010e4  uart_rx_buffer                
2020110c  system_tick_ms                
20201110  task_num                      
20201138  last_rx_time                  
2020113c  packet_length                 
2020113e  rx_read_index                 
20201140  rx_write_index                
20201142  adc1_flag                     
20201143  adc_done_count                
20201144  adc_flag                      
20201145  key_down                      
20201146  key_early                     
20201147  key_old                       
20201148  key_up                        
20201149  key_val                       
2020114a  packet_ready                  
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[131 symbols]

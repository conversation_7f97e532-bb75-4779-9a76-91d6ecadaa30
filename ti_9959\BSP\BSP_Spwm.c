/*
 * BSP_Spwm.c
 *
 * 用于产生Spwm波
 *
 *  Created on: 2024年8月19日
 *      Author: <PERSON><PERSON><PERSON> Huang
 */
#include "BSP_Spwm.h"

uint16_t Pwm_Data[100] = {0};
uint8_t Spwm_cnt = 0;
/*
    SPWM波
    载波频率：10kHz
    一周期点数：100个点
    向上计数模式
*/
//频率范围10~100Hz
void Spwm_SetFre(float Fre)
{
    float Pwm_Fre = Fre*100;
    uint8_t cnt;
    NVIC_DisableIRQ(PWM_0_INST_INT_IRQN);

    DL_TimerG_setLoadValue(PWM_0_INST,32000000/Pwm_Fre);
    for(cnt = 0;cnt < 100 ;cnt ++)
        Pwm_Data[cnt] = (50*sin(2*PI*cnt/100)+50)*320000.0/Pwm_Fre;

    NVIC_EnableIRQ(PWM_0_INST_INT_IRQN);
}

void PWM_0_INST_IRQHandler(void)
{
    switch (DL_TimerG_getPendingInterrupt(PWM_0_INST)) {
        case DL_TIMERG_IIDX_LOAD:
            Spwm_cnt ++;
            if(Spwm_cnt >= 100 )Spwm_cnt = 0;
            DL_TimerG_setCaptureCompareValue(PWM_0_INST, Pwm_Data[Spwm_cnt], DL_TIMER_CC_0_INDEX);
            break;
        default:
            break;
    }
}
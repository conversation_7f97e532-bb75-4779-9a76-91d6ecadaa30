/*
 * BSP_4x4KEY.h
 *
 *  Created on: 2024年8月19日
 *      Author: <PERSON><PERSON><PERSON> <PERSON>
 */
#ifndef __BSP_4X4KEY_H_
#define __BSP_4X4KEY_H_

#include "User_Header.h"

// 枚举 按键名称
enum KEY_NAME
{
    KEY_NULL = 0,   // 无按键

    KEY_NUM1,
    KEY_NUM2,
    KEY_NUM3,
    KEY_A,

    KEY_NUM4,
    <PERSON><PERSON><PERSON>_NUM5,
    <PERSON><PERSON><PERSON>_NUM6,
    <PERSON>EY_B,

    <PERSON>EY_NUM7,
    KEY_NUM8,
    KEY_NUM9,
    KEY_C,

    KEY_STAR,   // 星号键
    KEY_NUM0,
    KEY_HASH,   // 井号键
    KEY_D,
};

// ************************************ 宏定义 操作
#define ROW1_SET DL_GPIO_setPins(KeyBoard_PORT, KeyBoard_ROW1_PIN)
#define ROW1_RESET DL_GPIO_clearPins(KeyBoard_PORT, KeyBoard_ROW1_PIN)

#define ROW2_SET DL_GPIO_setPins(KeyBoard_PORT, KeyBoard_ROW2_PIN)
#define ROW2_RESET DL_GPIO_clearPins(KeyBoard_PORT, KeyBoard_ROW2_PIN)

#define ROW3_SET DL_GPIO_setPins(KeyBoard_PORT, KeyBoard_ROW3_PIN)
#define ROW3_RESET DL_GPIO_clearPins(KeyBoard_PORT, KeyBoard_ROW3_PIN)

#define ROW4_SET DL_GPIO_setPins(KeyBoard_PORT, KeyBoard_ROW4_PIN)
#define ROW4_RESET DL_GPIO_clearPins(KeyBoard_PORT, KeyBoard_ROW4_PIN)

#define COL1_READ DL_GPIO_readPins(KeyBoard_PORT, KeyBoard_COL1_PIN)

#define COL2_READ DL_GPIO_readPins(KeyBoard_PORT, KeyBoard_COL2_PIN)

#define COL3_READ DL_GPIO_readPins(KeyBoard_PORT, KeyBoard_COL3_PIN)

#define COL4_READ DL_GPIO_readPins(KeyBoard_PORT, KeyBoard_COL4_PIN)

// ************************************ 函数声明
uint8_t Read4X4KEY( void );
uint8_t keyToUint(uint8_t key);
float keyboard_readnum(void);
#endif /* __BSP_4X4KEY_H_ */
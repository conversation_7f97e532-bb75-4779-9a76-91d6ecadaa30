#ifndef __DRIVER_ADC_H_
#define __DRIVER_ADC_H_

#include "bsp_system.h"

// 采样点数定义
#define SampNum 1024

// 外部变量声明
extern uint16_t gADCSamples[SampNum];
extern bool adc_flag;

// ADC1相关变量和函数声明
extern uint16_t gADC1Samples[SampNum];
extern bool adc1_flag;


void User_ADC_Init(float fs);
float Set_Fs(float fs);
//ADC0函数声名
void ADC12_0_INST_IRQHandler(void);

// ADC1函数声明
void ADC1_IRQHandler(void);

void adc_proc(void);

#endif


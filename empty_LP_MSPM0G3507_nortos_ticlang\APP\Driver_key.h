#ifndef __DRIVER_KEY_H_
#define __DRIVER_KEY_H_

#include "bsp_system.h"

#define ROW1_SET DL_GPIO_setPins(KeyBoard_PORT, KeyBoard_ROW1_PIN)
#define ROW1_RESET DL_GPIO_clearPins(KeyBoard_PORT, KeyBoard_ROW1_PIN)

#define ROW2_SET DL_GPIO_setPins(KeyBoard_PORT, KeyBoard_ROW2_PIN)
#define ROW2_RESET DL_GPIO_clearPins(KeyBoard_PORT, KeyBoard_ROW2_PIN)

#define ROW3_SET DL_GPIO_setPins(KeyBoard_PORT, KeyBoard_ROW3_PIN)
#define ROW3_RESET DL_GPIO_clearPins(KeyBoard_PORT, KeyBoard_ROW3_PIN)

#define ROW4_SET DL_GPIO_setPins(KeyBoard_PORT, KeyBoard_ROW4_PIN)
#define ROW4_RESET DL_GPIO_clearPins(KeyBoard_PORT, KeyBoard_ROW4_PIN)

#define COL1_READ DL_GPIO_readPins(KeyBoard_PORT, KeyBoard_COL1_PIN)
#define COL2_READ DL_GPIO_readPins(KeyBoard_PORT, KeyBoard_COL2_PIN)
#define COL3_READ DL_GPIO_readPins(KeyBoard_PORT, KeyBoard_COL3_PIN)
#define COL4_READ DL_GPIO_readPins(KeyBoard_PORT, KeyBoard_COL4_PIN)


uint8_t key_read(void);
void key_proc(void);

#endif

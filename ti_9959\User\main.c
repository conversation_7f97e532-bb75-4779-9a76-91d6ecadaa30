
#include "User_Header.h"
#include"AD9959.h"

// 画布
uint8_t ImageBW[2888];

// 计数器
uint16_t cnt = 0,cnt1 = 0;

// 按键变量
uint8_t key_val=0;
uint8_t sweep_flag=0;

uint8_t t=1;

// 函数声明

int main( void )
{
    // 初始化全部
    Init_All();

    while(1)
    {
        key_val = Read4X4KEY();  //获取按键值
        switch( KEY_A )
        {
            case KEY_A:
                TaskA_Handler();
            break;
            case KEY_B:
                TaskB_Handler();
            break;
            case KEY_C:
                TaskC_Handler();
            break;
            case KEY_D:
                TaskD_Handler();
            break;
            default:break;
        }
    }
}

// 菜单1
static void TaskA_Handler( void )
{
	key_val = KEY_NULL;
	// 更新界面
    EPD_ClearAll();
    EPD_ShowNum(40,0,0,"  MENU1  ");
    EPD_Display(ImageBW);
    EPD_PartUpdate();

  Init_AD9959();	

        if(sweep_flag==0)
        {
            //处理数据写入频率数据
            Write_frequence_no_update(0,10000); 
            Write_frequence_no_update(1,10000);
            Write_frequence_no_update(2,10000);
            Write_frequence_no_update(3,10000);

            Write_Amplitude_no_update(3, 200);
            Write_Amplitude_no_update(0, 300);
            Write_Amplitude_no_update(1, 400);
            Write_Amplitude_no_update(2, 500);


            Write_Phase_no_update(0, 0);
            Write_Phase_no_update(1, 180);
            Write_Phase_no_update(2, 0);
            Write_Phase_no_update(3, 0);
    
            IO_Update();
        }

        t=10;
        SweepFre(100,1000000,1);    //生成扫频频率码表
        NVIC_EnableIRQ(TIMER_9959_INST_INT_IRQN);
        DL_TimerA_startCounter(TIMER_9959_INST);
        DL_Timer_setLoadValue(TIMER_9959_INST, 1250*t);
	


	// 进入任务A主循环
	while( key_val != KEY_D )
	{
    key_val = Read4X4KEY();  //获取按键值


        EPD_Display(ImageBW);//刷新显示
        EPD_PartUpdate();
	}
	key_val= KEY_NULL;
    Show_menu();
}

// 菜单2
static void TaskB_Handler( void )
{
	key_val = KEY_NULL;
	// 更新界面
    EPD_ClearAll();
    EPD_ShowNum(40,0,0,"  MENU2  ");
    EPD_Display(ImageBW);
    EPD_PartUpdate();

	// 进入任务B主循环
	while( key_val != KEY_D )
	{
        key_val = Read4X4KEY();  //获取按键值


        EPD_Display(ImageBW);//刷新显示
        EPD_PartUpdate();
	}
	key_val= KEY_NULL;
    Show_menu();
}
uint16_t Ads7886_Value;
float Ads7886_Data;

// 菜单3
static void TaskC_Handler( void )
{
	key_val = KEY_NULL;
	// 更新界面
    EPD_ClearAll();
    EPD_ShowNum(40,0,0,"  MENU3  ");
    EPD_Display(ImageBW);
    EPD_PartUpdate();

	// 进入任务C主循环
	while( key_val != KEY_D )
	{
        key_val = Read4X4KEY();  //获取按键值


        EPD_Display(ImageBW);//刷新显示
        EPD_PartUpdate();
	}
	key_val= KEY_NULL;
    Show_menu();
}

// 菜单4
static void TaskD_Handler( void )
{
	key_val = KEY_NULL;
	// 更新界面
    EPD_ClearAll();
    EPD_ShowNum(40,0,0,"  MENU4  ");
    EPD_Display(ImageBW);
    EPD_PartUpdate();

	// 进入任务D主循环
	while( key_val != KEY_D )
	{
        key_val = Read4X4KEY();  //获取按键值


        EPD_Display(ImageBW);//刷新显示
        EPD_PartUpdate();
    }
    key_val= KEY_NULL;
    Show_menu();
}

// 主菜单
void Show_menu(void)
{
    EPD_ClearAll();

    EPD_ShowNum(40,0,0,"Template");
    EPD_ShowNum(25,4,0,"KEYA -> MENU1   ");
    EPD_ShowNum(25,5,0,"KEYB -> MENU2   ");
    EPD_ShowNum(25,6,0,"KEYC -> MENU3   ");
    EPD_ShowNum(25,7,0,"KEYD -> MENU4   ");
    EPD_Display(ImageBW);
    EPD_PartUpdate();
}

void Init_All(void)
{
    // 系统初始化
    SYSCFG_DL_init();

    // 初始化墨水屏
    Paint_NewImage(ImageBW,EPD_W,EPD_H,0,WHITE);    //创建画布
    Paint_Clear(WHITE);
    EPD_FastMode2Init();
    EPD_Display_Clear();
    EPD_FastUpdate();//更新画面显示
    EPD_Clear_R26H();//工作在局刷模式

    // 初始化片内ADC
    User_ADC_Init(1024000);

    // 初始化片内DAC
    User_DAC_Init();

    // 显示主菜单
    Show_menu();
}

uint32_t count=0;
void TIMER_9959_INST_IRQHandler(void)
{   
    switch (DL_TimerA_getPendingInterrupt(TIMER_9959_INST)) {
        case DL_TIMER_IIDX_ZERO:
          		count++;

                Write_frequence(0,(uint32_t)SweepData[count]);
                
                if(count>=99)
                    count=0;
            break;
        default:
            break;
    }
}
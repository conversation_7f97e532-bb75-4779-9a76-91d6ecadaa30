#ifndef _SPI_INIT_H_
#define _SPI_INIT_H_

#include "ti_msp_dl_config.h"

#define EPD_SCL_Clr() (DL_GPIO_clearPins(EPD_EPD_SCL_PORT, EPD_EPD_SCL_PIN))
#define EPD_SCL_Set() (DL_GPIO_setPins(EPD_EPD_SCL_PORT, EPD_EPD_SCL_PIN))

#define EPD_SDA_Clr() (DL_GPIO_clearPins(EPD_EPD_SDA_PORT, EPD_EPD_SDA_PIN))
#define EPD_SDA_Set() (DL_GPIO_setPins(EPD_EPD_SDA_PORT, EPD_EPD_SDA_PIN))

#define EPD_RES_Clr() (DL_GPIO_clearPins(EPD_EPD_RES_PORT, EPD_EPD_RES_PIN))
#define EPD_RES_Set() (DL_GPIO_setPins(EPD_EPD_RES_PORT, EPD_EPD_RES_PIN))

#define EPD_DC_Clr() (DL_GPIO_clearPins(EPD_EPD_DC_PORT, EPD_EPD_DC_PIN))
#define EPD_DC_Set() (DL_GPIO_setPins(EPD_EPD_DC_PORT, EPD_EPD_DC_PIN))

#define EPD_CS_Clr() (DL_GPIO_clearPins(EPD_EPD_CS_PORT, EPD_EPD_CS_PIN))
#define EPD_CS_Set() (DL_GPIO_setPins(EPD_EPD_CS_PORT, EPD_EPD_CS_PIN))

#define EPD_ReadBusy (DL_GPIO_readPins(EPD_EPD_BUSY_PORT,EPD_EPD_BUSY_PIN))


void EPD_WR_Bus(uint8_t dat);	
void EPD_WR_REG(uint8_t reg);
void EPD_WR_DATA8(uint8_t dat);

#endif

******************************************************************************
            TI ARM Clang Linker PC v3.2.2                      
******************************************************************************
>> Linked Tue Aug 20 16:19:53 2024

OUTPUT FILE NAME:   <ADC_DMA_FFT.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00006171


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00007410  00018bf0  R  X
  SRAM                  20200000   00008000  00001cf5  0000630b  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00007410   00007410    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00006950   00006950    r-x .text
  00006a10    00006a10    000009d0   000009d0    r-- .rodata
  000073e0    000073e0    00000030   00000030    r-- .cinit
20200000    20200000    00001af5   00000000    rw-
  20200000    20200000    00001224   00000000    rw- .bss
  20201224    20201224    000008d1   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00006950     
                  000000c0    00000a70     libc.a : e_pow.c.obj (.text.pow)
                  00000b30    00000a00            : _printfi.c.obj (.text:__TI_printfi)
                  00001530    00000658            : k_rem_pio2.c.obj (.text.__kernel_rem_pio2)
                  00001b88    00000458            : s_sin.c.obj (.text.sin)
                  00001fe0    00000440            : s_cos.c.obj (.text.cos)
                  00002420    00000384     BSP_4x4KEY.o (.text.Read4X4KEY)
                  000027a4    000002f8     libc.a : s_atan.c.obj (.text.atan)
                  00002a9c    00000220            : _printfi.c.obj (.text._pconv_a)
                  00002cbc    000001de     User_FFT.o (.text.fft)
                  00002e9a    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002e9c    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00003078    000001ac     User_FFT.o (.text.User_GetSpectrum)
                  00003224    00000198     libc.a : e_atan2.c.obj (.text.atan2)
                  000033bc    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  0000354e    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  00003550    00000190     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  000036e0    00000184     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00003864    00000168            : k_sin.c.obj (.text.__kernel_sin)
                  000039cc    00000158     EPD_GUI.o (.text.EPD_ShowChar)
                  00003b24    00000150     libc.a : k_cos.c.obj (.text.__kernel_cos)
                  00003c74    00000144            : s_floor.c.obj (.text.floor)
                  00003db8    0000013c            : _printfi.c.obj (.text.fcvt)
                  00003ef4    00000128     main.o (.text.TaskA_Handler)
                  0000401c    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  0000413c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00004248    000000f4     EPD_GUI.o (.text.Paint_SetPixel)
                  0000433c    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00004424    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00004508    000000d8     User_FFT.o (.text.Wn_i)
                  000045e0    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000046b8    000000d8     libclang_rt.builtins.a : addsf3.S.obj (.text)
                  00004790    000000c4     driverlib.a : dl_timer.o (.text.DL_Timer_initPWMMode)
                  00004854    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  000048f6    00000002     --HOLE-- [fill = 0]
                  000048f8    0000009c     User_ADC.o (.text.Get_AC_Vol)
                  00004994    00000094     EPD_GUI.o (.text.Paint_NewImage)
                  00004a28    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00004ab8    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00004b44    00000084     SPI_Init.o (.text.EPD_WR_Bus)
                  00004bc8    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00004c4a    00000002     --HOLE-- [fill = 0]
                  00004c4c    00000080     main.o (.text.main)
                  00004ccc    0000007c     EPD_GUI.o (.text.EPD_ClearAll)
                  00004d48    00000078     EPD.o (.text.EPD_Display)
                  00004dc0    00000078     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00004e38    00000078     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00004eb0    00000074     EPD.o (.text.EPD_Display_Clear)
                  00004f24    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00004f30    00000074                            : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00004fa4    00000070     main.o (.text.Show_menu)
                  00005014    00000070     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00005084    0000006c     EPD_GUI.o (.text.Paint_Clear)
                  000050f0    0000006c     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  0000515c    00000068     EPD.o (.text.EPD_FastMode2Init)
                  000051c4    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  0000522a    00000002     --HOLE-- [fill = 0]
                  0000522c    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00005290    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000052f2    00000002     --HOLE-- [fill = 0]
                  000052f4    00000062                            : aeabi_fcmp.S.obj (.text.__aeabi_fcmp)
                  00005356    00000002     --HOLE-- [fill = 0]
                  00005358    00000060     main.o (.text.TaskB_Handler)
                  000053b8    00000060     main.o (.text.TaskC_Handler)
                  00005418    00000060     main.o (.text.TaskD_Handler)
                  00005478    0000005e     EPD_GUI.o (.text.EPD_ShowString)
                  000054d6    0000005e     User_FFT.o (.text.c_mul)
                  00005534    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00005590    00000058            : _ltoa.c.obj (.text.__TI_ltoa)
                  000055e8    00000058            : _printfi.c.obj (.text._pconv_f)
                  00005640    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00005696    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  000056e8    00000050     User_ADC.o (.text.User_ADC_Init)
                  00005738    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00005784    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  000057d0    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  0000581c    0000004c     BSP_Spwm.o (.text.TIMG8_IRQHandler)
                  00005868    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  000058b2    00000002     --HOLE-- [fill = 0]
                  000058b4    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  000058fe    00000002     --HOLE-- [fill = 0]
                  00005900    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  00005948    00000048     main.o (.text.Init_All)
                  00005990    00000048     User_ADC.o (.text.Set_Fs)
                  000059d8    00000046     EPD_GUI.o (.text.EPD_ShowNum)
                  00005a1e    00000002     --HOLE-- [fill = 0]
                  00005a20    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00005a64    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00005aa4    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00005ae4    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00005b24    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00005b64    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00005ba4    0000003c     EPD.o (.text.EPD_Clear_R26H)
                  00005be0    0000003c     EPD.o (.text.EPD_HW_RESET)
                  00005c1c    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00005c58    0000003c                            : comparesf2.S.obj (.text.__gtsf2)
                  00005c94    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00005cd0    0000003a     libclang_rt.builtins.a : comparesf2.S.obj (.text.__eqsf2)
                  00005d0a    00000002     --HOLE-- [fill = 0]
                  00005d0c    0000003a                            : muldsi3.S.obj (.text.__muldsi3)
                  00005d46    00000002     --HOLE-- [fill = 0]
                  00005d48    00000038     ti_msp_dl_config.o (.text.DL_Timer_setPublisherChanID)
                  00005d80    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00005db8    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00005df0    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00005e24    00000030     User_ADC.o (.text.DL_DMA_setTransferSize)
                  00005e54    00000030     ti_msp_dl_config.o (.text.DL_DMA_setTransferSize)
                  00005e84    00000030     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutputFeatures)
                  00005eb4    00000030     SPI_Init.o (.text.EPD_WR_DATA8)
                  00005ee4    00000030     SPI_Init.o (.text.EPD_WR_REG)
                  00005f14    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00005f44    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00005f74    0000002c     User_ADC.o (.text.ADC0_IRQHandler)
                  00005fa0    0000002c     ti_msp_dl_config.o (.text.DL_ADC12_setDMASamplesCnt)
                  00005fcc    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH0_init)
                  00005ff8    0000002c     User_ADC.o (.text.__NVIC_EnableIRQ)
                  00006024    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00006050    0000002a     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  0000607a    0000002a     User_FFT.o (.text.c_plus)
                  000060a4    0000002a     User_FFT.o (.text.c_sub)
                  000060ce    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  000060f6    00000002     --HOLE-- [fill = 0]
                  000060f8    00000028     User_ADC.o (.text.DL_DMA_setDestAddr)
                  00006120    00000028     User_ADC.o (.text.DL_DMA_setSrcAddr)
                  00006148    00000028     ti_msp_dl_config.o (.text.DL_Timer_enableEvent)
                  00006170    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00006198    00000026     User_ADC.o (.text.DL_DMA_enableChannel)
                  000061be    00000002     --HOLE-- [fill = 0]
                  000061c0    00000024     libclang_rt.builtins.a : muldi3.S.obj (.text.__muldi3)
                  000061e4    00000022     Delay.o (.text.delay_ms)
                  00006206    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00006228    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00006248    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  00006266    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00006284    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  000062a2    00000002     --HOLE-- [fill = 0]
                  000062a4    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_clearInterruptStatus)
                  000062c0    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableDMA)
                  000062dc    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableDMATrigger)
                  000062f8    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableInterrupt)
                  00006314    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  00006330    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  0000634c    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00006368    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00006384    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setMCLKDivider)
                  000063a0    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  000063bc    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  000063d8    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  000063f4    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00006410    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  0000642c    0000001c     EPD.o (.text.EPD_READBUSY)
                  00006448    0000001a     ti_msp_dl_config.o (.text.DL_ADC12_setSubscriberChanID)
                  00006462    0000001a     EPD.o (.text.EPD_FastUpdate)
                  0000647c    0000001a     EPD.o (.text.EPD_PartUpdate)
                  00006496    00000002     --HOLE-- [fill = 0]
                  00006498    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  000064b0    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  000064c8    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  000064e0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000064f8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  00006510    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00006528    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00006540    00000018     BSP_4x4KEY.o (.text.DL_GPIO_setPins)
                  00006558    00000018     EPD.o (.text.DL_GPIO_setPins)
                  00006570    00000018     SPI_Init.o (.text.DL_GPIO_setPins)
                  00006588    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  000065a0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000065b8    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000065d0    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000065e8    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  00006600    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00006618    00000018     User_ADC.o (.text.DL_Timer_setLoadValue)
                  00006630    00000018     User_ADC.o (.text.DL_Timer_startCounter)
                  00006648    00000018     User_ADC.o (.text.DL_Timer_stopCounter)
                  00006660    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00006678    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00006690    00000018     libc.a : sprintf.c.obj (.text._outs)
                  000066a8    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000066be    00000016     BSP_4x4KEY.o (.text.DL_GPIO_readPins)
                  000066d4    00000016     EPD.o (.text.DL_GPIO_readPins)
                  000066ea    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00006700    00000016     libc.a : k_rem_pio2.c.obj (.text.OUTLINED_FUNCTION_0)
                  00006716    00000002     --HOLE-- [fill = 0]
                  00006718    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00006728    00000014     BSP_4x4KEY.o (.text.DL_GPIO_clearPins)
                  0000673c    00000014     EPD.o (.text.DL_GPIO_clearPins)
                  00006750    00000014     SPI_Init.o (.text.DL_GPIO_clearPins)
                  00006764    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00006778    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  0000678c    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000067a0    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000067b4    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000067c8    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000067dc    00000012     User_ADC.o (.text.DL_ADC12_getPendingInterrupt)
                  000067ee    00000012     BSP_Spwm.o (.text.DL_Timer_getPendingInterrupt)
                  00006800    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00006812    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00006824    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00006836    00000002     --HOLE-- [fill = 0]
                  00006838    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00006848    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00006858    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00006868    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  00006878    0000000e            : s_cos.c.obj (.text.OUTLINED_FUNCTION_0)
                  00006886    00000002     --HOLE-- [fill = 0]
                  00006888    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00006898    0000000e     libc.a : s_sin.c.obj (.text.OUTLINED_FUNCTION_0)
                  000068a6    0000000e            : k_rem_pio2.c.obj (.text.OUTLINED_FUNCTION_2)
                  000068b4    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  000068c2    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  000068d0    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  000068de    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  000068e8    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  000068f2    0000000a            : e_pow.c.obj (.text.OUTLINED_FUNCTION_0)
                  000068fc    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  0000690c    0000000a     libc.a : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                  00006916    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00006920    0000000a            : e_pow.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000692a    0000000a            : k_rem_pio2.c.obj (.text.OUTLINED_FUNCTION_1)
                  00006934    0000000a            : s_cos.c.obj (.text.OUTLINED_FUNCTION_6)
                  0000693e    00000002     --HOLE-- [fill = 0]
                  00006940    00000010            : k_sin.c.obj (.tramp.__kernel_sin.1)
                  00006950    0000000a            : sprintf.c.obj (.text._outc)
                  0000695a    00000008            : e_pow.c.obj (.text.OUTLINED_FUNCTION_2)
                  00006962    00000008            : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                  0000696a    00000008     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00006972    00000002     --HOLE-- [fill = 0]
                  00006974    00000008     libc.a : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  0000697c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00006984    00000006     libc.a : k_cos.c.obj (.text.OUTLINED_FUNCTION_0)
                  0000698a    00000006            : k_sin.c.obj (.text.OUTLINED_FUNCTION_0)
                  00006990    00000006            : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                  00006996    00000006            : s_cos.c.obj (.text.OUTLINED_FUNCTION_1)
                  0000699c    00000006            : s_sin.c.obj (.text.OUTLINED_FUNCTION_1)
                  000069a2    00000006            : e_pow.c.obj (.text.OUTLINED_FUNCTION_3)
                  000069a8    00000006            : s_cos.c.obj (.text.OUTLINED_FUNCTION_3)
                  000069ae    00000006            : s_sin.c.obj (.text.OUTLINED_FUNCTION_3)
                  000069b4    00000006            : e_pow.c.obj (.text.OUTLINED_FUNCTION_5)
                  000069ba    00000006            : s_cos.c.obj (.text.OUTLINED_FUNCTION_5)
                  000069c0    00000006            : s_sin.c.obj (.text.OUTLINED_FUNCTION_5)
                  000069c6    00000006            : e_pow.c.obj (.text.OUTLINED_FUNCTION_6)
                  000069cc    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  000069d0    00000004     libc.a : s_cos.c.obj (.text.OUTLINED_FUNCTION_2)
                  000069d4    00000004            : s_sin.c.obj (.text.OUTLINED_FUNCTION_2)
                  000069d8    00000004            : e_pow.c.obj (.text.OUTLINED_FUNCTION_4)
                  000069dc    00000004            : s_cos.c.obj (.text.OUTLINED_FUNCTION_4)
                  000069e0    00000004            : s_sin.c.obj (.text.OUTLINED_FUNCTION_4)
                  000069e4    00000004            : e_pow.c.obj (.text.OUTLINED_FUNCTION_7)
                  000069e8    00000004            : e_pow.c.obj (.text.OUTLINED_FUNCTION_8)
                  000069ec    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  000069f0    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00006a00    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00006a04    00000004            : exit.c.obj (.text:abort)
                  00006a08    00000008     --HOLE-- [fill = 0]

.cinit     0    000073e0    00000030     
                  000073e0    0000000c     (__TI_handler_table)
                  000073ec    0000000b     (.cinit..data.load) [load image, compression = lzss]
                  000073f7    00000001     --HOLE-- [fill = 0]
                  000073f8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  00007400    00000010     (__TI_cinit_table)

.rodata    0    00006a10    000009d0     
                  00006a10    000005f0     EPD_GUI.o (.rodata.asc2_1608)
                  00007000    00000108     libc.a : k_rem_pio2.c.obj (.rodata.ipio2)
                  00007108    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00007110    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  00007211    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00007214    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00007217    00000001     --HOLE-- [fill = 0]
                  00007218    00000040     libc.a : k_rem_pio2.c.obj (.rodata.PIo2)
                  00007258    00000040            : s_atan.c.obj (.rodata.cst32)
                  00007298    00000030            : e_pow.c.obj (.rodata.cst16)
                  000072c8    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH0Config)
                  000072e0    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  000072f4    00000014     EPD_GUI.o (.rodata.str1.160496125614851016371)
                  00007308    00000011     libc.a : _printfi.c.obj (.rodata.str1.11645776875810915891)
                  00007319    00000011     main.o (.rodata.str1.120848099768473620991)
                  0000732a    00000011     main.o (.rodata.str1.122786433597999586301)
                  0000733b    00000011     libc.a : _printfi.c.obj (.rodata.str1.44690500295887128011)
                  0000734c    00000011     main.o (.rodata.str1.57136986666369238191)
                  0000735d    00000011     main.o (.rodata.str1.93435692222562013921)
                  0000736e    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  00007370    00000010     libc.a : k_rem_pio2.c.obj (.rodata.cst16)
                  00007380    0000000f     main.o (.rodata.str1.18934544870302961421)
                  0000738f    0000000d     main.o (.rodata.str1.179480043780824248221)
                  0000739c    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  000073a6    0000000a     main.o (.rodata.str1.182859457380636363961)
                  000073b0    0000000a     main.o (.rodata.str1.74852076718367031741)
                  000073ba    0000000a     main.o (.rodata.str1.80750631935809179731)
                  000073c4    0000000a     main.o (.rodata.str1.89219620533961422581)
                  000073ce    00000009     main.o (.rodata.str1.123866747220708346941)
                  000073d7    00000001     --HOLE-- [fill = 0]
                  000073d8    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00001224     UNINITIALIZED
                  20200000    00000b48     (.common:ImageBW)
                  20200b48    00000400     (.common:buff1)
                  20200f48    00000200     (.common:gADCSamples)
                  20201148    000000bc     (.common:gTIMER_0Backup)
                  20201204    00000014     (.common:Paint)
                  20201218    00000004     (.common:Vol)
                  2020121c    00000004     (.common:fre)
                  20201220    00000004     (.common:fs)

.data      0    20201224    000008d1     UNINITIALIZED
                  20201224    00000800     User_FFT.o (.data.FFT_Data)
                  20201a24    000000c8     BSP_Spwm.o (.data.Pwm_Data)
                  20201aec    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20201af0    00000002     main.o (.data.cnt)
                  20201af2    00000001     BSP_Spwm.o (.data.Spwm_cnt)
                  20201af3    00000001     User_ADC.o (.data.adc_flag)
                  20201af4    00000001     main.o (.data.key_val)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             2522    78        188    
    +--+------------------------------+-------+---------+---------+
       Total:                         2522    78        188    
                                                               
    .\App\
       User_FFT.o                     1300    0         2048   
       User_ADC.o                     652     0         513    
    +--+------------------------------+-------+---------+---------+
       Total:                         1952    0         2561   
                                                               
    .\BSP\
       BSP_4x4KEY.o                   966     0         0      
       BSP_Spwm.o                     94      0         201    
    +--+------------------------------+-------+---------+---------+
       Total:                         1060    0         201    
                                                               
    .\EPD\
       EPD_GUI.o                      1132    1540      20     
       EPD.o                          606     0         0      
       SPI_Init.o                     272     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2010    1540      20     
                                                               
    .\System\
       Delay.o                        34      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         34      0         0      
                                                               
    .\User\
       main.o                         896     145       3927   
       startup_mspm0g350x_ticlang.o   8       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         904     337       3927   
                                                               
    C:/ti/mspm0_sdk_2_00_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     524     0         0      
       dl_uart.o                      90      0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         764     0         0      
                                                               
    C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4558    34        0      
       e_pow.c.obj                    2730    48        0      
       k_rem_pio2.c.obj               1670    344       0      
       s_sin.c.obj                    1152    0         0      
       s_cos.c.obj                    1138    0         0      
       s_atan.c.obj                   784     64        0      
       e_atan2.c.obj                  408     0         0      
       e_sqrt.c.obj                   388     0         0      
       k_sin.c.obj                    382     0         0      
       k_cos.c.obj                    342     0         0      
       s_floor.c.obj                  324     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     120     0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         14794   747       4      
                                                               
    C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang/15.0.7/lib/armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       addsf3.S.obj                   216     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       comparesf2.S.obj               118     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_fcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       floatsidf.S.obj                44      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2874    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       47        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   26914   2749      7413   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 00007400 records: 2, size/record: 8, table size: 16
	.data: load addr=000073ec, load size=0000000b bytes, run addr=20201224, run size=000008d1 bytes, compression=lzss
	.bss: load addr=000073f8, load size=00000008 bytes, run addr=20200000, run size=00001224 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000073e0 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   000033c7     00006718     00006714   libc.a : k_rem_pio2.c.obj (.text.OUTLINED_FUNCTION_0)
                             00006932          : k_rem_pio2.c.obj (.text.OUTLINED_FUNCTION_1)
                             00006994          : s_atan.c.obj (.text.OUTLINED_FUNCTION_1)
                             0000699a          : s_cos.c.obj (.text.OUTLINED_FUNCTION_1)
                             000069a0          : s_sin.c.obj (.text.OUTLINED_FUNCTION_1)
                             000069b8          : e_pow.c.obj (.text.OUTLINED_FUNCTION_5)
                             000069d2          : s_cos.c.obj (.text.OUTLINED_FUNCTION_2)
                             000069d6          : s_sin.c.obj (.text.OUTLINED_FUNCTION_2)
                             000069da          : e_pow.c.obj (.text.OUTLINED_FUNCTION_4)
                             000069ea          : e_pow.c.obj (.text.OUTLINED_FUNCTION_8)
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   000033bd     00006888     00006884   libc.a : s_cos.c.obj (.text.OUTLINED_FUNCTION_0)
                             000068a4          : s_sin.c.obj (.text.OUTLINED_FUNCTION_0)
                             000068f0          : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                             00006960          : e_pow.c.obj (.text.OUTLINED_FUNCTION_2)
                             000069be          : s_cos.c.obj (.text.OUTLINED_FUNCTION_5)
                             000069c4          : s_sin.c.obj (.text.OUTLINED_FUNCTION_5)
                             000069de          : s_cos.c.obj (.text.OUTLINED_FUNCTION_4)
                             000069e2          : s_sin.c.obj (.text.OUTLINED_FUNCTION_4)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00004425     000068fc     000068fa   libc.a : e_pow.c.obj (.text.OUTLINED_FUNCTION_0)
                             00006914          : s_atan.c.obj (.text.OUTLINED_FUNCTION_0)
                             00006928          : e_pow.c.obj (.text.OUTLINED_FUNCTION_1)
                             00006968          : s_atan.c.obj (.text.OUTLINED_FUNCTION_2)
                             00006988          : k_cos.c.obj (.text.OUTLINED_FUNCTION_0)
                             0000698e          : k_sin.c.obj (.text.OUTLINED_FUNCTION_0)
                             000069a6          : e_pow.c.obj (.text.OUTLINED_FUNCTION_3)
                             000069ac          : s_cos.c.obj (.text.OUTLINED_FUNCTION_3)
                             000069b2          : s_sin.c.obj (.text.OUTLINED_FUNCTION_3)
                             000069ca          : e_pow.c.obj (.text.OUTLINED_FUNCTION_6)
                             000069e6          : e_pow.c.obj (.text.OUTLINED_FUNCTION_7)
__kernel_sin              $Tramp$TT$L$PI$$__kernel_sin
   00003865     00006940     0000693c   libc.a : s_cos.c.obj (.text.OUTLINED_FUNCTION_6)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00006171     000069f0     000069ec   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[5 trampolines]
[31 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
00005f75  ADC0_IRQHandler                 
000069cd  ADC1_IRQHandler                 
000069cd  AES_IRQHandler                  
00006a04  C$$EXIT                         
000069cd  CANFD0_IRQHandler               
000069cd  DAC0_IRQHandler                 
00005a65  DL_ADC12_setClockConfig         
000068df  DL_Common_delayCycles           
00005785  DL_DMA_initChannel              
00004791  DL_Timer_initPWMMode            
0000433d  DL_Timer_initTimerMode          
000063f5  DL_Timer_setCaptCompUpdateMethod
00006601  DL_Timer_setCaptureCompareOutCtl
00006849  DL_Timer_setCaptureCompareValue 
00006411  DL_Timer_setClockConfig         
00005901  DL_UART_init                    
00006801  DL_UART_setClockConfig          
000069cd  DMA_IRQHandler                  
000069cd  Default_Handler                 
00004ccd  EPD_ClearAll                    
00005ba5  EPD_Clear_R26H                  
00004d49  EPD_Display                     
00004eb1  EPD_Display_Clear               
0000515d  EPD_FastMode2Init               
00006463  EPD_FastUpdate                  
00005be1  EPD_HW_RESET                    
0000647d  EPD_PartUpdate                  
0000642d  EPD_READBUSY                    
000039cd  EPD_ShowChar                    
000059d9  EPD_ShowNum                     
00005479  EPD_ShowString                  
00004b45  EPD_WR_Bus                      
00005eb5  EPD_WR_DATA8                    
00005ee5  EPD_WR_REG                      
20201224  FFT_Data                        
000069cd  GROUP0_IRQHandler               
000069cd  GROUP1_IRQHandler               
000048f9  Get_AC_Vol                      
000069cd  HardFault_Handler               
000069cd  I2C0_IRQHandler                 
000069cd  I2C1_IRQHandler                 
20200000  ImageBW                         
00005949  Init_All                        
000069cd  NMI_Handler                     
20201204  Paint                           
00005085  Paint_Clear                     
00004995  Paint_NewImage                  
00004249  Paint_SetPixel                  
000069cd  PendSV_Handler                  
20201a24  Pwm_Data                        
000069cd  RTC_IRQHandler                  
00002421  Read4X4KEY                      
000069ed  Reset_Handler                   
000069cd  SPI0_IRQHandler                 
000069cd  SPI1_IRQHandler                 
000069cd  SVC_Handler                     
00004a29  SYSCFG_DL_ADC12_0_init          
00005fcd  SYSCFG_DL_DMA_CH0_init          
0000696b  SYSCFG_DL_DMA_init              
00003551  SYSCFG_DL_GPIO_init             
0000522d  SYSCFG_DL_PWM_0_init            
00006051  SYSCFG_DL_SYSCTL_init           
00005a21  SYSCFG_DL_TIMER_0_init          
00005aa5  SYSCFG_DL_UART_0_init           
00005f15  SYSCFG_DL_init                  
00004dc1  SYSCFG_DL_initPower             
00005991  Set_Fs                          
00004fa5  Show_menu                       
20201af2  Spwm_cnt                        
000069cd  SysTick_Handler                 
000069cd  TIMA0_IRQHandler                
000069cd  TIMA1_IRQHandler                
000069cd  TIMG0_IRQHandler                
000069cd  TIMG12_IRQHandler               
000069cd  TIMG6_IRQHandler                
000069cd  TIMG7_IRQHandler                
0000581d  TIMG8_IRQHandler                
00006813  TI_memcpy_small                 
000068d1  TI_memset_small                 
000069cd  UART0_IRQHandler                
000069cd  UART1_IRQHandler                
000069cd  UART2_IRQHandler                
000069cd  UART3_IRQHandler                
000056e9  User_ADC_Init                   
00003079  User_GetSpectrum                
20201218  Vol                             
20208000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00007400  __TI_CINIT_Base                 
00007410  __TI_CINIT_Limit                
00007410  __TI_CINIT_Warm                 
000073e0  __TI_Handler_Table_Base         
000073ec  __TI_Handler_Table_Limit        
00005c95  __TI_auto_init_nobinit_nopinit  
00004e39  __TI_decompress_lzss            
00006825  __TI_decompress_none            
00005591  __TI_ltoa                       
ffffffff  __TI_pprof_out_hndl             
00000b31  __TI_printfi                    
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00006869  __TI_zero_init                  
000033c7  __adddf3                        
000046c3  __addsf3                        
00007110  __aeabi_ctype_table_            
00007110  __aeabi_ctype_table_C           
00004f31  __aeabi_d2f                     
000058b5  __aeabi_d2iz                    
000033c7  __aeabi_dadd                    
00005291  __aeabi_dcmpeq                  
000052cd  __aeabi_dcmpge                  
000052e1  __aeabi_dcmpgt                  
000052b9  __aeabi_dcmple                  
000052a5  __aeabi_dcmplt                  
0000413d  __aeabi_ddiv                    
00004425  __aeabi_dmul                    
000033bd  __aeabi_dsub                    
20201aec  __aeabi_errno                   
00006975  __aeabi_errno_addr              
00005b25  __aeabi_f2d                     
00005d81  __aeabi_f2iz                    
000046c3  __aeabi_fadd                    
000052f5  __aeabi_fcmpeq                  
00005331  __aeabi_fcmpge                  
00005345  __aeabi_fcmpgt                  
0000531d  __aeabi_fcmple                  
00005309  __aeabi_fcmplt                  
00004bc9  __aeabi_fdiv                    
00004ab9  __aeabi_fmul                    
000046b9  __aeabi_fsub                    
00006025  __aeabi_i2d                     
00005c1d  __aeabi_i2f                     
00005641  __aeabi_idiv                    
00002e9b  __aeabi_idiv0                   
00005641  __aeabi_idivmod                 
0000354f  __aeabi_ldiv0                   
00006285  __aeabi_llsl                    
000061c1  __aeabi_lmul                    
00004f25  __aeabi_memclr                  
00004f25  __aeabi_memclr4                 
00004f25  __aeabi_memclr8                 
0000697d  __aeabi_memcpy                  
0000697d  __aeabi_memcpy4                 
0000697d  __aeabi_memcpy8                 
000068b5  __aeabi_memset                  
000068b5  __aeabi_memset4                 
000068b5  __aeabi_memset8                 
00005ae5  __aeabi_uidiv                   
00005ae5  __aeabi_uidivmod                
000067b5  __aeabi_uldivmod                
00006285  __ashldi3                       
ffffffff  __binit__                       
000050f1  __cmpdf2                        
00005cd1  __cmpsf2                        
0000413d  __divdf3                        
00004bc9  __divsf3                        
000050f1  __eqdf2                         
00005cd1  __eqsf2                         
00005b25  __extendsfdf2                   
000058b5  __fixdfsi                       
00005d81  __fixsfsi                       
00006025  __floatsidf                     
00005c1d  __floatsisf                     
00005015  __gedf2                         
00005c59  __gesf2                         
00005015  __gtdf2                         
00005c59  __gtsf2                         
00003b25  __kernel_cos                    
00001531  __kernel_rem_pio2               
00003865  __kernel_sin                    
000050f1  __ledf2                         
00005cd1  __lesf2                         
000050f1  __ltdf2                         
00005cd1  __ltsf2                         
UNDEFED   __mpu_init                      
00004425  __muldf3                        
000061c1  __muldi3                        
00005d0d  __muldsi3                       
00004ab9  __mulsf3                        
000050f1  __nedf2                         
00005cd1  __nesf2                         
20207e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
000033bd  __subdf3                        
000046b9  __subsf3                        
00004f31  __truncdfsf2                    
00004855  __udivmoddi4                    
00006171  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
00006a01  _system_pre_init                
00006a05  abort                           
20201af3  adc_flag                        
00006a10  asc2_1608                       
000027a5  atan                            
00003225  atan2                           
00003225  atan2l                          
000027a5  atanl                           
00005b65  atoi                            
ffffffff  binit                           
20200b48  buff1                           
20201af0  cnt                             
00001fe1  cos                             
00001fe1  cosl                            
000061e5  delay_ms                        
00002cbd  fft                             
00003c75  floor                           
00003c75  floorl                          
2020121c  fre                             
00005535  frexp                           
00005535  frexpl                          
20201220  fs                              
20200f48  gADCSamples                     
20201148  gTIMER_0Backup                  
00000000  interruptVectors                
20201af4  key_val                         
000045e1  ldexp                           
000045e1  ldexpl                          
00004c4d  main                            
00006207  memccpy                         
000000c1  pow                             
000000c1  powl                            
000045e1  scalbn                          
000045e1  scalbnl                         
00001b89  sin                             
00001b89  sinl                            
00005db9  sprintf                         
000036e1  sqrt                            
000036e1  sqrtl                           
00006859  wcslen                          


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  pow                             
000000c1  powl                            
00000200  __STACK_SIZE                    
00000b31  __TI_printfi                    
00001531  __kernel_rem_pio2               
00001b89  sin                             
00001b89  sinl                            
00001fe1  cos                             
00001fe1  cosl                            
00002421  Read4X4KEY                      
000027a5  atan                            
000027a5  atanl                           
00002cbd  fft                             
00002e9b  __aeabi_idiv0                   
00003079  User_GetSpectrum                
00003225  atan2                           
00003225  atan2l                          
000033bd  __aeabi_dsub                    
000033bd  __subdf3                        
000033c7  __adddf3                        
000033c7  __aeabi_dadd                    
0000354f  __aeabi_ldiv0                   
00003551  SYSCFG_DL_GPIO_init             
000036e1  sqrt                            
000036e1  sqrtl                           
00003865  __kernel_sin                    
000039cd  EPD_ShowChar                    
00003b25  __kernel_cos                    
00003c75  floor                           
00003c75  floorl                          
0000413d  __aeabi_ddiv                    
0000413d  __divdf3                        
00004249  Paint_SetPixel                  
0000433d  DL_Timer_initTimerMode          
00004425  __aeabi_dmul                    
00004425  __muldf3                        
000045e1  ldexp                           
000045e1  ldexpl                          
000045e1  scalbn                          
000045e1  scalbnl                         
000046b9  __aeabi_fsub                    
000046b9  __subsf3                        
000046c3  __addsf3                        
000046c3  __aeabi_fadd                    
00004791  DL_Timer_initPWMMode            
00004855  __udivmoddi4                    
000048f9  Get_AC_Vol                      
00004995  Paint_NewImage                  
00004a29  SYSCFG_DL_ADC12_0_init          
00004ab9  __aeabi_fmul                    
00004ab9  __mulsf3                        
00004b45  EPD_WR_Bus                      
00004bc9  __aeabi_fdiv                    
00004bc9  __divsf3                        
00004c4d  main                            
00004ccd  EPD_ClearAll                    
00004d49  EPD_Display                     
00004dc1  SYSCFG_DL_initPower             
00004e39  __TI_decompress_lzss            
00004eb1  EPD_Display_Clear               
00004f25  __aeabi_memclr                  
00004f25  __aeabi_memclr4                 
00004f25  __aeabi_memclr8                 
00004f31  __aeabi_d2f                     
00004f31  __truncdfsf2                    
00004fa5  Show_menu                       
00005015  __gedf2                         
00005015  __gtdf2                         
00005085  Paint_Clear                     
000050f1  __cmpdf2                        
000050f1  __eqdf2                         
000050f1  __ledf2                         
000050f1  __ltdf2                         
000050f1  __nedf2                         
0000515d  EPD_FastMode2Init               
0000522d  SYSCFG_DL_PWM_0_init            
00005291  __aeabi_dcmpeq                  
000052a5  __aeabi_dcmplt                  
000052b9  __aeabi_dcmple                  
000052cd  __aeabi_dcmpge                  
000052e1  __aeabi_dcmpgt                  
000052f5  __aeabi_fcmpeq                  
00005309  __aeabi_fcmplt                  
0000531d  __aeabi_fcmple                  
00005331  __aeabi_fcmpge                  
00005345  __aeabi_fcmpgt                  
00005479  EPD_ShowString                  
00005535  frexp                           
00005535  frexpl                          
00005591  __TI_ltoa                       
00005641  __aeabi_idiv                    
00005641  __aeabi_idivmod                 
000056e9  User_ADC_Init                   
00005785  DL_DMA_initChannel              
0000581d  TIMG8_IRQHandler                
000058b5  __aeabi_d2iz                    
000058b5  __fixdfsi                       
00005901  DL_UART_init                    
00005949  Init_All                        
00005991  Set_Fs                          
000059d9  EPD_ShowNum                     
00005a21  SYSCFG_DL_TIMER_0_init          
00005a65  DL_ADC12_setClockConfig         
00005aa5  SYSCFG_DL_UART_0_init           
00005ae5  __aeabi_uidiv                   
00005ae5  __aeabi_uidivmod                
00005b25  __aeabi_f2d                     
00005b25  __extendsfdf2                   
00005b65  atoi                            
00005ba5  EPD_Clear_R26H                  
00005be1  EPD_HW_RESET                    
00005c1d  __aeabi_i2f                     
00005c1d  __floatsisf                     
00005c59  __gesf2                         
00005c59  __gtsf2                         
00005c95  __TI_auto_init_nobinit_nopinit  
00005cd1  __cmpsf2                        
00005cd1  __eqsf2                         
00005cd1  __lesf2                         
00005cd1  __ltsf2                         
00005cd1  __nesf2                         
00005d0d  __muldsi3                       
00005d81  __aeabi_f2iz                    
00005d81  __fixsfsi                       
00005db9  sprintf                         
00005eb5  EPD_WR_DATA8                    
00005ee5  EPD_WR_REG                      
00005f15  SYSCFG_DL_init                  
00005f75  ADC0_IRQHandler                 
00005fcd  SYSCFG_DL_DMA_CH0_init          
00006025  __aeabi_i2d                     
00006025  __floatsidf                     
00006051  SYSCFG_DL_SYSCTL_init           
00006171  _c_int00_noargs                 
000061c1  __aeabi_lmul                    
000061c1  __muldi3                        
000061e5  delay_ms                        
00006207  memccpy                         
00006285  __aeabi_llsl                    
00006285  __ashldi3                       
000063f5  DL_Timer_setCaptCompUpdateMethod
00006411  DL_Timer_setClockConfig         
0000642d  EPD_READBUSY                    
00006463  EPD_FastUpdate                  
0000647d  EPD_PartUpdate                  
00006601  DL_Timer_setCaptureCompareOutCtl
000067b5  __aeabi_uldivmod                
00006801  DL_UART_setClockConfig          
00006813  TI_memcpy_small                 
00006825  __TI_decompress_none            
00006849  DL_Timer_setCaptureCompareValue 
00006859  wcslen                          
00006869  __TI_zero_init                  
000068b5  __aeabi_memset                  
000068b5  __aeabi_memset4                 
000068b5  __aeabi_memset8                 
000068d1  TI_memset_small                 
000068df  DL_Common_delayCycles           
0000696b  SYSCFG_DL_DMA_init              
00006975  __aeabi_errno_addr              
0000697d  __aeabi_memcpy                  
0000697d  __aeabi_memcpy4                 
0000697d  __aeabi_memcpy8                 
000069cd  ADC1_IRQHandler                 
000069cd  AES_IRQHandler                  
000069cd  CANFD0_IRQHandler               
000069cd  DAC0_IRQHandler                 
000069cd  DMA_IRQHandler                  
000069cd  Default_Handler                 
000069cd  GROUP0_IRQHandler               
000069cd  GROUP1_IRQHandler               
000069cd  HardFault_Handler               
000069cd  I2C0_IRQHandler                 
000069cd  I2C1_IRQHandler                 
000069cd  NMI_Handler                     
000069cd  PendSV_Handler                  
000069cd  RTC_IRQHandler                  
000069cd  SPI0_IRQHandler                 
000069cd  SPI1_IRQHandler                 
000069cd  SVC_Handler                     
000069cd  SysTick_Handler                 
000069cd  TIMA0_IRQHandler                
000069cd  TIMA1_IRQHandler                
000069cd  TIMG0_IRQHandler                
000069cd  TIMG12_IRQHandler               
000069cd  TIMG6_IRQHandler                
000069cd  TIMG7_IRQHandler                
000069cd  UART0_IRQHandler                
000069cd  UART1_IRQHandler                
000069cd  UART2_IRQHandler                
000069cd  UART3_IRQHandler                
000069ed  Reset_Handler                   
00006a01  _system_pre_init                
00006a04  C$$EXIT                         
00006a05  abort                           
00006a10  asc2_1608                       
00007110  __aeabi_ctype_table_            
00007110  __aeabi_ctype_table_C           
000073e0  __TI_Handler_Table_Base         
000073ec  __TI_Handler_Table_Limit        
00007400  __TI_CINIT_Base                 
00007410  __TI_CINIT_Limit                
00007410  __TI_CINIT_Warm                 
20200000  ImageBW                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200b48  buff1                           
20200f48  gADCSamples                     
20201148  gTIMER_0Backup                  
20201204  Paint                           
20201218  Vol                             
2020121c  fre                             
20201220  fs                              
20201224  FFT_Data                        
20201a24  Pwm_Data                        
20201aec  __aeabi_errno                   
20201af0  cnt                             
20201af2  Spwm_cnt                        
20201af3  adc_flag                        
20201af4  key_val                         
20207e00  __stack                         
20208000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[242 symbols]

################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
%.o: ../%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"C:/ti/ccstheia140/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507" -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507/EPD" -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507/System" -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507/App" -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507/OLED" -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507/Debug" -I"C:/ti/mspm0_sdk_2_00_01_00/source/third_party/CMSIS/Core/Include" -I"C:/ti/mspm0_sdk_2_00_01_00/source" -I"C:/ti/mspm0_sdk_2_00_01_00/source/third_party/CMSIS/DSP/lib/ticlang/m0p/arm_cortexM0l_math.a" -I"C:/ti/mspm0_sdk_2_00_01_00/source/third_party/CMSIS/DSP/Include" -gdwarf-3 -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

build-136640319: ../main.syscfg
	@echo 'Building file: "$<"'
	@echo 'Invoking: SysConfig'
	"C:/ti/ccstheia140/ccs/utils/sysconfig_1.20.0/sysconfig_cli.bat" --script "C:/Users/<USER>/workspace_ccstheia/Template_G3507/main.syscfg" -o "." -s "C:/ti/mspm0_sdk_2_00_01_00/.metadata/product.json" --compiler ticlang
	@echo 'Finished building: "$<"'
	@echo ' '

device_linker.cmd: build-136640319 ../main.syscfg
device.opt: build-136640319
device.cmd.genlibs: build-136640319
ti_msp_dl_config.c: build-136640319
ti_msp_dl_config.h: build-136640319
Event.dot: build-136640319

%.o: ./%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"C:/ti/ccstheia140/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507" -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507/EPD" -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507/System" -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507/App" -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507/OLED" -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507/Debug" -I"C:/ti/mspm0_sdk_2_00_01_00/source/third_party/CMSIS/Core/Include" -I"C:/ti/mspm0_sdk_2_00_01_00/source" -I"C:/ti/mspm0_sdk_2_00_01_00/source/third_party/CMSIS/DSP/lib/ticlang/m0p/arm_cortexM0l_math.a" -I"C:/ti/mspm0_sdk_2_00_01_00/source/third_party/CMSIS/DSP/Include" -gdwarf-3 -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '

startup_mspm0g350x_ticlang.o: C:/ti/mspm0_sdk_2_00_01_00/source/ti/devices/msp/m0p/startup_system_files/ticlang/startup_mspm0g350x_ticlang.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"C:/ti/ccstheia140/ccs/tools/compiler/ti-cgt-armllvm_3.2.2.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507" -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507/EPD" -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507/System" -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507/App" -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507/OLED" -I"C:/Users/<USER>/workspace_ccstheia/Template_G3507/Debug" -I"C:/ti/mspm0_sdk_2_00_01_00/source/third_party/CMSIS/Core/Include" -I"C:/ti/mspm0_sdk_2_00_01_00/source" -I"C:/ti/mspm0_sdk_2_00_01_00/source/third_party/CMSIS/DSP/lib/ticlang/m0p/arm_cortexM0l_math.a" -I"C:/ti/mspm0_sdk_2_00_01_00/source/third_party/CMSIS/DSP/Include" -gdwarf-3 -MMD -MP -MF"$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '



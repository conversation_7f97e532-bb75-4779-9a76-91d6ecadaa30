/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3507" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.24.0+4150"}
 */

/**
 * Import the modules used in this configuration.
 */
const ADC12   = scripting.addModule("/ti/driverlib/ADC12", {}, false);
const ADC121  = ADC12.addInstance();
const ADC122  = ADC12.addInstance();
const GPIO    = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1   = GPIO.addInstance();
const GPIO2   = GPIO.addInstance();
const GPIO3   = GPIO.addInstance();
const SYSCTL  = scripting.addModule("/ti/driverlib/SYSCTL");
const SYSTICK = scripting.addModule("/ti/driverlib/SYSTICK");
const TIMER   = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1  = TIMER.addInstance();
const UART    = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1   = UART.addInstance();

/**
 * Write custom configuration values to the imported modules.
 */
ADC121.$name                             = "ADC12_0";
ADC121.repeatMode                        = true;
ADC121.trigSrc                           = "DL_ADC12_TRIG_SRC_EVENT";
ADC121.powerDownMode                     = "DL_ADC12_POWER_DOWN_MODE_MANUAL";
ADC121.sampleTime0                       = "62.5ns";
ADC121.enabledInterrupts                 = ["DL_ADC12_INTERRUPT_DMA_DONE"];
ADC121.configureDMA                      = true;
ADC121.sampCnt                           = 1;
ADC121.enabledDMATriggers                = ["DL_ADC12_DMA_MEM0_RESULT_LOADED"];
ADC121.subChanID                         = 1;
ADC121.adcMem0trig                       = "DL_ADC12_TRIGGER_MODE_TRIGGER_NEXT";
ADC121.adcMem0chansel                    = "DL_ADC12_INPUT_CHAN_3";
ADC121.DMA_CHANNEL.$name                 = "DMA_CH0";
ADC121.DMA_CHANNEL.addressMode           = "f2b";
ADC121.DMA_CHANNEL.srcLength             = "HALF_WORD";
ADC121.DMA_CHANNEL.dstLength             = "HALF_WORD";
ADC121.DMA_CHANNEL.configureTransferSize = true;
ADC121.DMA_CHANNEL.transferSize          = 1024;
ADC121.DMA_CHANNEL.transferMode          = "FULL_CH_REPEAT_SINGLE";
ADC121.DMA_CHANNEL.peripheral.$assign    = "DMA_CH1";
ADC121.peripheral.$assign                = "ADC0";
ADC121.adcPin3Config.$name               = "ti_driverlib_gpio_GPIOPinGeneric2";

ADC122.$name                             = "ADC12_1";
ADC122.repeatMode                        = true;
ADC122.trigSrc                           = "DL_ADC12_TRIG_SRC_EVENT";
ADC122.adcMem0chansel                    = "DL_ADC12_INPUT_CHAN_3";
ADC122.adcMem0trig                       = "DL_ADC12_TRIGGER_MODE_TRIGGER_NEXT";
ADC122.powerDownMode                     = "DL_ADC12_POWER_DOWN_MODE_MANUAL";
ADC122.sampleTime0                       = "62.5ns";
ADC122.enabledInterrupts                 = ["DL_ADC12_INTERRUPT_DMA_DONE"];
ADC122.configureDMA                      = true;
ADC122.sampCnt                           = 1;
ADC122.enabledDMATriggers                = ["DL_ADC12_DMA_MEM0_RESULT_LOADED"];
ADC122.subChanID                         = 2;
ADC122.adcPin3Config.$name               = "ti_driverlib_gpio_GPIOPinGeneric3";
ADC122.DMA_CHANNEL.$name                 = "DMA_CH1";
ADC122.DMA_CHANNEL.addressMode           = "f2b";
ADC122.DMA_CHANNEL.srcLength             = "HALF_WORD";
ADC122.DMA_CHANNEL.dstLength             = "HALF_WORD";
ADC122.DMA_CHANNEL.transferMode          = "FULL_CH_REPEAT_SINGLE";
ADC122.DMA_CHANNEL.configureTransferSize = true;
ADC122.DMA_CHANNEL.transferSize          = 1024;

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

GPIO1.$name                              = "KeyBoard";
GPIO1.associatedPins.create(8);
GPIO1.associatedPins[0].$name            = "COL1";
GPIO1.associatedPins[0].direction        = "INPUT";
GPIO1.associatedPins[0].assignedPort     = "PORTB";
GPIO1.associatedPins[0].assignedPin      = "12";
GPIO1.associatedPins[0].internalResistor = "PULL_UP";
GPIO1.associatedPins[1].$name            = "COL2";
GPIO1.associatedPins[1].direction        = "INPUT";
GPIO1.associatedPins[1].assignedPort     = "PORTB";
GPIO1.associatedPins[1].assignedPin      = "17";
GPIO1.associatedPins[1].internalResistor = "PULL_UP";
GPIO1.associatedPins[2].$name            = "COL3";
GPIO1.associatedPins[2].direction        = "INPUT";
GPIO1.associatedPins[2].assignedPort     = "PORTB";
GPIO1.associatedPins[2].assignedPin      = "15";
GPIO1.associatedPins[2].internalResistor = "PULL_UP";
GPIO1.associatedPins[3].$name            = "COL4";
GPIO1.associatedPins[3].direction        = "INPUT";
GPIO1.associatedPins[3].assignedPort     = "PORTB";
GPIO1.associatedPins[3].assignedPin      = "8";
GPIO1.associatedPins[3].internalResistor = "PULL_UP";
GPIO1.associatedPins[4].initialValue     = "SET";
GPIO1.associatedPins[4].assignedPort     = "PORTB";
GPIO1.associatedPins[4].assignedPin      = "7";
GPIO1.associatedPins[4].$name            = "ROW1";
GPIO1.associatedPins[5].$name            = "ROW2";
GPIO1.associatedPins[5].initialValue     = "SET";
GPIO1.associatedPins[5].assignedPort     = "PORTB";
GPIO1.associatedPins[5].assignedPin      = "6";
GPIO1.associatedPins[6].$name            = "ROW3";
GPIO1.associatedPins[6].initialValue     = "SET";
GPIO1.associatedPins[6].assignedPort     = "PORTB";
GPIO1.associatedPins[6].assignedPin      = "0";
GPIO1.associatedPins[7].$name            = "ROW4";
GPIO1.associatedPins[7].initialValue     = "SET";
GPIO1.associatedPins[7].assignedPort     = "PORTB";
GPIO1.associatedPins[7].assignedPin      = "16";

GPIO2.$name                          = "LED_pins";
GPIO2.associatedPins[0].assignedPort = "PORTA";
GPIO2.associatedPins[0].assignedPin  = "30";
GPIO2.associatedPins[0].$name        = "LED1";

GPIO3.$name                          = "MYI2C";
GPIO3.port                           = "PORTA";
GPIO3.associatedPins.create(2);
GPIO3.associatedPins[0].$name        = "SCL";
GPIO3.associatedPins[0].initialValue = "SET";
GPIO3.associatedPins[0].ioStructure  = "OD";
GPIO3.associatedPins[0].assignedPin  = "0";
GPIO3.associatedPins[1].$name        = "SDA";
GPIO3.associatedPins[1].initialValue = "SET";
GPIO3.associatedPins[1].ioStructure  = "OD";
GPIO3.associatedPins[1].assignedPin  = "1";

SYSCTL.forceDefaultClkConfig = true;

SYSTICK.periodEnable      = true;
SYSTICK.period            = 32000;
SYSTICK.systickEnable     = true;
SYSTICK.interruptEnable   = true;
SYSTICK.interruptPriority = "0";

TIMER1.$name                       = "TIMER_0";
TIMER1.event1PublisherChannel      = 1;
TIMER1.event1ControllerInterruptEn = ["ZERO_EVENT"];
TIMER1.timerPeriod                 = "1us";
TIMER1.timerStartTimer             = true;
TIMER1.timerMode                   = "PERIODIC_UP";
TIMER1.event2PublisherChannel      = 2;
TIMER1.event2ControllerInterruptEn = ["ZERO_EVENT"];
TIMER1.peripheral.$assign          = "TIMA1";

UART1.$name                    = "UART_0";
UART1.targetBaudRate           = 115200;
UART1.interruptPriority        = "2";
UART1.enabledInterrupts        = ["RX"];
UART1.peripheral.$assign       = "UART0";
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric0";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
ADC121.peripheral.adcPin3.$suggestSolution     = "PA24";
ADC122.peripheral.$suggestSolution             = "ADC1";
ADC122.peripheral.adcPin3.$suggestSolution     = "PA18";
ADC122.DMA_CHANNEL.peripheral.$suggestSolution = "DMA_CH0";
Board.peripheral.$suggestSolution              = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution     = "PA20";
Board.peripheral.swdioPin.$suggestSolution     = "PA19";
GPIO1.associatedPins[0].pin.$suggestSolution   = "PB12";
GPIO1.associatedPins[1].pin.$suggestSolution   = "PB17";
GPIO1.associatedPins[2].pin.$suggestSolution   = "PB15";
GPIO1.associatedPins[3].pin.$suggestSolution   = "PB8";
GPIO1.associatedPins[4].pin.$suggestSolution   = "PB7";
GPIO1.associatedPins[5].pin.$suggestSolution   = "PB6";
GPIO1.associatedPins[6].pin.$suggestSolution   = "PB0";
GPIO1.associatedPins[7].pin.$suggestSolution   = "PB16";
GPIO2.associatedPins[0].pin.$suggestSolution   = "PA30";
GPIO3.associatedPins[0].pin.$suggestSolution   = "PA0";
GPIO3.associatedPins[1].pin.$suggestSolution   = "PA1";
SYSCTL.peripheral.$suggestSolution             = "SYSCTL";

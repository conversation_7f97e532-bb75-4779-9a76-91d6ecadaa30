#include "Driver_adc.h"

uint16_t gADCSamples[SampNum];
bool adc_flag = false;
// ADC1相关变量
uint16_t gADC1Samples[SampNum];
bool adc1_flag = false;


//ADC初始化函数
// 1:采样率；2：采样点数
void User_ADC_Init(float fs)
{
    DL_DMA_setSrcAddr(DMA, DMA_CH0_CHAN_ID,(uint32_t) 0x40556280);
    DL_DMA_setDestAddr(DMA, DMA_CH0_CHAN_ID, (uint32_t) &gADCSamples[0]);
    DL_DMA_setTransferSize(DMA, DMA_CH0_CHAN_ID, SampNum);
    DL_DMA_enableChannel(DMA, DMA_CH0_CHAN_ID);

    // 设置ADC1的DMA - 使用DMA通道0
   
    DL_DMA_setSrcAddr(DMA, DMA_CH1_CHAN_ID, (uint32_t) (DL_ADC12_getMemResultAddress(ADC12_1_INST,ADC12_1_ADCMEM_0)));  // ADC1基地址 + 偏移
    DL_DMA_setDestAddr(DMA, DMA_CH1_CHAN_ID, (uint32_t)&gADC1Samples[0]);
    DL_DMA_setTransferSize(DMA, DMA_CH1_CHAN_ID, SampNum);
    DL_DMA_enableChannel(DMA, DMA_CH1_CHAN_ID);

    Set_Fs(fs);
    NVIC_EnableIRQ(ADC12_0_INST_INT_IRQN);
    NVIC_EnableIRQ(ADC12_1_INST_INT_IRQN);
}

float Set_Fs(float fs)
{
    float real_fs;
    uint16_t cnt;
    cnt = 32000000/fs;
    real_fs = 32000000/cnt;
    DL_TimerA_setLoadValue(TIMER_0_INST,cnt);

    return real_fs;
}

// void Get_AC_Vol(void)
// {
//     uint16_t cnt;
//     // 启动转换
//     DL_TimerA_startCounter(TIMER_0_INST);

//     // 等待转换完成
//     adc_flag = false;
//     while(adc_flag == false);

//     // 数字量转模拟量
//     for( cnt = 0; cnt < SampNum;cnt ++)
//     {
//         FFT_Data[cnt].real = gADCSamples[cnt]*3.3/4096;
//         FFT_Data[cnt].imag = 0;
//     }
// }



volatile uint8_t adc_done_count = 0;

void ADC12_0_INST_IRQHandler(void)
{
    if(++adc_done_count >= 2) 
    {
        DL_TimerA_stopCounter(TIMER_0_INST);  // 立即停止
        adc_done_count = 0;
    }
    adc_flag = true;
}

// ADC1中断处理函数
void ADC12_1_INST_IRQHandler (void)
{
     if(++adc_done_count >= 2) 
    {
        DL_TimerA_stopCounter(TIMER_0_INST);  // 立即停止  
        adc_done_count = 0;
    }
    adc1_flag = true; 
}

void adc_proc(void)
{
    // if(adc_flag)
    // {
    //     DL_TimerA_stopCounter(TIMER_0_INST);
    //     adc_flag = false;
    // }
        
    
}
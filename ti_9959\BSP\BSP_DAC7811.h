/*
 * BSP_DAC7811.h
 *
 *  Created on: 2024年8月19日
 *      Author: <PERSON><PERSON><PERSON>
 */
#ifndef __BSP_DAC7811_H
#define __BSP_DAC7811_H

#include "BSP_SPI.h"

// 命令
#define DAC7811_Power_on   0x0
#define DAC7811_Update     0x1
#define DAC7811_Readback   0x2
#define DAC7811_Daisy_DIS  0x9
#define DAC7811_Daisy_DIS  0x9
#define DAC7811_Clock_Data 0xA 
#define DAC7811_OUT_ZERO   0xB 
#define DAC7811_OUT_MID    0xC

// 函数声明
void DAC7811_Convert(uint16_t Data);

#endif /* __BSP_DAC7811_H */
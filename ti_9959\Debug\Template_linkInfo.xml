<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v3.2.2.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <link_time>0x66b5c419</link_time>
   <link_errors>0x0</link_errors>
   <output_file>C:\Users\<USER>\workspace_ccstheia\Template\Debug\Template.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0x1655</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>C:\Users\<USER>\workspace_ccstheia\Template\Debug\.\</path>
         <kind>object</kind>
         <file>main.o</file>
         <name>main.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>C:\Users\<USER>\workspace_ccstheia\Template\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>C:\Users\<USER>\workspace_ccstheia\Template\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>C:\Users\<USER>\workspace_ccstheia\Template\Debug\.\App\</path>
         <kind>object</kind>
         <file>BSP_4x4KEY.o</file>
         <name>BSP_4x4KEY.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>C:\Users\<USER>\workspace_ccstheia\Template\Debug\.\App\</path>
         <kind>object</kind>
         <file>usart.o</file>
         <name>usart.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>C:\Users\<USER>\workspace_ccstheia\Template\Debug\.\OLED\</path>
         <kind>object</kind>
         <file>oled.o</file>
         <name>oled.o</name>
      </input_file>
      <input_file id="fl-14">
         <path>C:\Users\<USER>\workspace_ccstheia\Template\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-15">
         <path>C:\ti\mspm0_sdk_2_00_01_00\source\third_party\CMSIS\DSP\lib\ticlang\m0p\</path>
         <kind>archive</kind>
         <file>arm_cortexM0l_math.a</file>
         <name>arm_cmplx_mag_f32.o</name>
      </input_file>
      <input_file id="fl-16">
         <path>C:\ti\mspm0_sdk_2_00_01_00\source\third_party\CMSIS\DSP\lib\ticlang\m0p\</path>
         <kind>archive</kind>
         <file>arm_cortexM0l_math.a</file>
         <name>arm_max_f32.o</name>
      </input_file>
      <input_file id="fl-17">
         <path>C:\ti\mspm0_sdk_2_00_01_00\source\third_party\CMSIS\DSP\lib\ticlang\m0p\</path>
         <kind>archive</kind>
         <file>arm_cortexM0l_math.a</file>
         <name>arm_cfft_radix4_f32.o</name>
      </input_file>
      <input_file id="fl-18">
         <path>C:\ti\mspm0_sdk_2_00_01_00\source\third_party\CMSIS\DSP\lib\ticlang\m0p\</path>
         <kind>archive</kind>
         <file>arm_cortexM0l_math.a</file>
         <name>arm_cfft_radix4_init_f32.o</name>
      </input_file>
      <input_file id="fl-19">
         <path>C:\ti\mspm0_sdk_2_00_01_00\source\third_party\CMSIS\DSP\lib\ticlang\m0p\</path>
         <kind>archive</kind>
         <file>arm_cortexM0l_math.a</file>
         <name>arm_common_tables.o</name>
      </input_file>
      <input_file id="fl-1a">
         <path>C:\ti\mspm0_sdk_2_00_01_00\source\third_party\CMSIS\DSP\lib\ticlang\m0p\</path>
         <kind>archive</kind>
         <file>arm_cortexM0l_math.a</file>
         <name>arm_bitreversal.o</name>
      </input_file>
      <input_file id="fl-1b">
         <path>C:\ti\mspm0_sdk_2_00_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_adc12.o</name>
      </input_file>
      <input_file id="fl-1c">
         <path>C:\ti\mspm0_sdk_2_00_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-1d">
         <path>C:\ti\mspm0_sdk_2_00_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_dma.o</name>
      </input_file>
      <input_file id="fl-1e">
         <path>C:\ti\mspm0_sdk_2_00_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_i2c.o</name>
      </input_file>
      <input_file id="fl-1f">
         <path>C:\ti\mspm0_sdk_2_00_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-20">
         <path>C:\ti\mspm0_sdk_2_00_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-2f">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>e_sqrtf.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>e_atan2.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>e_pow.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>e_sqrt.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_atan.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_scalbn.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_portable.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>assert.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>defs.c.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memory.c.obj</name>
      </input_file>
      <input_file id="fl-39">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>strlen.c.obj</name>
      </input_file>
      <input_file id="fl-3a">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-3b">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-3c">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-3d">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-3e">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-3f">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-40">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-41">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-42">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>sprintf.c.obj</name>
      </input_file>
      <input_file id="fl-43">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>fflush.c.obj</name>
      </input_file>
      <input_file id="fl-44">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-45">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-46">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-47">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_printfi.c.obj</name>
      </input_file>
      <input_file id="fl-48">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>s_frexp.c.obj</name>
      </input_file>
      <input_file id="fl-49">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_ltoa.c.obj</name>
      </input_file>
      <input_file id="fl-4a">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>atoi.c.obj</name>
      </input_file>
      <input_file id="fl-4b">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memccpy.c.obj</name>
      </input_file>
      <input_file id="fl-4c">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>wcslen.c.obj</name>
      </input_file>
      <input_file id="fl-4d">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>aeabi_ctype.S.obj</name>
      </input_file>
      <input_file id="fl-b3">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>write.c.obj</name>
      </input_file>
      <input_file id="fl-b4">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>host_device.c.obj</name>
      </input_file>
      <input_file id="fl-b5">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostclose.c.obj</name>
      </input_file>
      <input_file id="fl-b6">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostlseek.c.obj</name>
      </input_file>
      <input_file id="fl-b7">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostopen.c.obj</name>
      </input_file>
      <input_file id="fl-b8">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostread.c.obj</name>
      </input_file>
      <input_file id="fl-b9">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostrename.c.obj</name>
      </input_file>
      <input_file id="fl-ba">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostunlink.c.obj</name>
      </input_file>
      <input_file id="fl-bb">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostwrite.c.obj</name>
      </input_file>
      <input_file id="fl-bc">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>trgmsg.c.obj</name>
      </input_file>
      <input_file id="fl-bd">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>addsf3.S.obj</name>
      </input_file>
      <input_file id="fl-be">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>adddf3.S.obj</name>
      </input_file>
      <input_file id="fl-bf">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldf3.S.obj</name>
      </input_file>
      <input_file id="fl-c0">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldsi3.S.obj</name>
      </input_file>
      <input_file id="fl-c1">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>mulsf3.S.obj</name>
      </input_file>
      <input_file id="fl-c2">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divsf3.S.obj</name>
      </input_file>
      <input_file id="fl-c3">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divdf3.S.obj</name>
      </input_file>
      <input_file id="fl-c4">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>extendsfdf2.S.obj</name>
      </input_file>
      <input_file id="fl-c5">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixdfsi.S.obj</name>
      </input_file>
      <input_file id="fl-c6">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-c7">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsidf.S.obj</name>
      </input_file>
      <input_file id="fl-c8">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-c9">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatunsidf.S.obj</name>
      </input_file>
      <input_file id="fl-ca">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatunsisf.S.obj</name>
      </input_file>
      <input_file id="fl-cb">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>muldi3.S.obj</name>
      </input_file>
      <input_file id="fl-cc">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>truncdfsf2.S.obj</name>
      </input_file>
      <input_file id="fl-cd">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_dcmp.S.obj</name>
      </input_file>
      <input_file id="fl-ce">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_fcmp.S.obj</name>
      </input_file>
      <input_file id="fl-cf">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_idivmod.S.obj</name>
      </input_file>
      <input_file id="fl-d0">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-d1">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-d2">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-d3">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uldivmod.S.obj</name>
      </input_file>
      <input_file id="fl-d4">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparesf2.S.obj</name>
      </input_file>
      <input_file id="fl-d5">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>udivmoddi4.S.obj</name>
      </input_file>
      <input_file id="fl-d6">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>ashldi3.S.obj</name>
      </input_file>
      <input_file id="fl-d7">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>comparedf2.c.obj</name>
      </input_file>
      <input_file id="fl-d8">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang\15.0.7\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-d9">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-da">
         <path>C:\ti\ccstheia140\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.text.Read4X4KEY</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x384</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.I2C0_IRQHandler</name>
         <load_address>0x444</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x444</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-153">
         <name>.text.OLED_ShowChar</name>
         <load_address>0x570</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x570</run_address>
         <size>0x118</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.text.OLED_Init</name>
         <load_address>0x688</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x688</run_address>
         <size>0x100</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.text.DL_Timer_initTimerMode</name>
         <load_address>0x788</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x788</run_address>
         <size>0xe8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.text.OLED_WR_Byte</name>
         <load_address>0x870</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x870</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x94c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x94c</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.text.SYSCFG_DL_ADC12_0_init</name>
         <load_address>0xa28</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa28</run_address>
         <size>0xb4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.text.OLED_ShowChinese</name>
         <load_address>0xadc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xadc</run_address>
         <size>0xa8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-85">
         <name>.text.main</name>
         <load_address>0xb84</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb84</run_address>
         <size>0x98</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0xc1c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc1c</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0xc94</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc94</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.text.Show_menu</name>
         <load_address>0xd0c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd0c</run_address>
         <size>0x74</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.text.OLED_ShowString</name>
         <load_address>0xd80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd80</run_address>
         <size>0x70</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.text.OLED_Clear</name>
         <load_address>0xdf0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xdf0</run_address>
         <size>0x6a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-16e">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0xe5a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe5a</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.text.SYSCFG_DL_I2C_OLED_init</name>
         <load_address>0xe5c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xe5c</run_address>
         <size>0x64</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.text.DL_ADC12_initSeqSample</name>
         <load_address>0xec0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xec0</run_address>
         <size>0x60</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-69">
         <name>.text.DL_I2C_fillControllerTXFIFO</name>
         <load_address>0xf20</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf20</run_address>
         <size>0x5e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-14c">
         <name>.text.DL_I2C_startControllerTransfer</name>
         <load_address>0xf80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xf80</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-168">
         <name>.text.DL_DMA_initChannel</name>
         <load_address>0xfd0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xfd0</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-133">
         <name>.text.DL_UART_setBaudRateDivisor</name>
         <load_address>0x101c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x101c</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13e">
         <name>.text.DL_ADC12_configConversionMem</name>
         <load_address>0x1068</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1068</run_address>
         <size>0x4a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-131">
         <name>.text.DL_UART_init</name>
         <load_address>0x10b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10b4</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.text.SYSCFG_DL_TIMER_0_init</name>
         <load_address>0x10fc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x10fc</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-137">
         <name>.text.DL_ADC12_setClockConfig</name>
         <load_address>0x1140</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1140</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.text.SYSCFG_DL_UART_0_init</name>
         <load_address>0x1180</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1180</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14d">
         <name>.text.__aeabi_uidivmod</name>
         <load_address>0x11c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x11c0</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d2"/>
      </object_component>
      <object_component id="oc-106">
         <name>.text.DL_GPIO_initPeripheralInputFunctionFeatures</name>
         <load_address>0x1200</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1200</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-151">
         <name>.text.OLED_Set_Pos</name>
         <load_address>0x123c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x123c</run_address>
         <size>0x3c</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0x1278</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1278</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.text.DL_Timer_setPublisherChanID</name>
         <load_address>0x12b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x12b4</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.text.DL_GPIO_initDigitalInputFeatures</name>
         <load_address>0x12ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x12ec</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-167">
         <name>.text.DL_DMA_setTransferSize</name>
         <load_address>0x1320</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1320</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.text.DL_GPIO_initDigitalOutputFeatures</name>
         <load_address>0x1350</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1350</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0x1380</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1380</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.ADC0_IRQHandler</name>
         <load_address>0x13b0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13b0</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-143">
         <name>.text.DL_ADC12_setDMASamplesCnt</name>
         <load_address>0x13dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x13dc</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-14a">
         <name>.text.SYSCFG_DL_DMA_CH0_init</name>
         <load_address>0x1408</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1408</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.text.TaskA_Handler</name>
         <load_address>0x1434</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1434</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-af">
         <name>.text.TaskB_Handler</name>
         <load_address>0x1460</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1460</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.text.TaskC_Handler</name>
         <load_address>0x148c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x148c</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x14b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14b8</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-df">
         <name>.text.__NVIC_EnableIRQ</name>
         <load_address>0x14e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x14e4</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x1510</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1510</run_address>
         <size>0x2a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-16d">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x153a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x153a</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-160">
         <name>.text.DL_Common_updateReg</name>
         <load_address>0x1562</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1562</run_address>
         <size>0x28</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.text.DL_DMA_setDestAddr</name>
         <load_address>0x158c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x158c</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.text.DL_DMA_setSrcAddr</name>
         <load_address>0x15b4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15b4</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-126">
         <name>.text.DL_I2C_setControllerRXFIFOThreshold</name>
         <load_address>0x15dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x15dc</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-125">
         <name>.text.DL_I2C_setControllerTXFIFOThreshold</name>
         <load_address>0x1604</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1604</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.text.DL_Timer_enableEvent</name>
         <load_address>0x162c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x162c</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-59">
         <name>.text:_c_int00_noargs</name>
         <load_address>0x1654</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1654</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.text.DL_DMA_enableChannel</name>
         <load_address>0x167c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x167c</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-121">
         <name>.text.DL_I2C_setAnalogGlitchFilterPulseWidth</name>
         <load_address>0x16a2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16a2</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-120">
         <name>.text.DL_I2C_setClockConfig</name>
         <load_address>0x16c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16c8</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-de">
         <name>.text.delay_ms</name>
         <load_address>0x16ee</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x16ee</run_address>
         <size>0x22</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-109">
         <name>.text.DL_GPIO_initPeripheralInputFunction</name>
         <load_address>0x1710</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1710</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-140">
         <name>.text.DL_ADC12_setPowerDownMode</name>
         <load_address>0x1730</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1730</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-132">
         <name>.text.DL_UART_setOversampling</name>
         <load_address>0x174e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x174e</run_address>
         <size>0x1e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-146">
         <name>.text.DL_ADC12_clearInterruptStatus</name>
         <load_address>0x176c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x176c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-142">
         <name>.text.DL_ADC12_enableDMA</name>
         <load_address>0x1788</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1788</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-144">
         <name>.text.DL_ADC12_enableDMATrigger</name>
         <load_address>0x17a4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17a4</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-13f">
         <name>.text.DL_ADC12_enableFIFO</name>
         <load_address>0x17c0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17c0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-147">
         <name>.text.DL_ADC12_enableInterrupt</name>
         <load_address>0x17dc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17dc</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-107">
         <name>.text.DL_GPIO_enableHiZ</name>
         <load_address>0x17f8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x17f8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-108">
         <name>.text.DL_GPIO_initPeripheralOutputFunction</name>
         <load_address>0x1814</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1814</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-66">
         <name>.text.DL_I2C_disableInterrupt</name>
         <load_address>0x1830</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1830</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-128">
         <name>.text.DL_I2C_enableInterrupt</name>
         <load_address>0x184c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x184c</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-67">
         <name>.text.DL_I2C_isControllerRXFIFOEmpty</name>
         <load_address>0x1868</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1868</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-113">
         <name>.text.DL_SYSCTL_setMCLKDivider</name>
         <load_address>0x1884</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1884</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.text.DL_SYSCTL_setSYSOSCFreq</name>
         <load_address>0x18a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18a0</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-112">
         <name>.text.DL_SYSCTL_setULPCLKDivider</name>
         <load_address>0x18bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18bc</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-114">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0x18d8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18d8</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-145">
         <name>.text.DL_ADC12_setSubscriberChanID</name>
         <load_address>0x18f4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x18f4</run_address>
         <size>0x1a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-101">
         <name>.text.DL_ADC12_enablePower</name>
         <load_address>0x1910</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1910</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.text.DL_ADC12_reset</name>
         <load_address>0x1928</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1928</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-141">
         <name>.text.DL_ADC12_setSampleTime0</name>
         <load_address>0x1940</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1940</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.text.DL_GPIO_enableOutput</name>
         <load_address>0x1958</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1958</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.text.DL_GPIO_enablePower</name>
         <load_address>0x1970</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1970</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.text.DL_GPIO_reset</name>
         <load_address>0x1988</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1988</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e8">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x19a0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19a0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.text.DL_GPIO_setPins</name>
         <load_address>0x19b8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19b8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-122">
         <name>.text.DL_I2C_enableAnalogGlitchFilter</name>
         <load_address>0x19d0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19d0</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-129">
         <name>.text.DL_I2C_enableController</name>
         <load_address>0x19e8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x19e8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-127">
         <name>.text.DL_I2C_enableControllerClockStretching</name>
         <load_address>0x1a00</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a00</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.text.DL_I2C_enablePower</name>
         <load_address>0x1a18</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a18</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.text.DL_I2C_reset</name>
         <load_address>0x1a30</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a30</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-124">
         <name>.text.DL_I2C_setTimerPeriod</name>
         <load_address>0x1a48</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a48</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.text.DL_SYSCTL_setBORThreshold</name>
         <load_address>0x1a60</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a60</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.text.DL_Timer_enablePower</name>
         <load_address>0x1a78</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a78</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.text.DL_Timer_reset</name>
         <load_address>0x1a90</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1a90</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-61">
         <name>.text.DL_Timer_stopCounter</name>
         <load_address>0x1aa8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1aa8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-100">
         <name>.text.DL_UART_enablePower</name>
         <load_address>0x1ac0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ac0</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.text.DL_UART_reset</name>
         <load_address>0x1ad8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1ad8</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-148">
         <name>.text.DL_ADC12_enableConversions</name>
         <load_address>0x1af0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1af0</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.text.DL_GPIO_readPins</name>
         <load_address>0x1b06</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b06</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-134">
         <name>.text.DL_UART_enable</name>
         <load_address>0x1b1c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b1c</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.text.DL_ADC12_getFIFOAddress</name>
         <load_address>0x1b34</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b34</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.text.DL_GPIO_clearPins</name>
         <load_address>0x1b48</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b48</run_address>
         <size>0x14</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-14b">
         <name>.text.DL_I2C_getControllerStatus</name>
         <load_address>0x1b5c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b5c</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-68">
         <name>.text.DL_I2C_receiveControllerData</name>
         <load_address>0x1b70</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b70</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-123">
         <name>.text.DL_I2C_resetControllerTransfer</name>
         <load_address>0x1b84</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b84</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-111">
         <name>.text.DL_SYSCTL_disableSYSPLL</name>
         <load_address>0x1b98</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1b98</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.text.DL_Timer_enableClock</name>
         <load_address>0x1bac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1bac</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-60">
         <name>.text.DL_ADC12_getPendingInterrupt</name>
         <load_address>0x1bc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1bc0</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-65">
         <name>.text.DL_I2C_getPendingInterrupt</name>
         <load_address>0x1bd2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1bd2</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0x1be4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1be4</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-92">
         <name>.text:TI_memcpy_small</name>
         <load_address>0x1bf6</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1bf6</run_address>
         <size>0x12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0x1c08</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c08</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-110">
         <name>.text.DL_SYSCTL_disableHFXT</name>
         <load_address>0x1c1c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c1c</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-56">
         <name>.text:decompress:ZI</name>
         <load_address>0x1c2c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c2c</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.text:TI_memset_small</name>
         <load_address>0x1c3c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c3c</run_address>
         <size>0xe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.text.__aeabi_memclr</name>
         <load_address>0x1c4c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c4c</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d1"/>
      </object_component>
      <object_component id="oc-102">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0x1c58</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c58</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-da">
         <name>.text.SYSCFG_DL_DMA_init</name>
         <load_address>0x1c62</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c62</run_address>
         <size>0x8</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-49">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0x1c6c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c6c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d0"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x1c74</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c74</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0x1c78</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c78</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-81">
         <name>.text._system_pre_init</name>
         <load_address>0x1c7c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c7c</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.text:abort</name>
         <load_address>0x1c80</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c80</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-1bb">
         <name>__TI_handler_table</name>
         <load_address>0x2590</load_address>
         <readonly>true</readonly>
         <run_address>0x2590</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1be">
         <name>.cinit..bss.load</name>
         <load_address>0x259c</load_address>
         <readonly>true</readonly>
         <run_address>0x259c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-1bd">
         <name>.cinit..data.load</name>
         <load_address>0x25a4</load_address>
         <readonly>true</readonly>
         <run_address>0x25a4</run_address>
         <size>0x6</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-1bc">
         <name>__TI_cinit_table</name>
         <load_address>0x25ac</load_address>
         <readonly>true</readonly>
         <run_address>0x25ac</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-174">
         <name>.rodata.asc2_1608</name>
         <load_address>0x1c88</load_address>
         <readonly>true</readonly>
         <run_address>0x1c88</run_address>
         <size>0x5f0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-175">
         <name>.rodata.asc2_0806</name>
         <load_address>0x2278</load_address>
         <readonly>true</readonly>
         <run_address>0x2278</run_address>
         <size>0x228</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-152">
         <name>.rodata.Hzk</name>
         <load_address>0x24a0</load_address>
         <readonly>true</readonly>
         <run_address>0x24a0</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-16c">
         <name>.rodata.gDMA_CH0Config</name>
         <load_address>0x2520</load_address>
         <readonly>true</readonly>
         <run_address>0x2520</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.rodata.gTIMER_0TimerConfig</name>
         <load_address>0x2538</load_address>
         <readonly>true</readonly>
         <run_address>0x2538</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.rodata.str1.171135724027876188451</name>
         <load_address>0x254c</load_address>
         <readonly>true</readonly>
         <run_address>0x254c</run_address>
         <size>0xe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.rodata.str1.66840473539710954921</name>
         <load_address>0x255a</load_address>
         <readonly>true</readonly>
         <run_address>0x255a</run_address>
         <size>0xe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.rodata.str1.88864587114646350841</name>
         <load_address>0x2568</load_address>
         <readonly>true</readonly>
         <run_address>0x2568</run_address>
         <size>0xe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-136">
         <name>.rodata.gUART_0Config</name>
         <load_address>0x2576</load_address>
         <readonly>true</readonly>
         <run_address>0x2576</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-149">
         <name>.rodata.gADC12_0ClockConfig</name>
         <load_address>0x2580</load_address>
         <readonly>true</readonly>
         <run_address>0x2580</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.rodata.gTIMER_0ClockConfig</name>
         <load_address>0x2588</load_address>
         <readonly>true</readonly>
         <run_address>0x2588</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.rodata.gI2C_OLEDClockConfig</name>
         <load_address>0x258b</load_address>
         <readonly>true</readonly>
         <run_address>0x258b</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-135">
         <name>.rodata.gUART_0ClockConfig</name>
         <load_address>0x258d</load_address>
         <readonly>true</readonly>
         <run_address>0x258d</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-185">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-62">
         <name>.data.ad_over</name>
         <load_address>0x202010ed</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202010ed</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.data.key_val</name>
         <load_address>0x202010ee</load_address>
         <readwrite>true</readwrite>
         <run_address>0x202010ee</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.common:gADCSamples</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x1000</size>
         <alignment>0x2</alignment>
      </object_component>
      <object_component id="oc-db">
         <name>.common:gTIMER_0Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20201000</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-6f">
         <name>.common:gI2cControllerStatus</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202010ec</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-71">
         <name>.common:gTxLen</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202010e8</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-72">
         <name>.common:gTxPacket</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202010cc</run_address>
         <size>0x10</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-70">
         <name>.common:gTxCount</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202010e4</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-73">
         <name>.common:gRxCount</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202010dc</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-74">
         <name>.common:gRxLen</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202010e0</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-75">
         <name>.common:gRxPacket</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202010bc</run_address>
         <size>0x10</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-1c0">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-63">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1ed</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.debug_abbrev</name>
         <load_address>0x1ed</load_address>
         <run_address>0x1ed</run_address>
         <size>0x207</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.debug_abbrev</name>
         <load_address>0x3f4</load_address>
         <run_address>0x3f4</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.debug_abbrev</name>
         <load_address>0x461</load_address>
         <run_address>0x461</run_address>
         <size>0x132</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-76">
         <name>.debug_abbrev</name>
         <load_address>0x593</load_address>
         <run_address>0x593</run_address>
         <size>0x1de</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-165">
         <name>.debug_abbrev</name>
         <load_address>0x771</load_address>
         <run_address>0x771</run_address>
         <size>0x171</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-15d">
         <name>.debug_abbrev</name>
         <load_address>0x8e2</load_address>
         <run_address>0x8e2</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-176">
         <name>.debug_abbrev</name>
         <load_address>0x944</load_address>
         <run_address>0x944</run_address>
         <size>0x182</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_abbrev</name>
         <load_address>0xac6</load_address>
         <run_address>0xac6</run_address>
         <size>0x1e7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-161">
         <name>.debug_abbrev</name>
         <load_address>0xcad</load_address>
         <run_address>0xcad</run_address>
         <size>0x258</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-163">
         <name>.debug_abbrev</name>
         <load_address>0xf05</load_address>
         <run_address>0xf05</run_address>
         <size>0x27f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_abbrev</name>
         <load_address>0x1184</load_address>
         <run_address>0x1184</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-f1">
         <name>.debug_abbrev</name>
         <load_address>0x1233</load_address>
         <run_address>0x1233</run_address>
         <size>0x186</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_abbrev</name>
         <load_address>0x13b9</load_address>
         <run_address>0x13b9</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_abbrev</name>
         <load_address>0x13f2</load_address>
         <run_address>0x13f2</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_abbrev</name>
         <load_address>0x14b4</load_address>
         <run_address>0x14b4</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_abbrev</name>
         <load_address>0x1524</load_address>
         <run_address>0x1524</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.debug_abbrev</name>
         <load_address>0x15b1</load_address>
         <run_address>0x15b1</run_address>
         <size>0xb3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-79">
         <name>.debug_abbrev</name>
         <load_address>0x1664</load_address>
         <run_address>0x1664</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d0"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_abbrev</name>
         <load_address>0x168b</load_address>
         <run_address>0x168b</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d1"/>
      </object_component>
      <object_component id="oc-173">
         <name>.debug_abbrev</name>
         <load_address>0x16b0</load_address>
         <run_address>0x16b0</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d2"/>
      </object_component>
      <object_component id="oc-179">
         <name>.debug_abbrev</name>
         <load_address>0x16d7</load_address>
         <run_address>0x16d7</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_abbrev</name>
         <load_address>0x1730</load_address>
         <run_address>0x1730</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-f7">
         <name>.debug_abbrev</name>
         <load_address>0x1755</load_address>
         <run_address>0x1755</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-1c2">
         <name>.debug_abbrev</name>
         <load_address>0x177a</load_address>
         <run_address>0x177a</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x18ef</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_info</name>
         <load_address>0x18ef</load_address>
         <run_address>0x18ef</run_address>
         <size>0x502b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x691a</load_address>
         <run_address>0x691a</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.debug_info</name>
         <load_address>0x699a</load_address>
         <run_address>0x699a</run_address>
         <size>0xa59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_info</name>
         <load_address>0x73f3</load_address>
         <run_address>0x73f3</run_address>
         <size>0x137b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.debug_info</name>
         <load_address>0x876e</load_address>
         <run_address>0x876e</run_address>
         <size>0x731</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-105">
         <name>.debug_info</name>
         <load_address>0x8e9f</load_address>
         <run_address>0x8e9f</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-169">
         <name>.debug_info</name>
         <load_address>0x8f14</load_address>
         <run_address>0x8f14</run_address>
         <size>0x6df</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_info</name>
         <load_address>0x95f3</load_address>
         <run_address>0x95f3</run_address>
         <size>0xca0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-116">
         <name>.debug_info</name>
         <load_address>0xa293</load_address>
         <run_address>0xa293</run_address>
         <size>0x2f93</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.debug_info</name>
         <load_address>0xd226</load_address>
         <run_address>0xd226</run_address>
         <size>0x1259</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0xe47f</load_address>
         <run_address>0xe47f</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_info</name>
         <load_address>0xe8a2</load_address>
         <run_address>0xe8a2</run_address>
         <size>0x74a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_info</name>
         <load_address>0xefec</load_address>
         <run_address>0xefec</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_info</name>
         <load_address>0xf032</load_address>
         <run_address>0xf032</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_info</name>
         <load_address>0xf1c4</load_address>
         <run_address>0xf1c4</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_info</name>
         <load_address>0xf28a</load_address>
         <run_address>0xf28a</run_address>
         <size>0x180</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.debug_info</name>
         <load_address>0xf40a</load_address>
         <run_address>0xf40a</run_address>
         <size>0xed</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_info</name>
         <load_address>0xf4f7</load_address>
         <run_address>0xf4f7</run_address>
         <size>0x19e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d0"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_info</name>
         <load_address>0xf695</load_address>
         <run_address>0xf695</run_address>
         <size>0x1ba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d1"/>
      </object_component>
      <object_component id="oc-14f">
         <name>.debug_info</name>
         <load_address>0xf84f</load_address>
         <run_address>0xf84f</run_address>
         <size>0x1c1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d2"/>
      </object_component>
      <object_component id="oc-16f">
         <name>.debug_info</name>
         <load_address>0xfa10</load_address>
         <run_address>0xfa10</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_info</name>
         <load_address>0xfa95</load_address>
         <run_address>0xfa95</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_info</name>
         <load_address>0xfd8f</load_address>
         <run_address>0xfd8f</run_address>
         <size>0x244</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-1c1">
         <name>.debug_info</name>
         <load_address>0xffd3</load_address>
         <run_address>0xffd3</run_address>
         <size>0xa2</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xa8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_ranges</name>
         <load_address>0xa8</load_address>
         <run_address>0xa8</run_address>
         <size>0x210</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_ranges</name>
         <load_address>0x2b8</load_address>
         <run_address>0x2b8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_ranges</name>
         <load_address>0x2d0</load_address>
         <run_address>0x2d0</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_ranges</name>
         <load_address>0x300</load_address>
         <run_address>0x300</run_address>
         <size>0xd0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.debug_ranges</name>
         <load_address>0x3d0</load_address>
         <run_address>0x3d0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.debug_ranges</name>
         <load_address>0x3e8</load_address>
         <run_address>0x3e8</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-115">
         <name>.debug_ranges</name>
         <load_address>0x5c0</load_address>
         <run_address>0x5c0</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.debug_ranges</name>
         <load_address>0x730</load_address>
         <run_address>0x730</run_address>
         <size>0x190</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_ranges</name>
         <load_address>0x8c0</load_address>
         <run_address>0x8c0</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_ranges</name>
         <load_address>0x908</load_address>
         <run_address>0x908</run_address>
         <size>0xa8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_ranges</name>
         <load_address>0x9b0</load_address>
         <run_address>0x9b0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_ranges</name>
         <load_address>0x9c8</load_address>
         <run_address>0x9c8</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.debug_ranges</name>
         <load_address>0x9f8</load_address>
         <run_address>0x9f8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_ranges</name>
         <load_address>0xa10</load_address>
         <run_address>0xa10</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d1"/>
      </object_component>
      <object_component id="oc-170">
         <name>.debug_ranges</name>
         <load_address>0xa38</load_address>
         <run_address>0xa38</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_ranges</name>
         <load_address>0xa50</load_address>
         <run_address>0xa50</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_ranges</name>
         <load_address>0xa78</load_address>
         <run_address>0xa78</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xe4c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_str</name>
         <load_address>0xe4c</load_address>
         <run_address>0xe4c</run_address>
         <size>0x39ed</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.debug_str</name>
         <load_address>0x4839</load_address>
         <run_address>0x4839</run_address>
         <size>0x159</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.debug_str</name>
         <load_address>0x4992</load_address>
         <run_address>0x4992</run_address>
         <size>0x545</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-77">
         <name>.debug_str</name>
         <load_address>0x4ed7</load_address>
         <run_address>0x4ed7</run_address>
         <size>0xe64</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-166">
         <name>.debug_str</name>
         <load_address>0x5d3b</load_address>
         <run_address>0x5d3b</run_address>
         <size>0x63b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-15e">
         <name>.debug_str</name>
         <load_address>0x6376</load_address>
         <run_address>0x6376</run_address>
         <size>0x177</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-177">
         <name>.debug_str</name>
         <load_address>0x64ed</load_address>
         <run_address>0x64ed</run_address>
         <size>0x686</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_str</name>
         <load_address>0x6b73</load_address>
         <run_address>0x6b73</run_address>
         <size>0x8b9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-162">
         <name>.debug_str</name>
         <load_address>0x742c</load_address>
         <run_address>0x742c</run_address>
         <size>0x1c3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-164">
         <name>.debug_str</name>
         <load_address>0x9067</load_address>
         <run_address>0x9067</run_address>
         <size>0xced</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_str</name>
         <load_address>0x9d54</load_address>
         <run_address>0x9d54</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.debug_str</name>
         <load_address>0x9f79</load_address>
         <run_address>0x9f79</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_str</name>
         <load_address>0xa2a8</load_address>
         <run_address>0xa2a8</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_str</name>
         <load_address>0xa39d</load_address>
         <run_address>0xa39d</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_str</name>
         <load_address>0xa538</load_address>
         <run_address>0xa538</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_str</name>
         <load_address>0xa6a0</load_address>
         <run_address>0xa6a0</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_str</name>
         <load_address>0xa875</load_address>
         <run_address>0xa875</run_address>
         <size>0x13f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-17a">
         <name>.debug_str</name>
         <load_address>0xa9b4</load_address>
         <run_address>0xa9b4</run_address>
         <size>0x198</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1e0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_frame</name>
         <load_address>0x1e0</load_address>
         <run_address>0x1e0</run_address>
         <size>0x5ec</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0x7cc</load_address>
         <run_address>0x7cc</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.debug_frame</name>
         <load_address>0x7fc</load_address>
         <run_address>0x7fc</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_frame</name>
         <load_address>0x878</load_address>
         <run_address>0x878</run_address>
         <size>0x294</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.debug_frame</name>
         <load_address>0xb0c</load_address>
         <run_address>0xb0c</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-103">
         <name>.debug_frame</name>
         <load_address>0xb58</load_address>
         <run_address>0xb58</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-16a">
         <name>.debug_frame</name>
         <load_address>0xb78</load_address>
         <run_address>0xb78</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.debug_frame</name>
         <load_address>0xba8</load_address>
         <run_address>0xba8</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-117">
         <name>.debug_frame</name>
         <load_address>0xcd4</load_address>
         <run_address>0xcd4</run_address>
         <size>0x400</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.debug_frame</name>
         <load_address>0x10d4</load_address>
         <run_address>0x10d4</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_frame</name>
         <load_address>0x128c</load_address>
         <run_address>0x128c</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_frame</name>
         <load_address>0x131c</load_address>
         <run_address>0x131c</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-84">
         <name>.debug_frame</name>
         <load_address>0x141c</load_address>
         <run_address>0x141c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_frame</name>
         <load_address>0x143c</load_address>
         <run_address>0x143c</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_frame</name>
         <load_address>0x1474</load_address>
         <run_address>0x1474</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_frame</name>
         <load_address>0x149c</load_address>
         <run_address>0x149c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_frame</name>
         <load_address>0x14cc</load_address>
         <run_address>0x14cc</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-171">
         <name>.debug_frame</name>
         <load_address>0x14fc</load_address>
         <run_address>0x14fc</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x97c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_line</name>
         <load_address>0x97c</load_address>
         <run_address>0x97c</run_address>
         <size>0xde5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_line</name>
         <load_address>0x1761</load_address>
         <run_address>0x1761</run_address>
         <size>0xba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.debug_line</name>
         <load_address>0x181b</load_address>
         <run_address>0x181b</run_address>
         <size>0x6a7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_line</name>
         <load_address>0x1ec2</load_address>
         <run_address>0x1ec2</run_address>
         <size>0xd78</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-138">
         <name>.debug_line</name>
         <load_address>0x2c3a</load_address>
         <run_address>0x2c3a</run_address>
         <size>0x1f1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-104">
         <name>.debug_line</name>
         <load_address>0x2e2b</load_address>
         <run_address>0x2e2b</run_address>
         <size>0xe4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-16b">
         <name>.debug_line</name>
         <load_address>0x2f0f</load_address>
         <run_address>0x2f0f</run_address>
         <size>0x1b0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_line</name>
         <load_address>0x30bf</load_address>
         <run_address>0x30bf</run_address>
         <size>0x617</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-118">
         <name>.debug_line</name>
         <load_address>0x36d6</load_address>
         <run_address>0x36d6</run_address>
         <size>0x15a5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.debug_line</name>
         <load_address>0x4c7b</load_address>
         <run_address>0x4c7b</run_address>
         <size>0x989</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_line</name>
         <load_address>0x5604</load_address>
         <run_address>0x5604</run_address>
         <size>0x1fe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_line</name>
         <load_address>0x5802</load_address>
         <run_address>0x5802</run_address>
         <size>0x4fb</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_line</name>
         <load_address>0x5cfd</load_address>
         <run_address>0x5cfd</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3d"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_line</name>
         <load_address>0x5d3b</load_address>
         <run_address>0x5d3b</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_line</name>
         <load_address>0x5e33</load_address>
         <run_address>0x5e33</run_address>
         <size>0xbf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_line</name>
         <load_address>0x5ef2</load_address>
         <run_address>0x5ef2</run_address>
         <size>0x1c7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_line</name>
         <load_address>0x60b9</load_address>
         <run_address>0x60b9</run_address>
         <size>0x6b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_line</name>
         <load_address>0x6124</load_address>
         <run_address>0x6124</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d0"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_line</name>
         <load_address>0x61c8</load_address>
         <run_address>0x61c8</run_address>
         <size>0xba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d1"/>
      </object_component>
      <object_component id="oc-14e">
         <name>.debug_line</name>
         <load_address>0x6282</load_address>
         <run_address>0x6282</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d2"/>
      </object_component>
      <object_component id="oc-172">
         <name>.debug_line</name>
         <load_address>0x6344</load_address>
         <run_address>0x6344</run_address>
         <size>0xb7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_line</name>
         <load_address>0x63fb</load_address>
         <run_address>0x63fb</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_line</name>
         <load_address>0x649b</load_address>
         <run_address>0x649b</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-139">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1b"/>
      </object_component>
      <object_component id="oc-15f">
         <name>.debug_loc</name>
         <load_address>0xc7</load_address>
         <run_address>0xc7</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1c"/>
      </object_component>
      <object_component id="oc-178">
         <name>.debug_loc</name>
         <load_address>0xda</load_address>
         <run_address>0xda</run_address>
         <size>0xbd</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1d"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.debug_loc</name>
         <load_address>0x197</load_address>
         <run_address>0x197</run_address>
         <size>0x31c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1e"/>
      </object_component>
      <object_component id="oc-119">
         <name>.debug_loc</name>
         <load_address>0x4b3</load_address>
         <run_address>0x4b3</run_address>
         <size>0x18ad</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1f"/>
      </object_component>
      <object_component id="oc-130">
         <name>.debug_loc</name>
         <load_address>0x1d60</load_address>
         <run_address>0x1d60</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-20"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_loc</name>
         <load_address>0x251c</load_address>
         <run_address>0x251c</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3a"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.debug_loc</name>
         <load_address>0x25f4</load_address>
         <run_address>0x25f4</run_address>
         <size>0x480</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3c"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_loc</name>
         <load_address>0x2a74</load_address>
         <run_address>0x2a74</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3f"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_loc</name>
         <load_address>0x2be0</load_address>
         <run_address>0x2be0</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-40"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_loc</name>
         <load_address>0x2c4f</load_address>
         <run_address>0x2c4f</run_address>
         <size>0x166</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-41"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_loc</name>
         <load_address>0x2db5</load_address>
         <run_address>0x2db5</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-44"/>
      </object_component>
      <object_component id="oc-17b">
         <name>.debug_loc</name>
         <load_address>0x2ddb</load_address>
         <run_address>0x2ddb</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d8"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d0"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d1"/>
      </object_component>
      <object_component id="oc-150">
         <name>.debug_aranges</name>
         <load_address>0x48</load_address>
         <run_address>0x48</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d2"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_aranges</name>
         <load_address>0x68</load_address>
         <run_address>0x68</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_aranges</name>
         <load_address>0x90</load_address>
         <run_address>0x90</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x1bc8</size>
         <contents>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-153"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-16e"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-14c"/>
            <object_component_ref idref="oc-168"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-14d"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-151"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-167"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-143"/>
            <object_component_ref idref="oc-14a"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-16d"/>
            <object_component_ref idref="oc-160"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-140"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-142"/>
            <object_component_ref idref="oc-144"/>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-66"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-141"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-127"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-148"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-14b"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-92"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-56"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-b3"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x2590</load_address>
         <run_address>0x2590</run_address>
         <size>0x30</size>
         <contents>
            <object_component_ref idref="oc-1bb"/>
            <object_component_ref idref="oc-1be"/>
            <object_component_ref idref="oc-1bd"/>
            <object_component_ref idref="oc-1bc"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0x1c88</load_address>
         <run_address>0x1c88</run_address>
         <size>0x908</size>
         <contents>
            <object_component_ref idref="oc-174"/>
            <object_component_ref idref="oc-175"/>
            <object_component_ref idref="oc-152"/>
            <object_component_ref idref="oc-16c"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-149"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-135"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-185"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x202010ed</run_address>
         <size>0x2</size>
         <contents>
            <object_component_ref idref="oc-62"/>
            <object_component_ref idref="oc-b2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x10ed</size>
         <contents>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-75"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-1c0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-17c" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-17d" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-17e" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-17f" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-180" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-181" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-183" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-19f" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1789</size>
         <contents>
            <object_component_ref idref="oc-63"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-165"/>
            <object_component_ref idref="oc-15d"/>
            <object_component_ref idref="oc-176"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-161"/>
            <object_component_ref idref="oc-163"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-f1"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-173"/>
            <object_component_ref idref="oc-179"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-f7"/>
            <object_component_ref idref="oc-1c2"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1a1" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x10075</size>
         <contents>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-169"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-14f"/>
            <object_component_ref idref="oc-16f"/>
            <object_component_ref idref="oc-95"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-1c1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1a3" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xaa0</size>
         <contents>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-13b"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-170"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-d0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1a5" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xab4c</size>
         <contents>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-166"/>
            <object_component_ref idref="oc-15e"/>
            <object_component_ref idref="oc-177"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-162"/>
            <object_component_ref idref="oc-164"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-17a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1a7" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x152c</size>
         <contents>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-16a"/>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-171"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1a9" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x651b</size>
         <contents>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-16b"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-14e"/>
            <object_component_ref idref="oc-172"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-d1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1ab" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x2dfb</size>
         <contents>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-15f"/>
            <object_component_ref idref="oc-178"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-17b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1b5" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xb8</size>
         <contents>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-150"/>
            <object_component_ref idref="oc-94"/>
            <object_component_ref idref="oc-cf"/>
         </contents>
      </logical_group>
      <logical_group id="lg-1bf" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-1cf" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x25c0</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-1d0" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x10ef</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-1d1" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x25c0</used_space>
         <unused_space>0x1da40</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0x1bc8</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x1c88</start_address>
               <size>0x908</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x2590</start_address>
               <size>0x30</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x25c0</start_address>
               <size>0x1da40</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x12ef</used_space>
         <unused_space>0x6d11</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-181"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-183"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x10ed</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x202010ed</start_address>
               <size>0x2</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x202010ef</start_address>
               <size>0x6d11</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.bss</name>
            <load_address>0x259c</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x10ed</run_size>
            <compression>zero_init</compression>
         </cprec>
         <cprec>
            <name>.data</name>
            <load_address>0x25a4</load_address>
            <load_size>0x6</load_size>
            <run_address>0x202010ed</run_address>
            <run_size>0x2</run_size>
            <compression>lzss</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x25ac</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x25bc</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x25bc</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x2590</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x259c</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-6b">
         <name>main</name>
         <value>0xb85</value>
         <object_component_ref idref="oc-85"/>
      </symbol>
      <symbol id="sm-6c">
         <name>Show_menu</name>
         <value>0xd0d</value>
         <object_component_ref idref="oc-a7"/>
      </symbol>
      <symbol id="sm-6d">
         <name>gADCSamples</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-6e">
         <name>key_val</name>
         <value>0x202010ee</value>
         <object_component_ref idref="oc-b2"/>
      </symbol>
      <symbol id="sm-6f">
         <name>ADC0_IRQHandler</name>
         <value>0x13b1</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-70">
         <name>ad_over</name>
         <value>0x202010ed</value>
         <object_component_ref idref="oc-62"/>
      </symbol>
      <symbol id="sm-16c">
         <name>SYSCFG_DL_init</name>
         <value>0x1381</value>
         <object_component_ref idref="oc-9c"/>
      </symbol>
      <symbol id="sm-16d">
         <name>SYSCFG_DL_initPower</name>
         <value>0xc1d</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-16e">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x94d</value>
         <object_component_ref idref="oc-d4"/>
      </symbol>
      <symbol id="sm-16f">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x1511</value>
         <object_component_ref idref="oc-d5"/>
      </symbol>
      <symbol id="sm-170">
         <name>SYSCFG_DL_TIMER_0_init</name>
         <value>0x10fd</value>
         <object_component_ref idref="oc-d6"/>
      </symbol>
      <symbol id="sm-171">
         <name>SYSCFG_DL_I2C_OLED_init</name>
         <value>0xe5d</value>
         <object_component_ref idref="oc-d7"/>
      </symbol>
      <symbol id="sm-172">
         <name>SYSCFG_DL_UART_0_init</name>
         <value>0x1181</value>
         <object_component_ref idref="oc-d8"/>
      </symbol>
      <symbol id="sm-173">
         <name>SYSCFG_DL_ADC12_0_init</name>
         <value>0xa29</value>
         <object_component_ref idref="oc-d9"/>
      </symbol>
      <symbol id="sm-174">
         <name>SYSCFG_DL_DMA_init</name>
         <value>0x1c63</value>
         <object_component_ref idref="oc-da"/>
      </symbol>
      <symbol id="sm-175">
         <name>gTIMER_0Backup</name>
         <value>0x20201000</value>
      </symbol>
      <symbol id="sm-176">
         <name>SYSCFG_DL_DMA_CH0_init</name>
         <value>0x1409</value>
         <object_component_ref idref="oc-14a"/>
      </symbol>
      <symbol id="sm-181">
         <name>Default_Handler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-182">
         <name>Reset_Handler</name>
         <value>0x1c79</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-183">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-184">
         <name>NMI_Handler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-185">
         <name>HardFault_Handler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-186">
         <name>SVC_Handler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-187">
         <name>PendSV_Handler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-188">
         <name>SysTick_Handler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-189">
         <name>GROUP0_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-18a">
         <name>GROUP1_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-18b">
         <name>TIMG8_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-18c">
         <name>UART3_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-18d">
         <name>ADC1_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-18e">
         <name>CANFD0_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-18f">
         <name>DAC0_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-190">
         <name>SPI0_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-191">
         <name>SPI1_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-192">
         <name>UART1_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-193">
         <name>UART2_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-194">
         <name>UART0_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-195">
         <name>TIMG0_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-196">
         <name>TIMG6_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-197">
         <name>TIMA0_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-198">
         <name>TIMA1_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-199">
         <name>TIMG7_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-19a">
         <name>TIMG12_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-19b">
         <name>I2C1_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-19c">
         <name>AES_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-19d">
         <name>RTC_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-19e">
         <name>DMA_IRQHandler</name>
         <value>0x1c75</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-1b2">
         <name>Read4X4KEY</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-a8"/>
      </symbol>
      <symbol id="sm-1f0">
         <name>delay_ms</name>
         <value>0x16ef</value>
         <object_component_ref idref="oc-de"/>
      </symbol>
      <symbol id="sm-1f1">
         <name>OLED_WR_Byte</name>
         <value>0x871</value>
         <object_component_ref idref="oc-e0"/>
      </symbol>
      <symbol id="sm-1f2">
         <name>gI2cControllerStatus</name>
         <value>0x202010ec</value>
      </symbol>
      <symbol id="sm-1f3">
         <name>gTxLen</name>
         <value>0x202010e8</value>
      </symbol>
      <symbol id="sm-1f4">
         <name>gTxPacket</name>
         <value>0x202010cc</value>
      </symbol>
      <symbol id="sm-1f5">
         <name>gTxCount</name>
         <value>0x202010e4</value>
      </symbol>
      <symbol id="sm-1f6">
         <name>OLED_Set_Pos</name>
         <value>0x123d</value>
         <object_component_ref idref="oc-151"/>
      </symbol>
      <symbol id="sm-1f7">
         <name>OLED_Clear</name>
         <value>0xdf1</value>
         <object_component_ref idref="oc-ad"/>
      </symbol>
      <symbol id="sm-1f8">
         <name>OLED_ShowChar</name>
         <value>0x571</value>
         <object_component_ref idref="oc-153"/>
      </symbol>
      <symbol id="sm-1f9">
         <name>asc2_1608</name>
         <value>0x1c88</value>
         <object_component_ref idref="oc-174"/>
      </symbol>
      <symbol id="sm-1fa">
         <name>asc2_0806</name>
         <value>0x2278</value>
         <object_component_ref idref="oc-175"/>
      </symbol>
      <symbol id="sm-1fb">
         <name>OLED_ShowString</name>
         <value>0xd81</value>
         <object_component_ref idref="oc-e2"/>
      </symbol>
      <symbol id="sm-1fc">
         <name>OLED_ShowChinese</name>
         <value>0xadd</value>
         <object_component_ref idref="oc-e1"/>
      </symbol>
      <symbol id="sm-1fd">
         <name>Hzk</name>
         <value>0x24a0</value>
         <object_component_ref idref="oc-152"/>
      </symbol>
      <symbol id="sm-1fe">
         <name>OLED_Init</name>
         <value>0x689</value>
         <object_component_ref idref="oc-a1"/>
      </symbol>
      <symbol id="sm-1ff">
         <name>I2C0_IRQHandler</name>
         <value>0x445</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-200">
         <name>gRxCount</name>
         <value>0x202010dc</value>
      </symbol>
      <symbol id="sm-201">
         <name>gRxLen</name>
         <value>0x202010e0</value>
      </symbol>
      <symbol id="sm-202">
         <name>gRxPacket</name>
         <value>0x202010bc</value>
      </symbol>
      <symbol id="sm-203">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-204">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-205">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-206">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-207">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-208">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-209">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-20a">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-20b">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-21c">
         <name>DL_ADC12_setClockConfig</name>
         <value>0x1141</value>
         <object_component_ref idref="oc-137"/>
      </symbol>
      <symbol id="sm-225">
         <name>DL_Common_delayCycles</name>
         <value>0x1c59</value>
         <object_component_ref idref="oc-102"/>
      </symbol>
      <symbol id="sm-22f">
         <name>DL_DMA_initChannel</name>
         <value>0xfd1</value>
         <object_component_ref idref="oc-168"/>
      </symbol>
      <symbol id="sm-23b">
         <name>DL_I2C_setClockConfig</name>
         <value>0x16c9</value>
         <object_component_ref idref="oc-120"/>
      </symbol>
      <symbol id="sm-23c">
         <name>DL_I2C_fillControllerTXFIFO</name>
         <value>0xf21</value>
         <object_component_ref idref="oc-69"/>
      </symbol>
      <symbol id="sm-24c">
         <name>DL_Timer_setClockConfig</name>
         <value>0x18d9</value>
         <object_component_ref idref="oc-114"/>
      </symbol>
      <symbol id="sm-24d">
         <name>DL_Timer_initTimerMode</name>
         <value>0x789</value>
         <object_component_ref idref="oc-11a"/>
      </symbol>
      <symbol id="sm-25a">
         <name>DL_UART_init</name>
         <value>0x10b5</value>
         <object_component_ref idref="oc-131"/>
      </symbol>
      <symbol id="sm-25b">
         <name>DL_UART_setClockConfig</name>
         <value>0x1be5</value>
         <object_component_ref idref="oc-12b"/>
      </symbol>
      <symbol id="sm-271">
         <name>_c_int00_noargs</name>
         <value>0x1655</value>
         <object_component_ref idref="oc-59"/>
      </symbol>
      <symbol id="sm-272">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-27e">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0x1279</value>
         <object_component_ref idref="oc-ba"/>
      </symbol>
      <symbol id="sm-286">
         <name>_system_pre_init</name>
         <value>0x1c7d</value>
         <object_component_ref idref="oc-81"/>
      </symbol>
      <symbol id="sm-291">
         <name>__TI_zero_init</name>
         <value>0x1c2d</value>
         <object_component_ref idref="oc-56"/>
      </symbol>
      <symbol id="sm-29a">
         <name>__TI_decompress_none</name>
         <value>0x1c09</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-2a5">
         <name>__TI_decompress_lzss</name>
         <value>0xc95</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-2b1">
         <name>abort</name>
         <value>0x1c81</value>
         <object_component_ref idref="oc-b3"/>
      </symbol>
      <symbol id="sm-2b2">
         <name>C$$EXIT</name>
         <value>0x1c80</value>
         <object_component_ref idref="oc-b3"/>
      </symbol>
      <symbol id="sm-2ca">
         <name>__aeabi_memcpy</name>
         <value>0x1c6d</value>
         <object_component_ref idref="oc-49"/>
      </symbol>
      <symbol id="sm-2cb">
         <name>__aeabi_memcpy4</name>
         <value>0x1c6d</value>
         <object_component_ref idref="oc-49"/>
      </symbol>
      <symbol id="sm-2cc">
         <name>__aeabi_memcpy8</name>
         <value>0x1c6d</value>
         <object_component_ref idref="oc-49"/>
      </symbol>
      <symbol id="sm-2d3">
         <name>__aeabi_memclr</name>
         <value>0x1c4d</value>
         <object_component_ref idref="oc-7c"/>
      </symbol>
      <symbol id="sm-2d4">
         <name>__aeabi_memclr4</name>
         <value>0x1c4d</value>
         <object_component_ref idref="oc-7c"/>
      </symbol>
      <symbol id="sm-2d5">
         <name>__aeabi_memclr8</name>
         <value>0x1c4d</value>
         <object_component_ref idref="oc-7c"/>
      </symbol>
      <symbol id="sm-2db">
         <name>__aeabi_uidiv</name>
         <value>0x11c1</value>
         <object_component_ref idref="oc-14d"/>
      </symbol>
      <symbol id="sm-2dc">
         <name>__aeabi_uidivmod</name>
         <value>0x11c1</value>
         <object_component_ref idref="oc-14d"/>
      </symbol>
      <symbol id="sm-2e7">
         <name>__aeabi_idiv0</name>
         <value>0xe5b</value>
         <object_component_ref idref="oc-16e"/>
      </symbol>
      <symbol id="sm-2f0">
         <name>TI_memcpy_small</name>
         <value>0x1bf7</value>
         <object_component_ref idref="oc-92"/>
      </symbol>
      <symbol id="sm-2f9">
         <name>TI_memset_small</name>
         <value>0x1c3d</value>
         <object_component_ref idref="oc-cd"/>
      </symbol>
      <symbol id="sm-2fa">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-2fd">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-2fe">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>

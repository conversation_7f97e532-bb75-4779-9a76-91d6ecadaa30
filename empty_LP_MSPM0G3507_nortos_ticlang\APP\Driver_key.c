#include "Driver_key.h"
#include "ti/driverlib/dl_gpio.h"

uint8_t key_val = 0;  // 当前按键状态
uint8_t key_old = 0;  // 前一按键状态
uint8_t key_down = 0; // 按下的按键
uint8_t key_up = 0;   // 释放的按键
uint8_t key_early = 0;   //上一次的按键

/**
 * @brief 读取按键状态
 *
 * 该函数读取连接在 GPIO 引脚上的按键状态，并返回相应的按键编号。
 *
 * @return 返回按键编号。0 表示没有按键按下，1-16 表示对应的按键被按下。
 */

uint8_t key_read(void)
{
    uint8_t current_key = 0;  // 重命名避免与全局变量冲突

    // 拉低行,列为低则选中,以此类推
    ROW1_RESET;
    DL_Common_delayCycles(100);  // 添加稳定延时
    if( !COL1_READ )
        current_key = 1;
    else if( !COL2_READ )
        current_key = 2;
    else if( !COL3_READ )
        current_key = 3;
    else if( !COL4_READ )
        current_key = 4;
    ROW1_SET;

    ROW2_RESET;
    DL_Common_delayCycles(100);
    if( !COL1_READ )
        current_key = 5;
    else if( !COL2_READ )
        current_key = 6;
    else if( !COL3_READ )
        current_key = 7;
    else if( !COL4_READ )
        current_key = 8;
    ROW2_SET;

    ROW3_RESET;
    DL_Common_delayCycles(100);
    if( !COL1_READ )
        current_key = 9;
    else if( !COL2_READ )
        current_key = 10;
    else if( !COL3_READ )
        current_key = 11;
    else if( !COL4_READ )
        current_key = 12;
    ROW3_SET;

    ROW4_RESET;
    DL_Common_delayCycles(100);
    if( !COL1_READ )
        current_key = 13;
    else if( !COL2_READ )
        current_key = 14;
    else if( !COL3_READ )
        current_key = 15;
    else if( !COL4_READ )
        current_key = 16;
    ROW4_SET;

    return current_key;
}

/**
 * @brief 按键处理函数
 *
 * 该函数用于扫描按键的状态，并更新按键按下和释放的标志
 */
void key_proc(void)
{
    uint32_t debug_counter;
  // 读取当前按键状态
  key_val = key_read();
  // 计算按下的按键（当前按下状态与前一状态异或，并与当前状态相与）
  key_down = key_val & (key_old ^ key_val);
  // 计算释放的按键（当前未按下状态与前一状态异或，并与前一状态相与）
  key_up = ~key_val & (key_old ^ key_val);
  // 更新前一按键状态
  key_old = key_val;
  
  if(key_down != 0)
  key_early = key_down;
  // 只有按下按键1时LED才快速闪烁
//   if(key_early == 16) 
//   {
//        DL_GPIO_setPins(LED_pins_PORT, LED_pins_LED1_PIN);
//   }
//   else {
//     DL_GPIO_clearPins(LED_pins_PORT, LED_pins_LED1_PIN);
//   }
}

  

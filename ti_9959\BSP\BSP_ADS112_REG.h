/*
 * BSP_ADS112_REG.h
 * 
 * ADS112C04寄存器分布
 *
 *  Created on: 2024年8月19日
 *      Author: <PERSON><PERSON><PERSON> Huang
 */
#ifndef __BSP_ADS112_REG_H_
#define __BSP_ADS112_REG_H_

//-------------------------------------------------reg0
#define	AIN0_and_AIN1 0x0
#define	AIN0_and_AIN2 0x1
#define	AIN0_and_AIN3 0x2
#define	AIN1_and_AIN0 0x3
#define	AIN1_and_AIN2 0x4
#define	AIN1_and_AIN3 0x5
#define	AIN2_and_AIN3 0x6
#define	AIN3_and_AIN2 0x7
#define	AIN0_and_AVSS 0x8
#define	AIN1_and_AVSS 0x9
#define	AIN2_and_AVSS 0xA
#define	AIN3_and_AVSS 0xB

#define GAIN_1    0x0
#define GAIN_2    0x1
#define GAIN_4    0x2
#define GAIN_8    0x3
#define GAIN_16   0x4
#define GAIN_32   0x5
#define GAIN_64   0x6
#define GAIN_128  0x7

#define PGA_EN    0X0
#define PGA_DIS   0X1

//-------------------------------------------------reg1
#define RATE_NORMAL20_TURBO40        0X0
#define RATE_NORMAL45_TURBO90        0X1
#define RATE_NORMAL90_TURBO180       0X2
#define RATE_NORMAL175_TURBO350      0X3
#define RATE_NORMAL330_TURBO660      0X4
#define RATE_NORMAL600_TURBO1200     0X5
#define RATE_NORMAL1000_TURBO2000    0X6

#define MODE_NORMAL    0X0
#define MODE_TURBO     0X1

#define CM_SINGLE      0X0
#define CM_CONTINUOUS  0X1

#define VREF_Internal  0X0
#define VREF_External  0X1
#define VREF_AVSS      0X2

#define TS_DIS         0X0
#define TS_EN          0X1

//-------------------------------------------------reg2
#define COUNTER_DIS 0X0
#define COUNTER_EN  0X1

#define CRC_DIS          0X0
#define CRC_Inverted     0X1
#define CRC_EN           0X2

#define BCS_OFF  0X0
#define BCS_ON   0X1

#define IDAC_OFF        0X0
#define IDAC_10uA       0X1
#define IDAC_50uA       0X2
#define IDAC_100uA      0X3
#define IDAC_250uA      0X4
#define IDAC_500uA      0X5
#define IDAC_1000uA     0X6
#define IDAC_1500uA     0X7

//-------------------------------------------------reg3
#define IDAC1_DIS        0X0
#define IDAC1_AIN0       0X1
#define IDAC1_AIN1       0X2
#define IDAC1_AIN2       0X3
#define IDAC1_AIN3       0X4
#define IDAC1_REFP       0X5
#define IDAC1_REFN       0X6

#define IDAC2_DIS        0X0
#define IDAC2_AIN0       0X1
#define IDAC2_AIN1       0X2
#define IDAC2_AIN2       0X3
#define IDAC2_AIN3       0X4
#define IDAC2_REFP       0X5
#define IDAC2_REFN       0X6

#endif /* __BSP_ADS112_REG_H_ */
#ifndef __DRIVER_IIC_H_
#define __DRIVER_IIC_H_

#include "bsp_system.h"

// I2C 时钟SCL
#define	SCL_Set() (DL_GPIO_setPins(MYI2C_PORT, MYI2C_SCL_PIN))
#define	SCL_Clr() (DL_GPIO_clearPins(MYI2C_PORT, MYI2C_SCL_PIN))

//----------------------------------------------------------------------------------
// I2C 数据SDA
#define	SDA_Set() (DL_GPIO_setPins(MYI2C_PORT, MYI2C_SDA_PIN))
#define	SDA_Clr() (DL_GPIO_clearPins(MYI2C_PORT, MYI2C_SDA_PIN))

//打开SDA引脚（输出）
#define SDA_OUT() (DL_GPIO_initDigitalOutput(MYI2C_SDA_IOMUX))
//关闭SDA引脚（输入）
#define SDA_IN()  (DL_GPIO_initDigitalInput(MYI2C_SDA_IOMUX))

void I2C_Start(void);
void I2C_Stop(void);
void I2C_SendByte(uint8_t Byte);
uint8_t I2C_ReceiveByte(void);
void I2C_SendAck(uint8_t AckBit);
uint8_t I2C_ReceiveAck(void);


#endif


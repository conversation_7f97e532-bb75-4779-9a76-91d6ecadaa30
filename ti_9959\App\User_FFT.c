/*
 * User_FFT.c
 *
 * 不限点数FFT
 *
 *  Created on: 2024年8月19日
 *      Author: <PERSON><PERSON><PERSON>
 */

#include "User_FFT.h"

#define SWAP(a,b)  tempr=(a);(a)=(b);(b)=tempr

// 用于FFT的数组
complex FFT_Data[SampNum] = {0};

static void c_plus(complex a, complex b, complex *c){

    c->real = a.real + b.real;
    c->imag = a.imag + b.imag;
}

static void c_sub(complex a, complex b, complex *c){

    c->real = a.real - b.real;
    c->imag = a.imag - b.imag;
}

static void c_mul(complex a, complex b, complex *c){

    c->real = a.real * b.real - a.imag * b.imag;
    c->imag = a.real * b.imag + a.imag * b.real;
}


static void Wn_i(uint16_t n, uint16_t i, complex *Wn, char flag){

    Wn->real = cos(2 * PI * i / n);
    if(flag == 1)
        Wn->imag = -sin(2 * PI * i / n);
    else if(flag == 0)
        Wn->imag = -sin(2 * PI * i / n);
}

/*
*************************************************************************
*   功能  ：       傅里叶变换
*   参数  ：
*   1：转换长度
*   2：FFT数组
**************************************************************************
*/

void fft(int Num, complex f[]){

    complex t, wn; //中间变量
    int i, j, k, m, n, l, r ,M;

    int la, lb, lc;
    /*----计算分解的级数M=log2(N)----*/
    for(i = Num, M = 1; (i = i / 2) != 1; M++);

    /*----按照倒位序重新排列原信号----*/
    for(i = 1, j = Num / 2; i <= Num - 2; i++){

        if(i < j){

            t = f[j];
            f[j] = f[i];
            f[i] = t;
        }
        k = Num / 2;
        while(k <= j){

            j = j - k;
            k = k / 2;
        }
        j = j + k;
    }

    /*----FFT算法----*/
    for(m = 1; m <= M; m++){

        la = pow(2, m);     //la=2^m代表第m级每个分组所含节点数
        lb = la / 2;        //lb代表第m级每个分组所含碟形单元数。由于是基2，很明显是节点数的一半
        //同时它也表示每个碟形单元上下节点之间的距离
        /*----碟形运算----*/
        for(l = 1; l <= lb; l++){

            r = (l - 1) * pow(2, M - m);
            for(n = l - 1; n < Num - 1; n = n + la){

                //遍历每个分组，分组总数为N/la
                lc = n + lb;                    // n,lc分别代表一个碟形单元的上、下节点编号
                Wn_i(Num, r, &wn, 1);           // wn=Wnr
                c_mul(f[lc], wn, &t);           // t = f[lc] * wn复数运算
                c_sub(f[n], t, &(f[lc]));       // f[lc] = f[n] - f[lc] * Wnr
                c_plus(f[n], t, &(f[n]));       // f[n] = f[n] + f[lc] * Wnr
            }
        }
    }
}

// 获取频谱
uint16_t User_GetSpectrum( uint16_t d_len )
{
    uint16_t cnt = 0, index = 0;
    float vpp_max = 0 , temp = 0;

    // 虚部清零
    for (cnt = 0; cnt < d_len; cnt++)
        FFT_Data[cnt].imag = 0;

    // 做FFT
    fft(d_len, FFT_Data);

    // 对前半数据取模以及求出相角
    for (cnt = 0; cnt < d_len / 2; cnt++)
    {
        temp = FFT_Data[cnt].real;

        FFT_Data[cnt].real = sqrt(
                FFT_Data[cnt].real * FFT_Data[cnt].real
                        + FFT_Data[cnt].imag * FFT_Data[cnt].imag);

        FFT_Data[cnt].imag = atan2( FFT_Data[cnt].imag , temp ) / PI * 180;
    }

    // 提取直流量
    FFT_Data[0].real = FFT_Data[0].real / d_len;                // 第一个数据除以采样点数为直流分量

    // 提取谐波分量 & 寻找基波频点(除直流外幅值最大的频率点)
    for (cnt = 1; cnt < d_len / 2; cnt++)
    {
        FFT_Data[cnt].real = FFT_Data[cnt].real / d_len * 4;    // 对应频点模值除以采样点数乘以4得到幅值

        if (FFT_Data[cnt].real > vpp_max)
        {
            vpp_max = FFT_Data[cnt].real;
            index = cnt;
        }
    }

    // 返回基波频点
    return index;
}

// 修正相角值
float User_FixPha( float pha )
{
    while( 1 )
    {
        if( pha < -180 )
            pha = pha + 360;
        else if( pha > 180 )
            pha = pha - 360;
        else
            return pha;
    }
}


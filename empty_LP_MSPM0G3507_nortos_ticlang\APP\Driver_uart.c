#include "Driver_uart.h"
#include "ti/driverlib/dl_gpio.h"

// 接收缓冲区和状态变量
uint8_t uart_rx_buffer[UART_RX_BUFFER_SIZE];
volatile uint16_t rx_write_index = 0;    // 写入位置
volatile uint16_t rx_read_index = 0;     // 读取位置
volatile bool packet_ready = false;      // 数据包就绪标志
volatile uint16_t packet_length = 0;     // 数据包长度
volatile uint32_t last_rx_time = 0;      // 最后接收时间
uint8_t uart_data_buffer[UART_RX_BUFFER_SIZE];      //最后串口接收到的数据

int fputc(int c, FILE* stream)
{
	DL_UART_Main_transmitDataBlocking(UART_0_INST, c);
    return c;
}

/**
 * @brief UART0中断服务函数
 */
void UART0_IRQHandler(void)
{
    // 获取中断状态
    uint32_t interrupt_status = DL_UART_Main_getPendingInterrupt(UART_0_INST);

    // 处理接收中断
    if (interrupt_status == DL_UART_IIDX_RX)
    {
        // 读取接收到的字节
        uint8_t received_byte = DL_UART_Main_receiveData(UART_0_INST);
        // 检查缓冲区是否有空间
        uint16_t next_write_index = (rx_write_index + 1) % UART_RX_BUFFER_SIZE;
        if (next_write_index != rx_read_index)
        {
            // 存入缓冲区
            uart_rx_buffer[rx_write_index] = received_byte;
            rx_write_index = next_write_index;

            // 更新最后接收时间（用于软件超时）
            last_rx_time = system_tick_ms;
        }
    }

    // 清除中断标志
    DL_UART_Main_clearInterruptStatus(UART_0_INST, interrupt_status);
}

/**
 * @brief 串口处理函数（在调度器中调用）
 */
void uart_proc(void)
{
    // 软件超时检查
    if ((rx_write_index != rx_read_index) &&
        (system_tick_ms - last_rx_time > 5)) // 5ms超时
        {  

        // 计算数据包长度
        if (rx_write_index >= rx_read_index)
        {
            packet_length = rx_write_index - rx_read_index;
        } else
        {
            packet_length = (UART_RX_BUFFER_SIZE - rx_read_index) + rx_write_index;
        }

        // 标记数据包就绪
        if (packet_length > 0)
        {
            packet_ready = true;
        }
    }

    // 检查是否有完整数据包
    if (packet_ready)
    {
        // 创建临时缓冲区处理数据
        uint8_t temp_buffer[UART_RX_BUFFER_SIZE];
        uint16_t data_length = 0;
        
        // 从环形缓冲区复制数据到临时缓冲区
        for (uint16_t i = 0; i < packet_length; i++) 
        {
            temp_buffer[i] = uart_data_buffer[i] = uart_rx_buffer[rx_read_index];
            rx_read_index = (rx_read_index + 1) % UART_RX_BUFFER_SIZE;
            data_length++;
        }
        
        // 处理数据包
        process_uart_packet(temp_buffer, data_length);
        
        // 重置状态
        packet_ready = false;
        packet_length = 0;

    }
}

/**
 * @brief 处理接收到的数据包
 * @param data 数据指针
 * @param length 数据长度
 */
void process_uart_packet(uint8_t* data, uint16_t length)
{
    // DL_GPIO_togglePins(LED_pins_PORT, LED_pins_LED1_PIN);
    // data[length] = uart_data_buffer[length] = '\0';
    // printf("%s ",data);
    // printf("%s ",uart_data_buffer);
    // 在这里添加你的数据包解析逻辑
    // 例如：协议解析、命令处理等
}

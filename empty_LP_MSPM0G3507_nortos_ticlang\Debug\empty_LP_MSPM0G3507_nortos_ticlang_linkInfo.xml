<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.0.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\bin\tiarmlnk -ID:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib -o empty_LP_MSPM0G3507_nortos_ticlang.out -mempty_LP_MSPM0G3507_nortos_ticlang.map -iD:/ti/mspm0_sdk_2_05_01_00/source -iE:/study/ti/empty_LP_MSPM0G3507_nortos_ticlang -iE:/study/ti/empty_LP_MSPM0G3507_nortos_ticlang/Debug/syscfg -iD:/ti/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=empty_LP_MSPM0G3507_nortos_ticlang_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./APP/Driver_adc.o ./APP/Driver_key.o ./APP/Driver_uart.o ./APP/schedule.o -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x68775fc2</link_time>
   <link_errors>0x0</link_errors>
   <output_file>E:\study\ti\empty_LP_MSPM0G3507_nortos_ticlang\Debug\empty_LP_MSPM0G3507_nortos_ticlang.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0xc05</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>E:\study\ti\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>E:\study\ti\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>E:\study\ti\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>E:\study\ti\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\APP\</path>
         <kind>object</kind>
         <file>Driver_adc.o</file>
         <name>Driver_adc.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>E:\study\ti\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\APP\</path>
         <kind>object</kind>
         <file>Driver_key.o</file>
         <name>Driver_key.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>E:\study\ti\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\APP\</path>
         <kind>object</kind>
         <file>Driver_uart.o</file>
         <name>Driver_uart.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>E:\study\ti\empty_LP_MSPM0G3507_nortos_ticlang\Debug\.\APP\</path>
         <kind>object</kind>
         <file>schedule.o</file>
         <name>schedule.o</name>
      </input_file>
      <input_file id="fl-14">
         <path>E:\study\ti\empty_LP_MSPM0G3507_nortos_ticlang\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-15">
         <path>D:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_adc12.o</name>
      </input_file>
      <input_file id="fl-16">
         <path>D:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-17">
         <path>D:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_dma.o</name>
      </input_file>
      <input_file id="fl-18">
         <path>D:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_timer.o</name>
      </input_file>
      <input_file id="fl-19">
         <path>D:\ti\mspm0_sdk_2_05_01_00\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_uart.o</name>
      </input_file>
      <input_file id="fl-2e">
         <path>D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-2f">
         <path>D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-ce">
         <path>D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>divsf3.S.obj</name>
      </input_file>
      <input_file id="fl-cf">
         <path>D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>fixsfsi.S.obj</name>
      </input_file>
      <input_file id="fl-d0">
         <path>D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>floatsisf.S.obj</name>
      </input_file>
      <input_file id="fl-d1">
         <path>D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-d2">
         <path>D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-d3">
         <path>D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-d4">
         <path>D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-d5">
         <path>D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-d6">
         <path>D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-18">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-13a">
         <name>.text.key_read</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x104</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-103">
         <name>.text.DL_Timer_initTimerMode</name>
         <load_address>0x1c4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x1c4</run_address>
         <size>0xe8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-120">
         <name>.text.uart_proc</name>
         <load_address>0x2ac</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x2ac</run_address>
         <size>0xa4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-da">
         <name>.text.SYSCFG_DL_ADC12_1_init</name>
         <load_address>0x350</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x350</run_address>
         <size>0x9c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.text:memcpy</name>
         <load_address>0x3ec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x3ec</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-33">
         <name>.text.Default_Handler</name>
         <load_address>0x486</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x486</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.text.SYSCFG_DL_ADC12_0_init</name>
         <load_address>0x488</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x488</run_address>
         <size>0x98</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.text.SYSCFG_DL_UART_0_init</name>
         <load_address>0x520</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x520</run_address>
         <size>0x84</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.text.__divsf3</name>
         <load_address>0x5a4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5a4</run_address>
         <size>0x82</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-ce"/>
      </object_component>
      <object_component id="oc-96">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0x626</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x626</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-1c">
         <name>.text:decompress:lzss</name>
         <load_address>0x628</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x628</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.text.User_ADC_Init</name>
         <load_address>0x6a4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6a4</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d2">
         <name>.text:memset</name>
         <load_address>0x70c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x70c</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-121">
         <name>.text.adc_proc</name>
         <load_address>0x76e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x76e</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-3f">
         <name>.text.UART0_IRQHandler</name>
         <load_address>0x770</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x770</run_address>
         <size>0x58</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0x7c8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7c8</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d3">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0x81c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x81c</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-134">
         <name>.text.DL_DMA_initChannel</name>
         <load_address>0x870</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x870</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.text.SYSCFG_DL_TIMER_0_init</name>
         <load_address>0x8bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8bc</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10c">
         <name>.text.DL_UART_init</name>
         <load_address>0x908</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x908</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.text.scheduler_run</name>
         <load_address>0x950</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x950</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-10f">
         <name>.text.DL_ADC12_setClockConfig</name>
         <load_address>0x994</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x994</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x9d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9d4</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-71">
         <name>.text.__aeabi_uidivmod</name>
         <load_address>0xa14</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa14</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.text.key_proc</name>
         <load_address>0xa54</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa54</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0xa94</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa94</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.text.__fixsfsi</name>
         <load_address>0xad0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xad0</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-cf"/>
      </object_component>
      <object_component id="oc-88">
         <name>.text.main</name>
         <load_address>0xb08</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb08</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0xb40</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb40</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-39">
         <name>.text.ADC0_IRQHandler</name>
         <load_address>0xb74</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb74</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-3e">
         <name>.text.ADC1_IRQHandler</name>
         <load_address>0xba4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xba4</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0xbd4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbd4</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-60">
         <name>.text:_c_int00_noargs</name>
         <load_address>0xc04</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc04</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-118">
         <name>.text.SYSCFG_DL_DMA_CH1_init</name>
         <load_address>0xc2c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc2c</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-117">
         <name>.text.SYSCFG_DL_DMA_CH0_init</name>
         <load_address>0xc50</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc50</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.text.DL_Timer_setClockConfig</name>
         <load_address>0xc70</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc70</run_address>
         <size>0x1c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-106">
         <name>.text.DL_UART_setClockConfig</name>
         <load_address>0xc8c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc8c</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-20">
         <name>.text:decompress:none</name>
         <load_address>0xc9e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc9e</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-34">
         <name>.text.SysTick_Handler</name>
         <load_address>0xcb0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcb0</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.text:decompress:ZI</name>
         <load_address>0xcc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcc0</run_address>
         <size>0x10</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-db">
         <name>.text.SYSCFG_DL_DMA_init</name>
         <load_address>0xcd0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcd0</run_address>
         <size>0xc</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-7f">
         <name>.text.__aeabi_memclr</name>
         <load_address>0xcdc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcdc</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d2"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.text.scheduler_init</name>
         <load_address>0xce8</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xce8</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-f9">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0xcf4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xcf4</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-50">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0xd00</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd00</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d1"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.text.Reset_Handler</name>
         <load_address>0xd08</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd08</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-84">
         <name>.text._system_pre_init</name>
         <load_address>0xd0c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd0c</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.text:abort</name>
         <load_address>0xd10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xd10</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-18a">
         <name>.cinit..data.load</name>
         <load_address>0xd80</load_address>
         <readonly>true</readonly>
         <run_address>0xd80</run_address>
         <size>0x1c</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-188">
         <name>__TI_handler_table</name>
         <load_address>0xd9c</load_address>
         <readonly>true</readonly>
         <run_address>0xd9c</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-18b">
         <name>.cinit..bss.load</name>
         <load_address>0xda8</load_address>
         <readonly>true</readonly>
         <run_address>0xda8</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-189">
         <name>__TI_cinit_table</name>
         <load_address>0xdb0</load_address>
         <readonly>true</readonly>
         <run_address>0xdb0</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-138">
         <name>.rodata.gDMA_CH0Config</name>
         <load_address>0xd18</load_address>
         <readonly>true</readonly>
         <run_address>0xd18</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-139">
         <name>.rodata.gDMA_CH1Config</name>
         <load_address>0xd30</load_address>
         <readonly>true</readonly>
         <run_address>0xd30</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-105">
         <name>.rodata.gTIMER_0TimerConfig</name>
         <load_address>0xd48</load_address>
         <readonly>true</readonly>
         <run_address>0xd48</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10e">
         <name>.rodata.gUART_0Config</name>
         <load_address>0xd5c</load_address>
         <readonly>true</readonly>
         <run_address>0xd5c</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-10d">
         <name>.rodata.gUART_0ClockConfig</name>
         <load_address>0xd66</load_address>
         <readonly>true</readonly>
         <run_address>0xd66</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-115">
         <name>.rodata.gADC12_0ClockConfig</name>
         <load_address>0xd68</load_address>
         <readonly>true</readonly>
         <run_address>0xd68</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-116">
         <name>.rodata.gADC12_1ClockConfig</name>
         <load_address>0xd70</load_address>
         <readonly>true</readonly>
         <run_address>0xd70</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-104">
         <name>.rodata.gTIMER_0ClockConfig</name>
         <load_address>0xd78</load_address>
         <readonly>true</readonly>
         <run_address>0xd78</run_address>
         <size>0x3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-152">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-6c">
         <name>.data.adc_flag</name>
         <load_address>0x20201144</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20201144</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-70">
         <name>.data.adc1_flag</name>
         <load_address>0x20201142</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20201142</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-6b">
         <name>.data.adc_done_count</name>
         <load_address>0x20201143</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20201143</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-13c">
         <name>.data.key_val</name>
         <load_address>0x20201149</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20201149</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-13d">
         <name>.data.key_old</name>
         <load_address>0x20201147</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20201147</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-13e">
         <name>.data.key_down</name>
         <load_address>0x20201145</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20201145</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-13f">
         <name>.data.key_up</name>
         <load_address>0x20201148</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20201148</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-140">
         <name>.data.key_early</name>
         <load_address>0x20201146</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20201146</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-75">
         <name>.data.rx_write_index</name>
         <load_address>0x20201140</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20201140</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-76">
         <name>.data.rx_read_index</name>
         <load_address>0x2020113e</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020113e</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-144">
         <name>.data.packet_ready</name>
         <load_address>0x2020114a</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020114a</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-143">
         <name>.data.packet_length</name>
         <load_address>0x2020113c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020113c</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-78">
         <name>.data.last_rx_time</name>
         <load_address>0x20201138</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20201138</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.data.scheduler_task</name>
         <load_address>0x20201114</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20201114</run_address>
         <size>0x24</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.common:gTIMER_0Backup</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20201000</run_address>
         <size>0xbc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-e8">
         <name>.common:gADCSamples</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200800</run_address>
         <size>0x800</size>
         <alignment>0x2</alignment>
      </object_component>
      <object_component id="oc-e9">
         <name>.common:gADC1Samples</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x800</size>
         <alignment>0x2</alignment>
      </object_component>
      <object_component id="oc-77">
         <name>.common:uart_rx_buffer</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202010e4</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-145">
         <name>.common:uart_data_buffer</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x202010bc</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-67">
         <name>.common:system_tick_ms</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x2020110c</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-ea">
         <name>.common:task_num</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20201110</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-1a">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-18d">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-ae">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x13d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-de">
         <name>.debug_abbrev</name>
         <load_address>0x13d</load_address>
         <run_address>0x13d</run_address>
         <size>0x275</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-45">
         <name>.debug_abbrev</name>
         <load_address>0x3b2</load_address>
         <run_address>0x3b2</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_abbrev</name>
         <load_address>0x41f</load_address>
         <run_address>0x41f</run_address>
         <size>0x1da</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-141">
         <name>.debug_abbrev</name>
         <load_address>0x5f9</load_address>
         <run_address>0x5f9</run_address>
         <size>0x14b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-79">
         <name>.debug_abbrev</name>
         <load_address>0x744</load_address>
         <run_address>0x744</run_address>
         <size>0x19e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-68">
         <name>.debug_abbrev</name>
         <load_address>0x8e2</load_address>
         <run_address>0x8e2</run_address>
         <size>0xed</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-132">
         <name>.debug_abbrev</name>
         <load_address>0x9cf</load_address>
         <run_address>0x9cf</run_address>
         <size>0x171</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.debug_abbrev</name>
         <load_address>0xb40</load_address>
         <run_address>0xb40</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-146">
         <name>.debug_abbrev</name>
         <load_address>0xba2</load_address>
         <run_address>0xba2</run_address>
         <size>0x180</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-12e">
         <name>.debug_abbrev</name>
         <load_address>0xd22</load_address>
         <run_address>0xd22</run_address>
         <size>0x286</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-130">
         <name>.debug_abbrev</name>
         <load_address>0xfa8</load_address>
         <run_address>0xfa8</run_address>
         <size>0x29b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_abbrev</name>
         <load_address>0x1243</load_address>
         <run_address>0x1243</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-f2">
         <name>.debug_abbrev</name>
         <load_address>0x12f2</load_address>
         <run_address>0x12f2</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_abbrev</name>
         <load_address>0x1462</load_address>
         <run_address>0x1462</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.debug_abbrev</name>
         <load_address>0x149b</load_address>
         <run_address>0x149b</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_abbrev</name>
         <load_address>0x155d</load_address>
         <run_address>0x155d</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_abbrev</name>
         <load_address>0x15cd</load_address>
         <run_address>0x15cd</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_abbrev</name>
         <load_address>0x165a</load_address>
         <run_address>0x165a</run_address>
         <size>0xb3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-119">
         <name>.debug_abbrev</name>
         <load_address>0x170d</load_address>
         <run_address>0x170d</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ce"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.debug_abbrev</name>
         <load_address>0x1734</load_address>
         <run_address>0x1734</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cf"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_abbrev</name>
         <load_address>0x175b</load_address>
         <run_address>0x175b</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d1"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_abbrev</name>
         <load_address>0x1782</load_address>
         <run_address>0x1782</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d2"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_abbrev</name>
         <load_address>0x17a7</load_address>
         <run_address>0x17a7</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-c9">
         <name>.debug_abbrev</name>
         <load_address>0x17ce</load_address>
         <run_address>0x17ce</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-cc">
         <name>.debug_abbrev</name>
         <load_address>0x1827</load_address>
         <run_address>0x1827</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-f8">
         <name>.debug_abbrev</name>
         <load_address>0x184c</load_address>
         <run_address>0x184c</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-18f">
         <name>.debug_abbrev</name>
         <load_address>0x1871</load_address>
         <run_address>0x1871</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-89">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x955</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.debug_info</name>
         <load_address>0x955</load_address>
         <run_address>0x955</run_address>
         <size>0x41d3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-19">
         <name>.debug_info</name>
         <load_address>0x4b28</load_address>
         <run_address>0x4b28</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-3c">
         <name>.debug_info</name>
         <load_address>0x4ba8</load_address>
         <run_address>0x4ba8</run_address>
         <size>0x1485</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.debug_info</name>
         <load_address>0x602d</load_address>
         <run_address>0x602d</run_address>
         <size>0xb38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_info</name>
         <load_address>0x6b65</load_address>
         <run_address>0x6b65</run_address>
         <size>0x85e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-36">
         <name>.debug_info</name>
         <load_address>0x73c3</load_address>
         <run_address>0x73c3</run_address>
         <size>0x156</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-110">
         <name>.debug_info</name>
         <load_address>0x7519</load_address>
         <run_address>0x7519</run_address>
         <size>0x745</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.debug_info</name>
         <load_address>0x7c5e</load_address>
         <run_address>0x7c5e</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-135">
         <name>.debug_info</name>
         <load_address>0x7cd3</load_address>
         <run_address>0x7cd3</run_address>
         <size>0x6ea</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-101">
         <name>.debug_info</name>
         <load_address>0x83bd</load_address>
         <run_address>0x83bd</run_address>
         <size>0x3172</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.debug_info</name>
         <load_address>0xb52f</load_address>
         <run_address>0xb52f</run_address>
         <size>0x12a6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-1b">
         <name>.debug_info</name>
         <load_address>0xc7d5</load_address>
         <run_address>0xc7d5</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.debug_info</name>
         <load_address>0xcbf8</load_address>
         <run_address>0xcbf8</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-85">
         <name>.debug_info</name>
         <load_address>0xd33c</load_address>
         <run_address>0xd33c</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.debug_info</name>
         <load_address>0xd382</load_address>
         <run_address>0xd382</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-21">
         <name>.debug_info</name>
         <load_address>0xd514</load_address>
         <run_address>0xd514</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1d">
         <name>.debug_info</name>
         <load_address>0xd5da</load_address>
         <run_address>0xd5da</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.debug_info</name>
         <load_address>0xd756</load_address>
         <run_address>0xd756</run_address>
         <size>0xed</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_info</name>
         <load_address>0xd843</load_address>
         <run_address>0xd843</run_address>
         <size>0x18d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ce"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_info</name>
         <load_address>0xd9d0</load_address>
         <run_address>0xd9d0</run_address>
         <size>0x18f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cf"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_info</name>
         <load_address>0xdb5f</load_address>
         <run_address>0xdb5f</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d1"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_info</name>
         <load_address>0xdcf8</load_address>
         <run_address>0xdcf8</run_address>
         <size>0x1b5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d2"/>
      </object_component>
      <object_component id="oc-72">
         <name>.debug_info</name>
         <load_address>0xdead</load_address>
         <run_address>0xdead</run_address>
         <size>0x1bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_info</name>
         <load_address>0xe069</load_address>
         <run_address>0xe069</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_info</name>
         <load_address>0xe0ee</load_address>
         <run_address>0xe0ee</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-ce">
         <name>.debug_info</name>
         <load_address>0xe3e8</load_address>
         <run_address>0xe3e8</run_address>
         <size>0x244</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-18e">
         <name>.debug_info</name>
         <load_address>0xe62c</load_address>
         <run_address>0xe62c</run_address>
         <size>0xa8</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x714</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-df">
         <name>.debug_str</name>
         <load_address>0x714</load_address>
         <run_address>0x714</run_address>
         <size>0x2fad</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-46">
         <name>.debug_str</name>
         <load_address>0x36c1</load_address>
         <run_address>0x36c1</run_address>
         <size>0x15f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_str</name>
         <load_address>0x3820</load_address>
         <run_address>0x3820</run_address>
         <size>0xa12</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-142">
         <name>.debug_str</name>
         <load_address>0x4232</load_address>
         <run_address>0x4232</run_address>
         <size>0x4f7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_str</name>
         <load_address>0x4729</load_address>
         <run_address>0x4729</run_address>
         <size>0x69f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-69">
         <name>.debug_str</name>
         <load_address>0x4dc8</load_address>
         <run_address>0x4dc8</run_address>
         <size>0x195</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-133">
         <name>.debug_str</name>
         <load_address>0x4f5d</load_address>
         <run_address>0x4f5d</run_address>
         <size>0x631</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.debug_str</name>
         <load_address>0x558e</load_address>
         <run_address>0x558e</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-147">
         <name>.debug_str</name>
         <load_address>0x56fb</load_address>
         <run_address>0x56fb</run_address>
         <size>0x64a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-12f">
         <name>.debug_str</name>
         <load_address>0x5d45</load_address>
         <run_address>0x5d45</run_address>
         <size>0x1dcc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-131">
         <name>.debug_str</name>
         <load_address>0x7b11</load_address>
         <run_address>0x7b11</run_address>
         <size>0xce3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_str</name>
         <load_address>0x87f4</load_address>
         <run_address>0x87f4</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.debug_str</name>
         <load_address>0x8a19</load_address>
         <run_address>0x8a19</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_str</name>
         <load_address>0x8d48</load_address>
         <run_address>0x8d48</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_str</name>
         <load_address>0x8e3d</load_address>
         <run_address>0x8e3d</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-55">
         <name>.debug_str</name>
         <load_address>0x8fd8</load_address>
         <run_address>0x8fd8</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_str</name>
         <load_address>0x9140</load_address>
         <run_address>0x9140</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_str</name>
         <load_address>0x9315</load_address>
         <run_address>0x9315</run_address>
         <size>0x13f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-ca">
         <name>.debug_str</name>
         <load_address>0x9454</load_address>
         <run_address>0x9454</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-8a">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_frame</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x154</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-32">
         <name>.debug_frame</name>
         <load_address>0x174</load_address>
         <run_address>0x174</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-3b">
         <name>.debug_frame</name>
         <load_address>0x1a4</load_address>
         <run_address>0x1a4</run_address>
         <size>0x70</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.debug_frame</name>
         <load_address>0x214</load_address>
         <run_address>0x214</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-40">
         <name>.debug_frame</name>
         <load_address>0x25c</load_address>
         <run_address>0x25c</run_address>
         <size>0x78</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-37">
         <name>.debug_frame</name>
         <load_address>0x2d4</load_address>
         <run_address>0x2d4</run_address>
         <size>0x50</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-113">
         <name>.debug_frame</name>
         <load_address>0x324</load_address>
         <run_address>0x324</run_address>
         <size>0x4c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.debug_frame</name>
         <load_address>0x370</load_address>
         <run_address>0x370</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-136">
         <name>.debug_frame</name>
         <load_address>0x390</load_address>
         <run_address>0x390</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.debug_frame</name>
         <load_address>0x3c0</load_address>
         <run_address>0x3c0</run_address>
         <size>0x408</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-108">
         <name>.debug_frame</name>
         <load_address>0x7c8</load_address>
         <run_address>0x7c8</run_address>
         <size>0x1b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_frame</name>
         <load_address>0x980</load_address>
         <run_address>0x980</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_frame</name>
         <load_address>0xa10</load_address>
         <run_address>0xa10</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-86">
         <name>.debug_frame</name>
         <load_address>0xb10</load_address>
         <run_address>0xb10</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_frame</name>
         <load_address>0xb30</load_address>
         <run_address>0xb30</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-23">
         <name>.debug_frame</name>
         <load_address>0xb68</load_address>
         <run_address>0xb68</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1f">
         <name>.debug_frame</name>
         <load_address>0xb90</load_address>
         <run_address>0xb90</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.debug_frame</name>
         <load_address>0xbc0</load_address>
         <run_address>0xbc0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-98">
         <name>.debug_frame</name>
         <load_address>0xbf0</load_address>
         <run_address>0xbf0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-8b">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x229</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_line</name>
         <load_address>0x229</load_address>
         <run_address>0x229</run_address>
         <size>0x849</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_line</name>
         <load_address>0xa72</load_address>
         <run_address>0xa72</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-3a">
         <name>.debug_line</name>
         <load_address>0xb2a</load_address>
         <run_address>0xb2a</run_address>
         <size>0x363</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.debug_line</name>
         <load_address>0xe8d</load_address>
         <run_address>0xe8d</run_address>
         <size>0x3ad</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_line</name>
         <load_address>0x123a</load_address>
         <run_address>0x123a</run_address>
         <size>0x338</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_line</name>
         <load_address>0x1572</load_address>
         <run_address>0x1572</run_address>
         <size>0x157</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-114">
         <name>.debug_line</name>
         <load_address>0x16c9</load_address>
         <run_address>0x16c9</run_address>
         <size>0x27f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.debug_line</name>
         <load_address>0x1948</load_address>
         <run_address>0x1948</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-137">
         <name>.debug_line</name>
         <load_address>0x1ac0</load_address>
         <run_address>0x1ac0</run_address>
         <size>0x248</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-102">
         <name>.debug_line</name>
         <load_address>0x1d08</load_address>
         <run_address>0x1d08</run_address>
         <size>0x176e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-109">
         <name>.debug_line</name>
         <load_address>0x3476</load_address>
         <run_address>0x3476</run_address>
         <size>0xa17</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_line</name>
         <load_address>0x3e8d</load_address>
         <run_address>0x3e8d</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_line</name>
         <load_address>0x4069</load_address>
         <run_address>0x4069</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-87">
         <name>.debug_line</name>
         <load_address>0x4583</load_address>
         <run_address>0x4583</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-5a">
         <name>.debug_line</name>
         <load_address>0x45c1</load_address>
         <run_address>0x45c1</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-22">
         <name>.debug_line</name>
         <load_address>0x46bf</load_address>
         <run_address>0x46bf</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-1e">
         <name>.debug_line</name>
         <load_address>0x477f</load_address>
         <run_address>0x477f</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.debug_line</name>
         <load_address>0x4947</load_address>
         <run_address>0x4947</run_address>
         <size>0x69</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.debug_line</name>
         <load_address>0x49b0</load_address>
         <run_address>0x49b0</run_address>
         <size>0xdc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ce"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.debug_line</name>
         <load_address>0x4a8c</load_address>
         <run_address>0x4a8c</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cf"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_line</name>
         <load_address>0x4b44</load_address>
         <run_address>0x4b44</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d1"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_line</name>
         <load_address>0x4be8</load_address>
         <run_address>0x4be8</run_address>
         <size>0xba</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d2"/>
      </object_component>
      <object_component id="oc-74">
         <name>.debug_line</name>
         <load_address>0x4ca2</load_address>
         <run_address>0x4ca2</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-99">
         <name>.debug_line</name>
         <load_address>0x4d64</load_address>
         <run_address>0x4d64</run_address>
         <size>0xb5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_line</name>
         <load_address>0x4e19</load_address>
         <run_address>0x4e19</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-d0">
         <name>.debug_line</name>
         <load_address>0x4eb9</load_address>
         <run_address>0x4eb9</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1f2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_loc</name>
         <load_address>0x1f2</load_address>
         <run_address>0x1f2</run_address>
         <size>0xb8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-13b">
         <name>.debug_loc</name>
         <load_address>0x2aa</load_address>
         <run_address>0x2aa</run_address>
         <size>0x89</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-41">
         <name>.debug_loc</name>
         <load_address>0x333</load_address>
         <run_address>0x333</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-6a">
         <name>.debug_loc</name>
         <load_address>0x3cd</load_address>
         <run_address>0x3cd</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-112">
         <name>.debug_loc</name>
         <load_address>0x40e</load_address>
         <run_address>0x40e</run_address>
         <size>0xc7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-12d">
         <name>.debug_loc</name>
         <load_address>0x4d5</load_address>
         <run_address>0x4d5</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-16"/>
      </object_component>
      <object_component id="oc-148">
         <name>.debug_loc</name>
         <load_address>0x4e8</load_address>
         <run_address>0x4e8</run_address>
         <size>0xd0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-100">
         <name>.debug_loc</name>
         <load_address>0x5b8</load_address>
         <run_address>0x5b8</run_address>
         <size>0x1a27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-107">
         <name>.debug_loc</name>
         <load_address>0x1fdf</load_address>
         <run_address>0x1fdf</run_address>
         <size>0x7bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_loc</name>
         <load_address>0x279b</load_address>
         <run_address>0x279b</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_loc</name>
         <load_address>0x2873</load_address>
         <run_address>0x2873</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_loc</name>
         <load_address>0x2c97</load_address>
         <run_address>0x2c97</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-56">
         <name>.debug_loc</name>
         <load_address>0x2e03</load_address>
         <run_address>0x2e03</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_loc</name>
         <load_address>0x2e72</load_address>
         <run_address>0x2e72</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.debug_loc</name>
         <load_address>0x2fd9</load_address>
         <run_address>0x2fd9</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-cb">
         <name>.debug_loc</name>
         <load_address>0x2fff</load_address>
         <run_address>0x2fff</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x78</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_ranges</name>
         <load_address>0x78</load_address>
         <run_address>0x78</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-3d">
         <name>.debug_ranges</name>
         <load_address>0x90</load_address>
         <run_address>0x90</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.debug_ranges</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_ranges</name>
         <load_address>0xd8</load_address>
         <run_address>0xd8</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-38">
         <name>.debug_ranges</name>
         <load_address>0x100</load_address>
         <run_address>0x100</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-111">
         <name>.debug_ranges</name>
         <load_address>0x138</load_address>
         <run_address>0x138</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-15"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.debug_ranges</name>
         <load_address>0x150</load_address>
         <run_address>0x150</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-10b">
         <name>.debug_ranges</name>
         <load_address>0x328</load_address>
         <run_address>0x328</run_address>
         <size>0x1a8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-19"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_ranges</name>
         <load_address>0x4d0</load_address>
         <run_address>0x4d0</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2e"/>
      </object_component>
      <object_component id="oc-b9">
         <name>.debug_ranges</name>
         <load_address>0x518</load_address>
         <run_address>0x518</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-30"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.debug_ranges</name>
         <load_address>0x560</load_address>
         <run_address>0x560</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-33"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_ranges</name>
         <load_address>0x578</load_address>
         <run_address>0x578</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.debug_ranges</name>
         <load_address>0x5c8</load_address>
         <run_address>0x5c8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-83">
         <name>.debug_ranges</name>
         <load_address>0x5e0</load_address>
         <run_address>0x5e0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d2"/>
      </object_component>
      <object_component id="oc-97">
         <name>.debug_ranges</name>
         <load_address>0x608</load_address>
         <run_address>0x608</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d4"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_ranges</name>
         <load_address>0x620</load_address>
         <run_address>0x620</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-d1">
         <name>.debug_ranges</name>
         <load_address>0x648</load_address>
         <run_address>0x648</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-ce"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-cf"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d1"/>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_aranges</name>
         <load_address>0x60</load_address>
         <run_address>0x60</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d2"/>
      </object_component>
      <object_component id="oc-73">
         <name>.debug_aranges</name>
         <load_address>0x88</load_address>
         <run_address>0x88</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d3"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_aranges</name>
         <load_address>0xa8</load_address>
         <run_address>0xa8</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d5"/>
      </object_component>
      <object_component id="oc-cf">
         <name>.debug_aranges</name>
         <load_address>0xd0</load_address>
         <run_address>0xd0</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d6"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-18"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0xc58</size>
         <contents>
            <object_component_ref idref="oc-13a"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-96"/>
            <object_component_ref idref="oc-1c"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-d2"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-3f"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-d3"/>
            <object_component_ref idref="oc-134"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-10c"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-10f"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-88"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-39"/>
            <object_component_ref idref="oc-3e"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-20"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-f9"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-84"/>
            <object_component_ref idref="oc-b0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0xd80</load_address>
         <run_address>0xd80</run_address>
         <size>0x40</size>
         <contents>
            <object_component_ref idref="oc-18a"/>
            <object_component_ref idref="oc-188"/>
            <object_component_ref idref="oc-18b"/>
            <object_component_ref idref="oc-189"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0xd18</load_address>
         <run_address>0xd18</run_address>
         <size>0x68</size>
         <contents>
            <object_component_ref idref="oc-138"/>
            <object_component_ref idref="oc-139"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-10e"/>
            <object_component_ref idref="oc-10d"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-104"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-152"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20201114</run_address>
         <size>0x37</size>
         <contents>
            <object_component_ref idref="oc-6c"/>
            <object_component_ref idref="oc-70"/>
            <object_component_ref idref="oc-6b"/>
            <object_component_ref idref="oc-13c"/>
            <object_component_ref idref="oc-13d"/>
            <object_component_ref idref="oc-13e"/>
            <object_component_ref idref="oc-13f"/>
            <object_component_ref idref="oc-140"/>
            <object_component_ref idref="oc-75"/>
            <object_component_ref idref="oc-76"/>
            <object_component_ref idref="oc-144"/>
            <object_component_ref idref="oc-143"/>
            <object_component_ref idref="oc-78"/>
            <object_component_ref idref="oc-eb"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200000</run_address>
         <size>0x1111</size>
         <contents>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-77"/>
            <object_component_ref idref="oc-145"/>
            <object_component_ref idref="oc-67"/>
            <object_component_ref idref="oc-ea"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-1a"/>
            <object_component_ref idref="oc-18d"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-149" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14a" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14b" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14c" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14d" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14e" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-150" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-16c" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1880</size>
         <contents>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-141"/>
            <object_component_ref idref="oc-79"/>
            <object_component_ref idref="oc-68"/>
            <object_component_ref idref="oc-132"/>
            <object_component_ref idref="oc-12b"/>
            <object_component_ref idref="oc-146"/>
            <object_component_ref idref="oc-12e"/>
            <object_component_ref idref="oc-130"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-f2"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-c9"/>
            <object_component_ref idref="oc-cc"/>
            <object_component_ref idref="oc-f8"/>
            <object_component_ref idref="oc-18f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-16e" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xe6d4</size>
         <contents>
            <object_component_ref idref="oc-89"/>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-19"/>
            <object_component_ref idref="oc-3c"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-36"/>
            <object_component_ref idref="oc-110"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-135"/>
            <object_component_ref idref="oc-101"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-1b"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-85"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-21"/>
            <object_component_ref idref="oc-1d"/>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-ce"/>
            <object_component_ref idref="oc-18e"/>
         </contents>
      </logical_group>
      <logical_group id="lg-170" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x95e7</size>
         <contents>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-142"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-69"/>
            <object_component_ref idref="oc-133"/>
            <object_component_ref idref="oc-12c"/>
            <object_component_ref idref="oc-147"/>
            <object_component_ref idref="oc-12f"/>
            <object_component_ref idref="oc-131"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-ca"/>
         </contents>
      </logical_group>
      <logical_group id="lg-172" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc20</size>
         <contents>
            <object_component_ref idref="oc-8a"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-3b"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-40"/>
            <object_component_ref idref="oc-37"/>
            <object_component_ref idref="oc-113"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-136"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-86"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-23"/>
            <object_component_ref idref="oc-1f"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-98"/>
         </contents>
      </logical_group>
      <logical_group id="lg-174" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x4f39</size>
         <contents>
            <object_component_ref idref="oc-8b"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-3a"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-137"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-87"/>
            <object_component_ref idref="oc-5a"/>
            <object_component_ref idref="oc-22"/>
            <object_component_ref idref="oc-1e"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-74"/>
            <object_component_ref idref="oc-99"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-d0"/>
         </contents>
      </logical_group>
      <logical_group id="lg-176" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x301f</size>
         <contents>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-13b"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-6a"/>
            <object_component_ref idref="oc-112"/>
            <object_component_ref idref="oc-12d"/>
            <object_component_ref idref="oc-148"/>
            <object_component_ref idref="oc-100"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-56"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-cb"/>
         </contents>
      </logical_group>
      <logical_group id="lg-178" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x670</size>
         <contents>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-3d"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-38"/>
            <object_component_ref idref="oc-111"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-10b"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-b9"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-83"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-d1"/>
         </contents>
      </logical_group>
      <logical_group id="lg-182" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xf8</size>
         <contents>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-cf"/>
         </contents>
      </logical_group>
      <logical_group id="lg-18c" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-193" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xdc0</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-194" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x114b</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-f"/>
            <logical_group_ref idref="lg-e"/>
         </contents>
      </load_segment>
      <load_segment id="lg-195" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0xdc0</used_space>
         <unused_space>0x1f240</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0xc58</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xd18</start_address>
               <size>0x68</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xd80</start_address>
               <size>0x40</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0xdc0</start_address>
               <size>0x1f240</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x1348</used_space>
         <unused_space>0x6cb8</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-14e"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-150"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x1111</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x20201111</start_address>
               <size>0x3</size>
            </available_space>
            <allocated_space>
               <start_address>0x20201114</start_address>
               <size>0x37</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x2020114b</start_address>
               <size>0x6cb5</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0xd80</load_address>
            <load_size>0x1c</load_size>
            <run_address>0x20201114</run_address>
            <run_size>0x37</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0xda8</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x1111</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0xdb0</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0xdc0</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0xdc0</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0xd9c</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0xda8</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-3b">
         <name>main</name>
         <value>0xb09</value>
         <object_component_ref idref="oc-88"/>
      </symbol>
      <symbol id="sm-76">
         <name>SYSCFG_DL_init</name>
         <value>0xb41</value>
         <object_component_ref idref="oc-a6"/>
      </symbol>
      <symbol id="sm-77">
         <name>SYSCFG_DL_initPower</name>
         <value>0x81d</value>
         <object_component_ref idref="oc-d3"/>
      </symbol>
      <symbol id="sm-78">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0x7c9</value>
         <object_component_ref idref="oc-d4"/>
      </symbol>
      <symbol id="sm-79">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x9d5</value>
         <object_component_ref idref="oc-d5"/>
      </symbol>
      <symbol id="sm-7a">
         <name>SYSCFG_DL_TIMER_0_init</name>
         <value>0x8bd</value>
         <object_component_ref idref="oc-d7"/>
      </symbol>
      <symbol id="sm-7b">
         <name>SYSCFG_DL_UART_0_init</name>
         <value>0x521</value>
         <object_component_ref idref="oc-d8"/>
      </symbol>
      <symbol id="sm-7c">
         <name>SYSCFG_DL_ADC12_0_init</name>
         <value>0x489</value>
         <object_component_ref idref="oc-d9"/>
      </symbol>
      <symbol id="sm-7d">
         <name>SYSCFG_DL_ADC12_1_init</name>
         <value>0x351</value>
         <object_component_ref idref="oc-da"/>
      </symbol>
      <symbol id="sm-7e">
         <name>SYSCFG_DL_DMA_init</name>
         <value>0xcd1</value>
         <object_component_ref idref="oc-db"/>
      </symbol>
      <symbol id="sm-7f">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0xbd5</value>
         <object_component_ref idref="oc-dc"/>
      </symbol>
      <symbol id="sm-80">
         <name>gTIMER_0Backup</name>
         <value>0x20201000</value>
      </symbol>
      <symbol id="sm-81">
         <name>SYSCFG_DL_DMA_CH0_init</name>
         <value>0xc51</value>
         <object_component_ref idref="oc-117"/>
      </symbol>
      <symbol id="sm-82">
         <name>SYSCFG_DL_DMA_CH1_init</name>
         <value>0xc2d</value>
         <object_component_ref idref="oc-118"/>
      </symbol>
      <symbol id="sm-8d">
         <name>Default_Handler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-8e">
         <name>Reset_Handler</name>
         <value>0xd09</value>
         <object_component_ref idref="oc-2f"/>
      </symbol>
      <symbol id="sm-8f">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-18"/>
      </symbol>
      <symbol id="sm-90">
         <name>NMI_Handler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-91">
         <name>HardFault_Handler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-92">
         <name>SVC_Handler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-93">
         <name>PendSV_Handler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-94">
         <name>GROUP0_IRQHandler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-95">
         <name>GROUP1_IRQHandler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-96">
         <name>TIMG8_IRQHandler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-97">
         <name>UART3_IRQHandler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-98">
         <name>CANFD0_IRQHandler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-99">
         <name>DAC0_IRQHandler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9a">
         <name>SPI0_IRQHandler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9b">
         <name>SPI1_IRQHandler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9c">
         <name>UART1_IRQHandler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9d">
         <name>UART2_IRQHandler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9e">
         <name>TIMG0_IRQHandler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-9f">
         <name>TIMG6_IRQHandler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a0">
         <name>TIMA0_IRQHandler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a1">
         <name>TIMA1_IRQHandler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a2">
         <name>TIMG7_IRQHandler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a3">
         <name>TIMG12_IRQHandler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a4">
         <name>I2C0_IRQHandler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a5">
         <name>I2C1_IRQHandler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a6">
         <name>AES_IRQHandler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a7">
         <name>RTC_IRQHandler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-a8">
         <name>DMA_IRQHandler</name>
         <value>0x487</value>
         <object_component_ref idref="oc-33"/>
      </symbol>
      <symbol id="sm-bb">
         <name>User_ADC_Init</name>
         <value>0x6a5</value>
         <object_component_ref idref="oc-ab"/>
      </symbol>
      <symbol id="sm-bc">
         <name>gADCSamples</name>
         <value>0x20200800</value>
      </symbol>
      <symbol id="sm-bd">
         <name>gADC1Samples</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-be">
         <name>ADC0_IRQHandler</name>
         <value>0xb75</value>
         <object_component_ref idref="oc-39"/>
      </symbol>
      <symbol id="sm-bf">
         <name>adc_done_count</name>
         <value>0x20201143</value>
         <object_component_ref idref="oc-6b"/>
      </symbol>
      <symbol id="sm-c0">
         <name>adc_flag</name>
         <value>0x20201144</value>
         <object_component_ref idref="oc-6c"/>
      </symbol>
      <symbol id="sm-c1">
         <name>ADC1_IRQHandler</name>
         <value>0xba5</value>
         <object_component_ref idref="oc-3e"/>
      </symbol>
      <symbol id="sm-c2">
         <name>adc1_flag</name>
         <value>0x20201142</value>
         <object_component_ref idref="oc-70"/>
      </symbol>
      <symbol id="sm-c3">
         <name>adc_proc</name>
         <value>0x76f</value>
         <object_component_ref idref="oc-121"/>
      </symbol>
      <symbol id="sm-d1">
         <name>key_read</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-13a"/>
      </symbol>
      <symbol id="sm-d2">
         <name>key_proc</name>
         <value>0xa55</value>
         <object_component_ref idref="oc-11b"/>
      </symbol>
      <symbol id="sm-d3">
         <name>key_val</name>
         <value>0x20201149</value>
         <object_component_ref idref="oc-13c"/>
      </symbol>
      <symbol id="sm-d4">
         <name>key_old</name>
         <value>0x20201147</value>
         <object_component_ref idref="oc-13d"/>
      </symbol>
      <symbol id="sm-d5">
         <name>key_down</name>
         <value>0x20201145</value>
         <object_component_ref idref="oc-13e"/>
      </symbol>
      <symbol id="sm-d6">
         <name>key_up</name>
         <value>0x20201148</value>
         <object_component_ref idref="oc-13f"/>
      </symbol>
      <symbol id="sm-d7">
         <name>key_early</name>
         <value>0x20201146</value>
         <object_component_ref idref="oc-140"/>
      </symbol>
      <symbol id="sm-e5">
         <name>UART0_IRQHandler</name>
         <value>0x771</value>
         <object_component_ref idref="oc-3f"/>
      </symbol>
      <symbol id="sm-e6">
         <name>rx_write_index</name>
         <value>0x20201140</value>
         <object_component_ref idref="oc-75"/>
      </symbol>
      <symbol id="sm-e7">
         <name>rx_read_index</name>
         <value>0x2020113e</value>
         <object_component_ref idref="oc-76"/>
      </symbol>
      <symbol id="sm-e8">
         <name>uart_rx_buffer</name>
         <value>0x202010e4</value>
      </symbol>
      <symbol id="sm-e9">
         <name>last_rx_time</name>
         <value>0x20201138</value>
         <object_component_ref idref="oc-78"/>
      </symbol>
      <symbol id="sm-ea">
         <name>uart_proc</name>
         <value>0x2ad</value>
         <object_component_ref idref="oc-120"/>
      </symbol>
      <symbol id="sm-eb">
         <name>packet_length</name>
         <value>0x2020113c</value>
         <object_component_ref idref="oc-143"/>
      </symbol>
      <symbol id="sm-ec">
         <name>packet_ready</name>
         <value>0x2020114a</value>
         <object_component_ref idref="oc-144"/>
      </symbol>
      <symbol id="sm-ed">
         <name>uart_data_buffer</name>
         <value>0x202010bc</value>
      </symbol>
      <symbol id="sm-100">
         <name>SysTick_Handler</name>
         <value>0xcb1</value>
         <object_component_ref idref="oc-34"/>
      </symbol>
      <symbol id="sm-101">
         <name>system_tick_ms</name>
         <value>0x2020110c</value>
      </symbol>
      <symbol id="sm-102">
         <name>scheduler_init</name>
         <value>0xce9</value>
         <object_component_ref idref="oc-ac"/>
      </symbol>
      <symbol id="sm-103">
         <name>task_num</name>
         <value>0x20201110</value>
      </symbol>
      <symbol id="sm-104">
         <name>scheduler_run</name>
         <value>0x951</value>
         <object_component_ref idref="oc-ad"/>
      </symbol>
      <symbol id="sm-105">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-106">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-107">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-108">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-109">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-10a">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-10b">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-10c">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-10d">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-118">
         <name>DL_ADC12_setClockConfig</name>
         <value>0x995</value>
         <object_component_ref idref="oc-10f"/>
      </symbol>
      <symbol id="sm-121">
         <name>DL_Common_delayCycles</name>
         <value>0xcf5</value>
         <object_component_ref idref="oc-f9"/>
      </symbol>
      <symbol id="sm-12b">
         <name>DL_DMA_initChannel</name>
         <value>0x871</value>
         <object_component_ref idref="oc-134"/>
      </symbol>
      <symbol id="sm-13b">
         <name>DL_Timer_setClockConfig</name>
         <value>0xc71</value>
         <object_component_ref idref="oc-fd"/>
      </symbol>
      <symbol id="sm-13c">
         <name>DL_Timer_initTimerMode</name>
         <value>0x1c5</value>
         <object_component_ref idref="oc-103"/>
      </symbol>
      <symbol id="sm-149">
         <name>DL_UART_init</name>
         <value>0x909</value>
         <object_component_ref idref="oc-10c"/>
      </symbol>
      <symbol id="sm-14a">
         <name>DL_UART_setClockConfig</name>
         <value>0xc8d</value>
         <object_component_ref idref="oc-106"/>
      </symbol>
      <symbol id="sm-155">
         <name>_c_int00_noargs</name>
         <value>0xc05</value>
         <object_component_ref idref="oc-60"/>
      </symbol>
      <symbol id="sm-156">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-1a"/>
      </symbol>
      <symbol id="sm-162">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0xa95</value>
         <object_component_ref idref="oc-b7"/>
      </symbol>
      <symbol id="sm-16a">
         <name>_system_pre_init</name>
         <value>0xd0d</value>
         <object_component_ref idref="oc-84"/>
      </symbol>
      <symbol id="sm-175">
         <name>__TI_zero_init</name>
         <value>0xcc1</value>
         <object_component_ref idref="oc-5d"/>
      </symbol>
      <symbol id="sm-17e">
         <name>__TI_decompress_none</name>
         <value>0xc9f</value>
         <object_component_ref idref="oc-20"/>
      </symbol>
      <symbol id="sm-189">
         <name>__TI_decompress_lzss</name>
         <value>0x629</value>
         <object_component_ref idref="oc-1c"/>
      </symbol>
      <symbol id="sm-193">
         <name>abort</name>
         <value>0xd11</value>
         <object_component_ref idref="oc-b0"/>
      </symbol>
      <symbol id="sm-194">
         <name>C$$EXIT</name>
         <value>0xd10</value>
         <object_component_ref idref="oc-b0"/>
      </symbol>
      <symbol id="sm-19c">
         <name>__aeabi_fdiv</name>
         <value>0x5a5</value>
         <object_component_ref idref="oc-e0"/>
      </symbol>
      <symbol id="sm-19d">
         <name>__divsf3</name>
         <value>0x5a5</value>
         <object_component_ref idref="oc-e0"/>
      </symbol>
      <symbol id="sm-1a3">
         <name>__aeabi_f2iz</name>
         <value>0xad1</value>
         <object_component_ref idref="oc-e4"/>
      </symbol>
      <symbol id="sm-1a4">
         <name>__fixsfsi</name>
         <value>0xad1</value>
         <object_component_ref idref="oc-e4"/>
      </symbol>
      <symbol id="sm-1aa">
         <name>__aeabi_memcpy</name>
         <value>0xd01</value>
         <object_component_ref idref="oc-50"/>
      </symbol>
      <symbol id="sm-1ab">
         <name>__aeabi_memcpy4</name>
         <value>0xd01</value>
         <object_component_ref idref="oc-50"/>
      </symbol>
      <symbol id="sm-1ac">
         <name>__aeabi_memcpy8</name>
         <value>0xd01</value>
         <object_component_ref idref="oc-50"/>
      </symbol>
      <symbol id="sm-1b3">
         <name>__aeabi_memclr</name>
         <value>0xcdd</value>
         <object_component_ref idref="oc-7f"/>
      </symbol>
      <symbol id="sm-1b4">
         <name>__aeabi_memclr4</name>
         <value>0xcdd</value>
         <object_component_ref idref="oc-7f"/>
      </symbol>
      <symbol id="sm-1b5">
         <name>__aeabi_memclr8</name>
         <value>0xcdd</value>
         <object_component_ref idref="oc-7f"/>
      </symbol>
      <symbol id="sm-1bb">
         <name>__aeabi_uidiv</name>
         <value>0xa15</value>
         <object_component_ref idref="oc-71"/>
      </symbol>
      <symbol id="sm-1bc">
         <name>__aeabi_uidivmod</name>
         <value>0xa15</value>
         <object_component_ref idref="oc-71"/>
      </symbol>
      <symbol id="sm-1c6">
         <name>__aeabi_idiv0</name>
         <value>0x627</value>
         <object_component_ref idref="oc-96"/>
      </symbol>
      <symbol id="sm-1df">
         <name>memcpy</name>
         <value>0x3ed</value>
         <object_component_ref idref="oc-a1"/>
      </symbol>
      <symbol id="sm-1ee">
         <name>memset</name>
         <value>0x70d</value>
         <object_component_ref idref="oc-d2"/>
      </symbol>
      <symbol id="sm-1ef">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1f2">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-1f3">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link successful</title>
</link_info>

################################################################################
# Automatically-generated file. Do not edit!
################################################################################

SHELL = cmd.exe

# Each subdirectory must supply rules for building sources it contributes
System/%.o: ../System/%.c $(GEN_OPTS) | $(GEN_FILES) $(GEN_MISC_FILES)
	@echo 'Building file: "$<"'
	@echo 'Invoking: Arm Compiler'
	"D:/ti/ccs/tools/compiler/ti-cgt-armllvm_4.0.0.LTS/bin/tiarmclang.exe" -c @"device.opt"  -march=thumbv6m -mcpu=cortex-m0plus -mfloat-abi=soft -mlittle-endian -mthumb -O0 -I"E:/study/ti/ti_9959" -I"E:/study/ti/ti_9959/EPD" -I"E:/study/ti/ti_9959/App" -I"E:/study/ti/ti_9959/User" -I"E:/study/ti/ti_9959/System" -I"E:/study/ti/ti_9959/BSP" -I"E:/study/ti/ti_9959/OLED" -I"E:/study/ti/ti_9959/Debug" -I"D:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/Core/Include" -I"D:/ti/mspm0_sdk_2_05_01_00/source" -I"D:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/DSP/lib/ticlang/m0p/arm_cortexM0l_math.a" -I"D:/ti/mspm0_sdk_2_05_01_00/source/third_party/CMSIS/DSP/Include" -gdwarf-3 -MMD -MP -MF"System/$(basename $(<F)).d_raw" -MT"$(@)"  $(GEN_OPTS__FLAG) -o"$@" "$<"
	@echo 'Finished building: "$<"'
	@echo ' '



#ifndef _AD9959_H_
#define _AD9959_H_

#include "User_Header.h"
// 官方驱动库
#include "ti_msp_dl_config.h"

extern uint8_t CSR_DATAall[1]; 	
extern uint8_t CFTW0_DATA[4];
extern	uint8_t ACR_DATA[3];

extern uint32_t SweepStepFre;
extern float SweepData[100];

//AD9959寄存器地址定义
#define CSR_ADD   0x00   //CSR 通道选择寄存器
#define FR1_ADD   0x01   //FR1 功能寄存器1
#define FR2_ADD   0x02   //FR2 功能寄存器2
#define CFR_ADD   0x03   //CFR 通道功能寄存器
#define CFTW0_ADD 0x04   //CTW0 通道频率转换字寄存器
#define CPOW0_ADD 0x05   //CPW0 通道相位转换字寄存器
#define ACR_ADD   0x06   //ACR 幅度控制寄存器
#define LSRR_ADD  0x07   //LSR 通道线性扫描寄存器
#define RDW_ADD   0x08   //RDW 通道线性向上扫描寄存器
#define FDW_ADD   0x09   //FDW 通道线性向下扫描寄存器
//AD9959管脚宏定义
#define CS_0			    (DL_GPIO_clearPins(GPIO_9959_CS_PORT, GPIO_9959_CS_PIN))
#define CS_1	            (DL_GPIO_setPins(GPIO_9959_CS_PORT, GPIO_9959_CS_PIN))

#define SCLK_0              (DL_GPIO_clearPins(GPIO_9959_SCLK_PORT, GPIO_9959_SCLK_PIN))
#define SCLK_1		        (DL_GPIO_setPins(GPIO_9959_SCLK_PORT, GPIO_9959_SCLK_PIN))

#define UPDATE_0            (DL_GPIO_clearPins(GPIO_9959_UPDATA_PORT, GPIO_9959_UPDATA_PIN))
#define UPDATE_1	        (DL_GPIO_setPins(GPIO_9959_UPDATA_PORT, GPIO_9959_UPDATA_PIN))

#define PS0_0               (DL_GPIO_clearPins(GPIO_9959_PS0_PORT, GPIO_9959_PS0_PIN))
#define PS0_1               (DL_GPIO_setPins(GPIO_9959_PS0_PORT, GPIO_9959_PS0_PIN))

#define PS1_0	            (DL_GPIO_clearPins(GPIO_9959_PS1_PORT, GPIO_9959_PS1_PIN))
#define PS1_1		        (DL_GPIO_setPins(GPIO_9959_PS1_PORT, GPIO_9959_PS1_PIN))

#define PS2_0               (DL_GPIO_clearPins(GPIO_9959_PS2_PORT, GPIO_9959_PS2_PIN))
#define PS2_1			    (DL_GPIO_setPins(GPIO_9959_PS2_PORT, GPIO_9959_PS2_PIN))

#define PS3_0		        (DL_GPIO_clearPins(GPIO_9959_PS3_PORT, GPIO_9959_PS3_PIN))
#define PS3_1		        (DL_GPIO_setPins(GPIO_9959_PS3_PORT, GPIO_9959_PS3_PIN))

#define SDIO0_0	            (DL_GPIO_clearPins(GPIO_9959_SDIO0_PORT, GPIO_9959_SDIO0_PIN))
#define SDIO0_1	            (DL_GPIO_setPins(GPIO_9959_SDIO0_PORT, GPIO_9959_SDIO0_PIN))

#define SDIO1_0             (DL_GPIO_clearPins(GPIO_9959_SDIO1_PORT, GPIO_9959_SDIO1_PIN))
#define SDIO1_1             (DL_GPIO_setPins(GPIO_9959_SDIO1_PORT, GPIO_9959_SDIO1_PIN))

#define SDIO2_0		        (DL_GPIO_clearPins(GPIO_9959_SDIO2_PORT, GPIO_9959_SDIO2_PIN))
#define SDIO2_1             (DL_GPIO_setPins(GPIO_9959_SDIO2_PORT, GPIO_9959_SDIO2_PIN))

#define SDIO3_0             (DL_GPIO_clearPins(GPIO_9959_SDIO3_PORT, GPIO_9959_SDIO3_PIN))
#define SDIO3_1		        (DL_GPIO_setPins(GPIO_9959_SDIO3_PORT, GPIO_9959_SDIO3_PIN))

#define AD9959_PWR_0        (DL_GPIO_clearPins(GPIO_9959_PWR_PORT, GPIO_9959_PWR_PIN))
#define AD9959_PWR_1	    (DL_GPIO_setPins(GPIO_9959_PWR_PORT, GPIO_9959_PWR_PIN))

#define Reset_0             (DL_GPIO_clearPins(GPIO_9959_RESET_PORT, GPIO_9959_RESET_PIN))
#define Reset_1		        (DL_GPIO_setPins(GPIO_9959_RESET_PORT, GPIO_9959_RESET_PIN))


void Init_AD9959(void)  ;
void delay1 (uint32_t length);
void Intserve(void)		   ;
void IntReset(void)	  ;
void IO_Update(void)  ;
void WriteData_AD9959(uint8_t RegisterAddress, uint8_t NumberofRegisters, uint8_t *RegisterData,uint8_t temp);
void Write_frequence(uint8_t Channel,uint32_t Freq);
void Write_frequence_no_update(uint8_t Channel,uint32_t Freq);
void Write_Amplitude(uint8_t Channel, uint16_t Ampli);
void Write_Amplitude_no_update(uint8_t Channel, uint16_t Ampli);
void Write_Phase(uint8_t Channel,uint16_t Phase);
void Write_Phase_no_update(uint8_t Channel,uint16_t Phase);
void SweepFre(uint32_t SweepMinFre,uint32_t SweepMaxFre,uint16_t SweepTime);

#endif



#ifndef __USER_HEADER_H_
#define __USER_HEADER_H_

// 官方驱动库
#include "ti_msp_dl_config.h"

// 系统库
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "arm_const_structs.h"
#include "arm_math.h"
#include "math.h"
#include "usart.h"
#include "Delay.h"

// 外设包含
#include "EPD_GUI.h"
#include "BSP_4x4KEY.h"
#include "BSP_I2C.h"
#include "BSP_ADS112C04.h"
#include "BSP_ADS112_REG.h"
#include "BSP_DAC7811.h"
#include "BSP_ADS7886.h"
#include "BSP_Spwm.h"

// 用户算法
#include "User_ADC.h"
#include "User_DAC.h"
#include "User_FFT.h"

// 自定义函数
static void TaskA_Handler( void );
static void TaskB_Handler( void );
static void TaskC_Handler( void );
static void TaskD_Handler( void );
void Show_menu(void);
void Init_All(void);

//外部变量引用


#endif /* __USER_HEADER_H_ */

******************************************************************************
            TI ARM Clang Linker PC v4.0.0                      
******************************************************************************
>> Linked Wed Jul 16 15:15:34 2025

OUTPUT FILE NAME:   <ti_9959.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00005255


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00008000  000064e8  00001b18  R  X
  SRAM                  20200000   00004000  00001347  00002cb9  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000064e8   000064e8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00005b20   00005b20    r-x .text
  00005be0    00005be0    000008d0   000008d0    r-- .rodata
  000064b0    000064b0    00000038   00000038    r-- .cinit
20200000    20200000    00001147   00000000    rw-
  20200000    20200000    00000ed4   00000000    rw- .bss
  20200ed4    20200ed4    00000273   00000000    rw- .data
20203e00    20203e00    00000200   00000000    rw-
  20203e00    20203e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00005b20     
                  000000c0    00000a48     libc.a : e_pow.c.obj (.text.pow)
                  00000b08    000009d0            : _printfi.c.obj (.text:__TI_printfi)
                  000014d8    00000384     BSP_4x4KEY.o (.text.Read4X4KEY)
                  0000185c    000002e0     libc.a : e_log10.c.obj (.text.log10)
                  00001b3c    00000220            : _printfi.c.obj (.text._pconv_a)
                  00001d5c    000001e0     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001f3c    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  00002118    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000022aa    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  000022ac    00000170     libc.a : e_sqrt.c.obj (.text.sqrt)
                  0000241c    00000168     AD9959.o (.text.WriteData_AD9959)
                  00002584    00000158     EPD_GUI.o (.text.EPD_ShowChar)
                  000026dc    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00002818    00000138     AD9959.o (.text.Write_Phase)
                  00002950    00000138     AD9959.o (.text.Write_Phase_no_update)
                  00002a88    00000138     AD9959.o (.text.Write_frequence_no_update)
                  00002bc0    0000012c     main.o (.text.TaskA_Handler)
                  00002cec    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00002e0c    0000011c     AD9959.o (.text.Write_Amplitude_no_update)
                  00002f28    00000118     AD9959.o (.text.Write_Amplitude)
                  00003040    0000010c     AD9959.o (.text.Write_frequence)
                  0000314c    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  00003258    00000104     driverlib.a : dl_timer.o (.text.DL_Timer_initFourCCPWMMode)
                  0000335c    000000fc     AD9959.o (.text.Init_AD9959)
                  00003458    000000f8     AD9959.o (.text.SweepFre)
                  00003550    000000f4     EPD_GUI.o (.text.Paint_SetPixel)
                  00003644    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  0000372c    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00003810    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  000038e8    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  0000398a    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  0000398c    0000009c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00003a28    00000094     EPD_GUI.o (.text.Paint_NewImage)
                  00003abc    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00003b4c    0000008c     AD9959.o (.text.Intserve)
                  00003bd8    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00003c64    00000088     driverlib.a : dl_dac12.o (.text.DL_DAC12_init)
                  00003cec    00000084     SPI_Init.o (.text.EPD_WR_Bus)
                  00003d70    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00003df2    00000002     --HOLE-- [fill = 0]
                  00003df4    0000007c     EPD_GUI.o (.text.EPD_ClearAll)
                  00003e70    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003eec    00000078     EPD.o (.text.EPD_Display)
                  00003f64    00000074     EPD.o (.text.EPD_Display_Clear)
                  00003fd8    00000074     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  0000404c    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00004050    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  000040c4    00000070     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00004134    00000070     main.o (.text.Show_menu)
                  000041a4    0000006c     EPD_GUI.o (.text.Paint_Clear)
                  00004210    00000068     EPD.o (.text.EPD_FastMode2Init)
                  00004278    00000068     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  000042e0    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  00004346    00000002     --HOLE-- [fill = 0]
                  00004348    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000043aa    0000005e     EPD_GUI.o (.text.EPD_ShowString)
                  00004408    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  00004464    00000058            : _ltoa.c.obj (.text.__TI_ltoa)
                  000044bc    00000058            : _printfi.c.obj (.text._pconv_f)
                  00004514    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  0000456a    00000002     --HOLE-- [fill = 0]
                  0000456c    00000054     main.o (.text.TIMA0_IRQHandler)
                  000045c0    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  00004612    00000002     --HOLE-- [fill = 0]
                  00004614    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00004660    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  000046ac    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  000046f8    0000004c     main.o (.text.Init_All)
                  00004744    0000004c     BSP_Spwm.o (.text.TIMG8_IRQHandler)
                  00004790    0000004c     User_ADC.o (.text.User_ADC_Init)
                  000047dc    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  00004826    00000002     --HOLE-- [fill = 0]
                  00004828    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  00004872    00000002     --HOLE-- [fill = 0]
                  00004874    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000048bc    00000048     User_ADC.o (.text.Set_Fs)
                  00004904    00000046     EPD_GUI.o (.text.EPD_ShowNum)
                  0000494a    00000002     --HOLE-- [fill = 0]
                  0000494c    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  00004990    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  000049d2    00000002     --HOLE-- [fill = 0]
                  000049d4    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00004a14    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00004a54    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00004a94    00000040     User_DAC.o (.text.User_DAC_Init)
                  00004ad4    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004b14    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00004b54    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00004b94    0000003c     ti_msp_dl_config.o (.text.DL_Timer_setCounterControl)
                  00004bd0    0000003c     EPD.o (.text.EPD_Clear_R26H)
                  00004c0c    0000003c     EPD.o (.text.EPD_HW_RESET)
                  00004c48    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00004c84    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00004cc0    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00004cfa    00000002     --HOLE-- [fill = 0]
                  00004cfc    00000038     ti_msp_dl_config.o (.text.DL_Timer_setPublisherChanID)
                  00004d34    00000038     AD9959.o (.text.IO_Update)
                  00004d6c    00000038     AD9959.o (.text.IntReset)
                  00004da4    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00004ddc    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00004e14    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00004e48    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00004e7c    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_9959_init)
                  00004eb0    00000032     libclang_rt.builtins.a : fixunssfsi.S.obj (.text.__fixunssfsi)
                  00004ee2    00000002     --HOLE-- [fill = 0]
                  00004ee4    00000030     User_ADC.o (.text.DL_DMA_setTransferSize)
                  00004f14    00000030     User_DAC.o (.text.DL_DMA_setTransferSize)
                  00004f44    00000030     ti_msp_dl_config.o (.text.DL_DMA_setTransferSize)
                  00004f74    00000030     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutputFeatures)
                  00004fa4    00000030     SPI_Init.o (.text.EPD_WR_DATA8)
                  00004fd4    00000030     SPI_Init.o (.text.EPD_WR_REG)
                  00005004    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00005034    0000002c     User_ADC.o (.text.ADC0_IRQHandler)
                  00005060    0000002c     ti_msp_dl_config.o (.text.DL_ADC12_setDMASamplesCnt)
                  0000508c    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_DAC12_init)
                  000050b8    0000002c     User_ADC.o (.text.__NVIC_EnableIRQ)
                  000050e4    0000002c     main.o (.text.__NVIC_EnableIRQ)
                  00005110    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  0000513c    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00005164    00000028     User_ADC.o (.text.DL_DMA_setDestAddr)
                  0000518c    00000028     User_DAC.o (.text.DL_DMA_setDestAddr)
                  000051b4    00000028     User_ADC.o (.text.DL_DMA_setSrcAddr)
                  000051dc    00000028     User_DAC.o (.text.DL_DMA_setSrcAddr)
                  00005204    00000028     ti_msp_dl_config.o (.text.DL_Timer_enableEvent)
                  0000522c    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH0_init)
                  00005254    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  0000527c    00000026     User_ADC.o (.text.DL_DMA_enableChannel)
                  000052a2    00000026     User_DAC.o (.text.DL_DMA_enableChannel)
                  000052c8    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  000052ec    00000024                            : muldi3.S.obj (.text.__muldi3)
                  00005310    00000022     Delay.o (.text.delay_ms)
                  00005332    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00005354    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00005374    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setMFPCLKSource)
                  00005394    00000020     AD9959.o (.text.delay1)
                  000053b4    00000020     main.o (.text.main)
                  000053d4    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  000053f2    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  00005410    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  0000542e    00000002     --HOLE-- [fill = 0]
                  00005430    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_clearInterruptStatus)
                  0000544c    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableDMA)
                  00005468    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableDMATrigger)
                  00005484    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableInterrupt)
                  000054a0    0000001c     ti_msp_dl_config.o (.text.DL_DAC12_enableInterrupt)
                  000054bc    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000054d8    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000054f4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  00005510    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  0000552c    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setMCLKDivider)
                  00005548    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00005564    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  00005580    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  0000559c    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  000055b8    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000055d4    0000001c     EPD.o (.text.EPD_READBUSY)
                  000055f0    0000001a     ti_msp_dl_config.o (.text.DL_ADC12_setSubscriberChanID)
                  0000560a    0000001a     EPD.o (.text.EPD_FastUpdate)
                  00005624    0000001a     EPD.o (.text.EPD_PartUpdate)
                  0000563e    00000002     --HOLE-- [fill = 0]
                  00005640    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00005658    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  00005670    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00005688    00000018     ti_msp_dl_config.o (.text.DL_DAC12_enablePower)
                  000056a0    00000018     ti_msp_dl_config.o (.text.DL_DAC12_reset)
                  000056b8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000056d0    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000056e8    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  00005700    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  00005718    00000018     AD9959.o (.text.DL_GPIO_setPins)
                  00005730    00000018     BSP_4x4KEY.o (.text.DL_GPIO_setPins)
                  00005748    00000018     EPD.o (.text.DL_GPIO_setPins)
                  00005760    00000018     SPI_Init.o (.text.DL_GPIO_setPins)
                  00005778    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  00005790    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  000057a8    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  000057c0    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000057d8    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000057f0    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  00005808    00000018     User_ADC.o (.text.DL_Timer_setLoadValue)
                  00005820    00000018     main.o (.text.DL_Timer_setLoadValue)
                  00005838    00000018     main.o (.text.DL_Timer_startCounter)
                  00005850    00000018     User_ADC.o (.text.DL_Timer_stopCounter)
                  00005868    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  00005880    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00005898    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH1_init)
                  000058b0    00000018     libc.a : sprintf.c.obj (.text._outs)
                  000058c8    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000058de    00000016     ti_msp_dl_config.o (.text.DL_DAC12_enable)
                  000058f4    00000016     BSP_4x4KEY.o (.text.DL_GPIO_readPins)
                  0000590a    00000016     EPD.o (.text.DL_GPIO_readPins)
                  00005920    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00005936    00000014     AD9959.o (.text.DL_GPIO_clearPins)
                  0000594a    00000014     BSP_4x4KEY.o (.text.DL_GPIO_clearPins)
                  0000595e    00000014     EPD.o (.text.DL_GPIO_clearPins)
                  00005972    00000014     SPI_Init.o (.text.DL_GPIO_clearPins)
                  00005986    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  0000599a    00000002     --HOLE-- [fill = 0]
                  0000599c    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  000059b0    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000059c4    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000059d8    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000059ec    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  00005a00    00000012     User_ADC.o (.text.DL_ADC12_getPendingInterrupt)
                  00005a12    00000012     BSP_Spwm.o (.text.DL_Timer_getPendingInterrupt)
                  00005a24    00000012     main.o (.text.DL_Timer_getPendingInterrupt)
                  00005a36    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00005a48    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00005a5a    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00005a6c    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00005a7c    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_enableMFPCLK)
                  00005a8c    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00005a9c    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00005aac    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  00005abc    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00005aca    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00005ad8    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00005ae6    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00005af2    00000002     --HOLE-- [fill = 0]
                  00005af4    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00005b00    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00005b0a    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005b14    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00005b24    0000000a     libc.a : e_log10.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005b2e    00000002     --HOLE-- [fill = 0]
                  00005b30    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00005b40    0000000a     libc.a : e_pow.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005b4a    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005b54    0000000a            : e_pow.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005b5e    0000000a            : e_pow.c.obj (.text.OUTLINED_FUNCTION_6)
                  00005b68    0000000a            : sprintf.c.obj (.text._outc)
                  00005b72    00000008            : e_pow.c.obj (.text.OUTLINED_FUNCTION_2)
                  00005b7a    00000002     --HOLE-- [fill = 0]
                  00005b7c    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00005b84    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00005b8c    00000006     libc.a : e_log10.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005b92    00000006            : e_pow.c.obj (.text.OUTLINED_FUNCTION_3)
                  00005b98    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00005ba8    00000006     libc.a : e_pow.c.obj (.text.OUTLINED_FUNCTION_4)
                  00005bae    00000006            : e_pow.c.obj (.text.OUTLINED_FUNCTION_5)
                  00005bb4    00000004            : e_pow.c.obj (.text.OUTLINED_FUNCTION_10)
                  00005bb8    00000004            : e_pow.c.obj (.text.OUTLINED_FUNCTION_7)
                  00005bbc    00000004            : e_pow.c.obj (.text.OUTLINED_FUNCTION_8)
                  00005bc0    00000004            : e_pow.c.obj (.text.OUTLINED_FUNCTION_9)
                  00005bc4    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00005bc8    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00005bd8    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00005bdc    00000004            : exit.c.obj (.text:abort)

.cinit     0    000064b0    00000038     
                  000064b0    00000014     (.cinit..data.load) [load image, compression = lzss]
                  000064c4    0000000c     (__TI_handler_table)
                  000064d0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000064d8    00000010     (__TI_cinit_table)

.rodata    0    00005be0    000008d0     
                  00005be0    000005f0     EPD_GUI.o (.rodata.asc2_1608)
                  000061d0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  000062d1    00000001     --HOLE-- [fill = 0]
                  000062d2    00000080     User_DAC.o (.rodata.DAC_Buff)
                  00006352    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00006355    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00006358    00000030     libc.a : e_pow.c.obj (.rodata.cst16)
                  00006388    00000020     ti_msp_dl_config.o (.rodata.gDAC12Config)
                  000063a8    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH0Config)
                  000063c0    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH1Config)
                  000063d8    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  000063ec    00000014     ti_msp_dl_config.o (.rodata.gTIMER_9959TimerConfig)
                  00006400    00000014     EPD_GUI.o (.rodata.str1.83522250893381077531)
                  00006414    00000011     libc.a : _printfi.c.obj (.rodata.str1.103488685894817597201)
                  00006425    00000011            : _printfi.c.obj (.rodata.str1.153638888446227384661)
                  00006436    00000011     main.o (.rodata.str1.166377993610222413121)
                  00006447    00000011     main.o (.rodata.str1.172346908375677699691)
                  00006458    00000011     main.o (.rodata.str1.177815861202155679441)
                  00006469    00000011     main.o (.rodata.str1.21969950876111301151)
                  0000647a    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00006484    0000000a     main.o (.rodata.str1.16418588846768155021)
                  0000648e    00000009     main.o (.rodata.str1.79786358655082701271)
                  00006497    00000001     --HOLE-- [fill = 0]
                  00006498    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  000064a0    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  000064a8    00000003     ti_msp_dl_config.o (.rodata.gTIMER_9959ClockConfig)
                  000064ab    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  000064ad    00000003     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000ed4     UNINITIALIZED
                  20200000    00000b48     (.common:ImageBW)
                  20200b48    00000200     (.common:gADCSamples)
                  20200d48    000000bc     (.common:gTIMER_0Backup)
                  20200e04    000000bc     (.common:gTIMER_9959Backup)
                  20200ec0    00000014     (.common:Paint)

.data      0    20200ed4    00000273     UNINITIALIZED
                  20200ed4    00000190     AD9959.o (.data.SweepData)
                  20201064    000000c8     BSP_Spwm.o (.data.Pwm_Data)
                  2020112c    00000004     AD9959.o (.data.CFTW0_DATA)
                  20201130    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20201134    00000004     main.o (.data.count)
                  20201138    00000003     AD9959.o (.data.ACR_DATA)
                  2020113b    00000002     AD9959.o (.data.CPOW0_DATA0)
                  2020113d    00000001     AD9959.o (.data.CSR_DATA0)
                  2020113e    00000001     AD9959.o (.data.CSR_DATA1)
                  2020113f    00000001     AD9959.o (.data.CSR_DATA2)
                  20201140    00000001     AD9959.o (.data.CSR_DATA3)
                  20201141    00000001     AD9959.o (.data.CSR_DATAall)
                  20201142    00000001     BSP_Spwm.o (.data.Spwm_cnt)
                  20201143    00000001     User_ADC.o (.data.adc_flag)
                  20201144    00000001     main.o (.data.key_val)
                  20201145    00000001     main.o (.data.sweep_flag)
                  20201146    00000001     main.o (.data.t)

.stack     0    20203e00    00000200     UNINITIALIZED
                  20203e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20203e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             3002    157       376    
    +--+------------------------------+-------+---------+---------+
       Total:                         3002    157       376    
                                                               
    .\App\
       User_ADC.o                     468     0         513    
       User_DAC.o                     230     128       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         698     128       513    
                                                               
    .\BSP\
       AD9959.o                       2956    0         414    
       BSP_4x4KEY.o                   966     0         0      
       BSP_Spwm.o                     94      0         201    
    +--+------------------------------+-------+---------+---------+
       Total:                         4016    0         615    
                                                               
    .\EPD\
       EPD_GUI.o                      1132    1540      20     
       EPD.o                          606     0         0      
       SPI_Init.o                     272     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2010    1540      20     
                                                               
    .\System\
       Delay.o                        34      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         34      0         0      
                                                               
    .\User\
       main.o                         714     87        2895   
       startup_mspm0g350x_ticlang.o   8       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         722     279       2895   
                                                               
    D:/ti/mspm0_sdk_2_05_01_00/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     588     0         0      
       dl_dac12.o                     136     0         0      
       dl_uart.o                      90      0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         964     0         0      
                                                               
    D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4510    34        0      
       e_pow.c.obj                    2704    48        0      
       e_log10.c.obj                  752     0         0      
       e_sqrt.c.obj                   368     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     124     0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         9256    339       4      
                                                               
    D:\ti\ccs\tools\compiler\ti-cgt-armllvm_4.0.0.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       fixunssfsi.S.obj               50      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2594    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       56        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   23296   2499      4935   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000064d8 records: 2, size/record: 8, table size: 16
	.data: load addr=000064b0, load size=00000014 bytes, run addr=20200ed4, run size=00000273 bytes, compression=lzss
	.bss: load addr=000064d0, load size=00000008 bytes, run addr=20200000, run size=00000ed4 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000064c4 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   00002119     00005b14     00005b12   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                             00005b78          : e_pow.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   0000372d     00005b30     00005b2c   libc.a : e_log10.c.obj (.text.OUTLINED_FUNCTION_0)
                             00005b48          : e_pow.c.obj (.text.OUTLINED_FUNCTION_0)
                             00005b5c          : e_pow.c.obj (.text.OUTLINED_FUNCTION_1)
                             00005b66          : e_pow.c.obj (.text.OUTLINED_FUNCTION_6)
                             00005b90          : e_log10.c.obj (.text.OUTLINED_FUNCTION_1)
                             00005bac          : e_pow.c.obj (.text.OUTLINED_FUNCTION_4)
                             00005bb2          : e_pow.c.obj (.text.OUTLINED_FUNCTION_5)
                             00005bbe          : e_pow.c.obj (.text.OUTLINED_FUNCTION_8)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00002123     00005b98     00005b96   libc.a : e_pow.c.obj (.text.OUTLINED_FUNCTION_3)
                             00005bb6          : e_pow.c.obj (.text.OUTLINED_FUNCTION_10)
                             00005bba          : e_pow.c.obj (.text.OUTLINED_FUNCTION_7)
                             00005bc2          : e_pow.c.obj (.text.OUTLINED_FUNCTION_9)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00005255     00005bc8     00005bc4   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[4 trampolines]
[15 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
20201138  ACR_DATA                        
00005035  ADC0_IRQHandler                 
0000404d  ADC1_IRQHandler                 
0000404d  AES_IRQHandler                  
00005bdc  C$$EXIT                         
0000404d  CANFD0_IRQHandler               
2020112c  CFTW0_DATA                      
2020113b  CPOW0_DATA0                     
2020113d  CSR_DATA0                       
2020113e  CSR_DATA1                       
2020113f  CSR_DATA2                       
20201140  CSR_DATA3                       
20201141  CSR_DATAall                     
0000404d  DAC0_IRQHandler                 
000062d2  DAC_Buff                        
000049d5  DL_ADC12_setClockConfig         
00005b01  DL_Common_delayCycles           
00003c65  DL_DAC12_init                   
00004661  DL_DMA_initChannel              
00003259  DL_Timer_initFourCCPWMMode      
00003645  DL_Timer_initTimerMode          
0000559d  DL_Timer_setCaptCompUpdateMethod
000057f1  DL_Timer_setCaptureCompareOutCtl
00005a8d  DL_Timer_setCaptureCompareValue 
000055b9  DL_Timer_setClockConfig         
00004875  DL_UART_init                    
00005a37  DL_UART_setClockConfig          
0000404d  DMA_IRQHandler                  
0000404d  Default_Handler                 
00003df5  EPD_ClearAll                    
00004bd1  EPD_Clear_R26H                  
00003eed  EPD_Display                     
00003f65  EPD_Display_Clear               
00004211  EPD_FastMode2Init               
0000560b  EPD_FastUpdate                  
00004c0d  EPD_HW_RESET                    
00005625  EPD_PartUpdate                  
000055d5  EPD_READBUSY                    
00002585  EPD_ShowChar                    
00004905  EPD_ShowNum                     
000043ab  EPD_ShowString                  
00003ced  EPD_WR_Bus                      
00004fa5  EPD_WR_DATA8                    
00004fd5  EPD_WR_REG                      
0000404d  GROUP0_IRQHandler               
0000404d  GROUP1_IRQHandler               
0000404d  HardFault_Handler               
0000404d  I2C0_IRQHandler                 
0000404d  I2C1_IRQHandler                 
00004d35  IO_Update                       
20200000  ImageBW                         
0000335d  Init_AD9959                     
000046f9  Init_All                        
00004d6d  IntReset                        
00003b4d  Intserve                        
0000404d  NMI_Handler                     
20200ec0  Paint                           
000041a5  Paint_Clear                     
00003a29  Paint_NewImage                  
00003551  Paint_SetPixel                  
0000404d  PendSV_Handler                  
20201064  Pwm_Data                        
0000404d  RTC_IRQHandler                  
000014d9  Read4X4KEY                      
00005bc5  Reset_Handler                   
0000404d  SPI0_IRQHandler                 
0000404d  SPI1_IRQHandler                 
0000404d  SVC_Handler                     
00003abd  SYSCFG_DL_ADC12_0_init          
0000508d  SYSCFG_DL_DAC12_init            
0000522d  SYSCFG_DL_DMA_CH0_init          
00005899  SYSCFG_DL_DMA_CH1_init          
00005ae7  SYSCFG_DL_DMA_init              
00001d5d  SYSCFG_DL_GPIO_init             
000040c5  SYSCFG_DL_PWM_0_init            
00004e49  SYSCFG_DL_SYSCTL_init           
0000494d  SYSCFG_DL_TIMER_0_init          
00004e7d  SYSCFG_DL_TIMER_9959_init       
00004a15  SYSCFG_DL_UART_0_init           
00004a55  SYSCFG_DL_init                  
0000398d  SYSCFG_DL_initPower             
000048bd  Set_Fs                          
00004135  Show_menu                       
20201142  Spwm_cnt                        
20200ed4  SweepData                       
00003459  SweepFre                        
0000404d  SysTick_Handler                 
0000456d  TIMA0_IRQHandler                
0000404d  TIMA1_IRQHandler                
0000404d  TIMG0_IRQHandler                
0000404d  TIMG12_IRQHandler               
0000404d  TIMG6_IRQHandler                
0000404d  TIMG7_IRQHandler                
00004745  TIMG8_IRQHandler                
00005a49  TI_memcpy_small                 
00005ad9  TI_memset_small                 
0000404d  UART0_IRQHandler                
0000404d  UART1_IRQHandler                
0000404d  UART2_IRQHandler                
0000404d  UART3_IRQHandler                
00004791  User_ADC_Init                   
00004a95  User_DAC_Init                   
0000241d  WriteData_AD9959                
00002f29  Write_Amplitude                 
00002e0d  Write_Amplitude_no_update       
00002819  Write_Phase                     
00002951  Write_Phase_no_update           
00003041  Write_frequence                 
00002a89  Write_frequence_no_update       
20204000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
000064d8  __TI_CINIT_Base                 
000064e8  __TI_CINIT_Limit                
000064e8  __TI_CINIT_Warm                 
000064c4  __TI_Handler_Table_Base         
000064d0  __TI_Handler_Table_Limit        
00004c85  __TI_auto_init_nobinit_nopinit  
00003e71  __TI_decompress_lzss            
00005a5b  __TI_decompress_none            
00004465  __TI_ltoa                       
ffffffff  __TI_pprof_out_hndl             
00000b09  __TI_printfi                    
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00005aad  __TI_zero_init                  
00002123  __adddf3                        
000061d0  __aeabi_ctype_table_            
000061d0  __aeabi_ctype_table_C           
00004051  __aeabi_d2f                     
00004829  __aeabi_d2iz                    
00004991  __aeabi_d2uiz                   
00002123  __aeabi_dadd                    
00004349  __aeabi_dcmpeq                  
00004385  __aeabi_dcmpge                  
00004399  __aeabi_dcmpgt                  
00004371  __aeabi_dcmple                  
0000435d  __aeabi_dcmplt                  
0000314d  __aeabi_ddiv                    
0000372d  __aeabi_dmul                    
00002119  __aeabi_dsub                    
20201130  __aeabi_errno                   
00005b7d  __aeabi_errno_addr              
00004b15  __aeabi_f2d                     
00004da5  __aeabi_f2iz                    
00004eb1  __aeabi_f2uiz                   
00003d71  __aeabi_fdiv                    
00003bd9  __aeabi_fmul                    
00005111  __aeabi_i2d                     
00004c49  __aeabi_i2f                     
00004515  __aeabi_idiv                    
000022ab  __aeabi_idiv0                   
00004515  __aeabi_idivmod                 
0000398b  __aeabi_ldiv0                   
00005411  __aeabi_llsl                    
000052ed  __aeabi_lmul                    
00005af5  __aeabi_memclr                  
00005af5  __aeabi_memclr4                 
00005af5  __aeabi_memclr8                 
00005b85  __aeabi_memcpy                  
00005b85  __aeabi_memcpy4                 
00005b85  __aeabi_memcpy8                 
00005abd  __aeabi_memset                  
00005abd  __aeabi_memset4                 
00005abd  __aeabi_memset8                 
000052c9  __aeabi_ui2d                    
00004ad5  __aeabi_uidiv                   
00004ad5  __aeabi_uidivmod                
000059d9  __aeabi_uldivmod                
00005411  __ashldi3                       
ffffffff  __binit__                       
00004279  __cmpdf2                        
0000314d  __divdf3                        
00003d71  __divsf3                        
00004279  __eqdf2                         
00004b15  __extendsfdf2                   
00004829  __fixdfsi                       
00004da5  __fixsfsi                       
00004991  __fixunsdfsi                    
00004eb1  __fixunssfsi                    
00005111  __floatsidf                     
00004c49  __floatsisf                     
000052c9  __floatunsidf                   
00003fd9  __gedf2                         
00003fd9  __gtdf2                         
00004279  __ledf2                         
00004279  __ltdf2                         
UNDEFED   __mpu_init                      
0000372d  __muldf3                        
000052ed  __muldi3                        
00004cc1  __muldsi3                       
00003bd9  __mulsf3                        
00004279  __nedf2                         
20203e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
00002119  __subdf3                        
00004051  __truncdfsf2                    
000038e9  __udivmoddi4                    
00005255  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
00005bd9  _system_pre_init                
00005bdd  abort                           
20201143  adc_flag                        
00005be0  asc2_1608                       
00004b55  atoi                            
ffffffff  binit                           
20201134  count                           
00005395  delay1                          
00005311  delay_ms                        
00004409  frexp                           
00004409  frexpl                          
20200b48  gADCSamples                     
20200d48  gTIMER_0Backup                  
20200e04  gTIMER_9959Backup               
00000000  interruptVectors                
20201144  key_val                         
00003811  ldexp                           
00003811  ldexpl                          
0000185d  log10                           
0000185d  log10l                          
000053b5  main                            
00005333  memccpy                         
000000c1  pow                             
000000c1  powl                            
00003811  scalbn                          
00003811  scalbnl                         
00004ddd  sprintf                         
000022ad  sqrt                            
000022ad  sqrtl                           
20201145  sweep_flag                      
20201146  t                               
00005a9d  wcslen                          


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  pow                             
000000c1  powl                            
00000200  __STACK_SIZE                    
00000b09  __TI_printfi                    
000014d9  Read4X4KEY                      
0000185d  log10                           
0000185d  log10l                          
00001d5d  SYSCFG_DL_GPIO_init             
00002119  __aeabi_dsub                    
00002119  __subdf3                        
00002123  __adddf3                        
00002123  __aeabi_dadd                    
000022ab  __aeabi_idiv0                   
000022ad  sqrt                            
000022ad  sqrtl                           
0000241d  WriteData_AD9959                
00002585  EPD_ShowChar                    
00002819  Write_Phase                     
00002951  Write_Phase_no_update           
00002a89  Write_frequence_no_update       
00002e0d  Write_Amplitude_no_update       
00002f29  Write_Amplitude                 
00003041  Write_frequence                 
0000314d  __aeabi_ddiv                    
0000314d  __divdf3                        
00003259  DL_Timer_initFourCCPWMMode      
0000335d  Init_AD9959                     
00003459  SweepFre                        
00003551  Paint_SetPixel                  
00003645  DL_Timer_initTimerMode          
0000372d  __aeabi_dmul                    
0000372d  __muldf3                        
00003811  ldexp                           
00003811  ldexpl                          
00003811  scalbn                          
00003811  scalbnl                         
000038e9  __udivmoddi4                    
0000398b  __aeabi_ldiv0                   
0000398d  SYSCFG_DL_initPower             
00003a29  Paint_NewImage                  
00003abd  SYSCFG_DL_ADC12_0_init          
00003b4d  Intserve                        
00003bd9  __aeabi_fmul                    
00003bd9  __mulsf3                        
00003c65  DL_DAC12_init                   
00003ced  EPD_WR_Bus                      
00003d71  __aeabi_fdiv                    
00003d71  __divsf3                        
00003df5  EPD_ClearAll                    
00003e71  __TI_decompress_lzss            
00003eed  EPD_Display                     
00003f65  EPD_Display_Clear               
00003fd9  __gedf2                         
00003fd9  __gtdf2                         
0000404d  ADC1_IRQHandler                 
0000404d  AES_IRQHandler                  
0000404d  CANFD0_IRQHandler               
0000404d  DAC0_IRQHandler                 
0000404d  DMA_IRQHandler                  
0000404d  Default_Handler                 
0000404d  GROUP0_IRQHandler               
0000404d  GROUP1_IRQHandler               
0000404d  HardFault_Handler               
0000404d  I2C0_IRQHandler                 
0000404d  I2C1_IRQHandler                 
0000404d  NMI_Handler                     
0000404d  PendSV_Handler                  
0000404d  RTC_IRQHandler                  
0000404d  SPI0_IRQHandler                 
0000404d  SPI1_IRQHandler                 
0000404d  SVC_Handler                     
0000404d  SysTick_Handler                 
0000404d  TIMA1_IRQHandler                
0000404d  TIMG0_IRQHandler                
0000404d  TIMG12_IRQHandler               
0000404d  TIMG6_IRQHandler                
0000404d  TIMG7_IRQHandler                
0000404d  UART0_IRQHandler                
0000404d  UART1_IRQHandler                
0000404d  UART2_IRQHandler                
0000404d  UART3_IRQHandler                
00004051  __aeabi_d2f                     
00004051  __truncdfsf2                    
000040c5  SYSCFG_DL_PWM_0_init            
00004135  Show_menu                       
000041a5  Paint_Clear                     
00004211  EPD_FastMode2Init               
00004279  __cmpdf2                        
00004279  __eqdf2                         
00004279  __ledf2                         
00004279  __ltdf2                         
00004279  __nedf2                         
00004349  __aeabi_dcmpeq                  
0000435d  __aeabi_dcmplt                  
00004371  __aeabi_dcmple                  
00004385  __aeabi_dcmpge                  
00004399  __aeabi_dcmpgt                  
000043ab  EPD_ShowString                  
00004409  frexp                           
00004409  frexpl                          
00004465  __TI_ltoa                       
00004515  __aeabi_idiv                    
00004515  __aeabi_idivmod                 
0000456d  TIMA0_IRQHandler                
00004661  DL_DMA_initChannel              
000046f9  Init_All                        
00004745  TIMG8_IRQHandler                
00004791  User_ADC_Init                   
00004829  __aeabi_d2iz                    
00004829  __fixdfsi                       
00004875  DL_UART_init                    
000048bd  Set_Fs                          
00004905  EPD_ShowNum                     
0000494d  SYSCFG_DL_TIMER_0_init          
00004991  __aeabi_d2uiz                   
00004991  __fixunsdfsi                    
000049d5  DL_ADC12_setClockConfig         
00004a15  SYSCFG_DL_UART_0_init           
00004a55  SYSCFG_DL_init                  
00004a95  User_DAC_Init                   
00004ad5  __aeabi_uidiv                   
00004ad5  __aeabi_uidivmod                
00004b15  __aeabi_f2d                     
00004b15  __extendsfdf2                   
00004b55  atoi                            
00004bd1  EPD_Clear_R26H                  
00004c0d  EPD_HW_RESET                    
00004c49  __aeabi_i2f                     
00004c49  __floatsisf                     
00004c85  __TI_auto_init_nobinit_nopinit  
00004cc1  __muldsi3                       
00004d35  IO_Update                       
00004d6d  IntReset                        
00004da5  __aeabi_f2iz                    
00004da5  __fixsfsi                       
00004ddd  sprintf                         
00004e49  SYSCFG_DL_SYSCTL_init           
00004e7d  SYSCFG_DL_TIMER_9959_init       
00004eb1  __aeabi_f2uiz                   
00004eb1  __fixunssfsi                    
00004fa5  EPD_WR_DATA8                    
00004fd5  EPD_WR_REG                      
00005035  ADC0_IRQHandler                 
0000508d  SYSCFG_DL_DAC12_init            
00005111  __aeabi_i2d                     
00005111  __floatsidf                     
0000522d  SYSCFG_DL_DMA_CH0_init          
00005255  _c_int00_noargs                 
000052c9  __aeabi_ui2d                    
000052c9  __floatunsidf                   
000052ed  __aeabi_lmul                    
000052ed  __muldi3                        
00005311  delay_ms                        
00005333  memccpy                         
00005395  delay1                          
000053b5  main                            
00005411  __aeabi_llsl                    
00005411  __ashldi3                       
0000559d  DL_Timer_setCaptCompUpdateMethod
000055b9  DL_Timer_setClockConfig         
000055d5  EPD_READBUSY                    
0000560b  EPD_FastUpdate                  
00005625  EPD_PartUpdate                  
000057f1  DL_Timer_setCaptureCompareOutCtl
00005899  SYSCFG_DL_DMA_CH1_init          
000059d9  __aeabi_uldivmod                
00005a37  DL_UART_setClockConfig          
00005a49  TI_memcpy_small                 
00005a5b  __TI_decompress_none            
00005a8d  DL_Timer_setCaptureCompareValue 
00005a9d  wcslen                          
00005aad  __TI_zero_init                  
00005abd  __aeabi_memset                  
00005abd  __aeabi_memset4                 
00005abd  __aeabi_memset8                 
00005ad9  TI_memset_small                 
00005ae7  SYSCFG_DL_DMA_init              
00005af5  __aeabi_memclr                  
00005af5  __aeabi_memclr4                 
00005af5  __aeabi_memclr8                 
00005b01  DL_Common_delayCycles           
00005b7d  __aeabi_errno_addr              
00005b85  __aeabi_memcpy                  
00005b85  __aeabi_memcpy4                 
00005b85  __aeabi_memcpy8                 
00005bc5  Reset_Handler                   
00005bd9  _system_pre_init                
00005bdc  C$$EXIT                         
00005bdd  abort                           
00005be0  asc2_1608                       
000061d0  __aeabi_ctype_table_            
000061d0  __aeabi_ctype_table_C           
000062d2  DAC_Buff                        
000064c4  __TI_Handler_Table_Base         
000064d0  __TI_Handler_Table_Limit        
000064d8  __TI_CINIT_Base                 
000064e8  __TI_CINIT_Limit                
000064e8  __TI_CINIT_Warm                 
20200000  ImageBW                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200b48  gADCSamples                     
20200d48  gTIMER_0Backup                  
20200e04  gTIMER_9959Backup               
20200ec0  Paint                           
20200ed4  SweepData                       
20201064  Pwm_Data                        
2020112c  CFTW0_DATA                      
20201130  __aeabi_errno                   
20201134  count                           
20201138  ACR_DATA                        
2020113b  CPOW0_DATA0                     
2020113d  CSR_DATA0                       
2020113e  CSR_DATA1                       
2020113f  CSR_DATA2                       
20201140  CSR_DATA3                       
20201141  CSR_DATAall                     
20201142  Spwm_cnt                        
20201143  adc_flag                        
20201144  key_val                         
20201145  sweep_flag                      
20201146  t                               
20203e00  __stack                         
20204000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[244 symbols]

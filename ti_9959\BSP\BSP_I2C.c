/*
 * BSP_I2C.c
 *
 * 软件I2C
 * 注：软件I2C速率很低，不需要加延时
 *  Created on: 2024年8月19日
 *      Author: <PERSON><PERSON><PERSON> Huang
 */
#include "BSP_I2C.h"

void I2C_W_SCL(uint8_t BitValue)
{
    if(BitValue){SCL_Set();}
    else{SCL_Clr();}

	//delay_us(1);
}

void I2C_W_SDA(uint8_t BitValue)
{
    SDA_OUT();

    if(BitValue){SDA_Set();}
    else{SDA_Clr();}

	//delay_us(1);
}

uint8_t I2C_R_SDA(void)
{
    SDA_IN();

	uint8_t BitValue;
    BitValue = DL_GPIO_readPins(MYI2C_PORT,MYI2C_SDA_PIN) == 0 ? 0 : 1;
	//delay_us(1);
	return BitValue;
}

void I2C_Start(void)
{
    SDA_OUT();
	I2C_W_SDA(1);
	I2C_W_SCL(1);
	I2C_W_SDA(0);
	I2C_W_SCL(0);
}

void I2C_Stop(void)
{
    SDA_OUT();
	I2C_W_SDA(0);
	I2C_W_SCL(1);
	I2C_W_SDA(1);
}

void I2C_SendByte(uint8_t Byte)
{
    SDA_OUT();
	uint8_t i;
	for (i = 0; i < 8; i ++)
	{
		I2C_W_SDA(Byte & (0x80 >> i));
		I2C_W_SCL(1);
		I2C_W_SCL(0);
	}
}

uint8_t I2C_ReceiveByte(void)
{
    SDA_OUT();
	uint8_t i, Byte = 0x00;
	I2C_W_SDA(1);	
	for (i = 0; i < 8; i ++)
	{
        SDA_IN();
		I2C_W_SCL(1);
		if (I2C_R_SDA() == 1){Byte |= (0x80 >> i);}

		I2C_W_SCL(0);
	}
	return Byte;
}

void I2C_SendAck(uint8_t AckBit)
{
    SDA_OUT();
	I2C_W_SDA(AckBit);
	I2C_W_SCL(1);
	I2C_W_SCL(0);
}

uint8_t I2C_ReceiveAck(void)
{
    SDA_OUT();
	uint8_t AckBit;
	I2C_W_SDA(1);
	I2C_W_SCL(1);
    SDA_IN();
	AckBit = I2C_R_SDA();
	I2C_W_SCL(0);
	return AckBit;
}

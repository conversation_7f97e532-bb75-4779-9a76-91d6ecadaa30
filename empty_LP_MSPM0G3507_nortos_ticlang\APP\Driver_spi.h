#ifndef __Driver_SPI_H
#define __Driver_SPI_H

#include "bsp_system.h"

//----------------------------------------------------------------------------------
// SPI 片选CS
#define MySPI_W_SS(x)  ((x)?(DL_GPIO_setPins(MYSPI_SS_PORT, MYSPI_SS_PIN)) : (DL_GPIO_clearPins(MYSPI_SS_PORT, MYSPI_SS_PIN)))  

//----------------------------------------------------------------------------------
// SPI 时钟线SCK
#define MySPI_W_SCK(x)  ((x)?(DL_GPIO_setPins(MYSPI_SCK_PORT, MYSPI_SCK_PIN)) : (DL_GPIO_clearPins(MYSPI_SCK_PORT, MYSPI_SCK_PIN)))  

//----------------------------------------------------------------------------------
// SPI 数据发送MOSI
#define MySPI_W_MOSI(x)  ((x)?(DL_GPIO_setPins(MYSPI_MOSI_PORT, MYSPI_MOSI_PIN)) : (DL_GPIO_clearPins(MYSPI_MOSI_PORT, MYSPI_MOSI_PIN)))  

//----------------------------------------------------------------------------------
// SPI 数据接收MISO
#define	MySPI_R_MISO() (DL_GPIO_readPins(MYSPI_MISO_PORT, MYSPI_MISO_PIN))

void MySPI_Start(void);
void MySPI_Stop(void);
uint8_t MySPI_SwapByte(uint8_t ByteSend);

#endif 


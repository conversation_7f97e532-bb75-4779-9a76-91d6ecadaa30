#ifndef BSP_SYSTEM_H
#define BSP_SYSTEM_H

#include <stdint.h>  
#include "stdio.h"
#include "string.h"
#include "ti_msp_dl_config.h"

#include "schedule.h"
#include "bsp_system.h"
#include "Driver_key.h"
#include "Driver_uart.h"
#include "Driver_adc.h"

extern uint32_t system_tick_ms;     //当前程序运行的时间
extern uint8_t key_val;  // 当前按键状态
extern uint8_t key_old;  // 前一按键状态
extern uint8_t key_down; // 按下的按键
extern uint8_t key_up;   // 释放的按键
extern uint8_t key_early;   //上一次的按键

#endif


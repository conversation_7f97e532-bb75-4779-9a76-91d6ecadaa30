******************************************************************************
            TI ARM Clang Linker PC v3.2.2                      
******************************************************************************
>> Linked Wed Jul  9 15:19:31 2025

OUTPUT FILE NAME:   <Template_G3507.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00005231


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000064b8  00019b48  R  X
  SRAM                  20200000   00008000  00001347  00006cb9  RW X
  BCR_CONFIG            41c00000   00000080  00000000  00000080  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000064b8   000064b8    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00005af0   00005af0    r-x .text
  00005bb0    00005bb0    000008d0   000008d0    r-- .rodata
  00006480    00006480    00000038   00000038    r-- .cinit
20200000    20200000    00001147   00000000    rw-
  20200000    20200000    00000ed4   00000000    rw- .bss
  20200ed4    20200ed4    00000273   00000000    rw- .data
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00005af0     
                  000000c0    00000a70     libc.a : e_pow.c.obj (.text.pow)
                  00000b30    00000a00            : _printfi.c.obj (.text:__TI_printfi)
                  00001530    00000384     BSP_4x4KEY.o (.text.Read4X4KEY)
                  000018b4    000002e0     libc.a : e_log10.c.obj (.text.log10)
                  00001b94    00000220            : _printfi.c.obj (.text._pconv_a)
                  00001db4    000001dc     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00001f90    000001dc     libc.a : _printfi.c.obj (.text._pconv_g)
                  0000216c    00000192     libclang_rt.builtins.a : adddf3.S.obj (.text.adddf3_subdf3)
                  000022fe    00000002                            : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00002300    00000184     libc.a : e_sqrt.c.obj (.text.sqrt)
                  00002484    00000168     AD9959.o (.text.WriteData_AD9959)
                  000025ec    00000158     EPD_GUI.o (.text.EPD_ShowChar)
                  00002744    0000013c     libc.a : _printfi.c.obj (.text.fcvt)
                  00002880    00000138     AD9959.o (.text.Write_Phase)
                  000029b8    00000138     AD9959.o (.text.Write_Phase_no_update)
                  00002af0    00000138     AD9959.o (.text.Write_frequence_no_update)
                  00002c28    0000012c     main.o (.text.TaskA_Handler)
                  00002d54    00000120     libc.a : _printfi.c.obj (.text._pconv_e)
                  00002e74    0000011c     AD9959.o (.text.Write_Amplitude_no_update)
                  00002f90    00000118     AD9959.o (.text.Write_Amplitude)
                  000030a8    0000010c     AD9959.o (.text.Write_frequence)
                  000031b4    0000010c     libclang_rt.builtins.a : divdf3.S.obj (.text.__divdf3)
                  000032c0    000000fc     AD9959.o (.text.Init_AD9959)
                  000033bc    000000f8     AD9959.o (.text.SweepFre)
                  000034b4    000000f4     EPD_GUI.o (.text.Paint_SetPixel)
                  000035a8    000000e8     driverlib.a : dl_timer.o (.text.DL_Timer_initTimerMode)
                  00003690    000000e4     libclang_rt.builtins.a : muldf3.S.obj (.text.__muldf3)
                  00003774    000000d8     libc.a : s_scalbn.c.obj (.text.scalbn)
                  0000384c    000000c4     driverlib.a : dl_timer.o (.text.DL_Timer_initPWMMode)
                  00003910    000000a2     libclang_rt.builtins.a : udivmoddi4.S.obj (.text)
                  000039b2    00000002                            : aeabi_div0.c.obj (.text.__aeabi_ldiv0)
                  000039b4    0000009c     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00003a50    00000094     EPD_GUI.o (.text.Paint_NewImage)
                  00003ae4    00000090     ti_msp_dl_config.o (.text.SYSCFG_DL_ADC12_0_init)
                  00003b74    0000008c     AD9959.o (.text.Intserve)
                  00003c00    0000008c     libclang_rt.builtins.a : mulsf3.S.obj (.text.__mulsf3)
                  00003c8c    00000088     driverlib.a : dl_dac12.o (.text.DL_DAC12_init)
                  00003d14    00000084     SPI_Init.o (.text.EPD_WR_Bus)
                  00003d98    00000082     libclang_rt.builtins.a : divsf3.S.obj (.text.__divsf3)
                  00003e1a    00000002     --HOLE-- [fill = 0]
                  00003e1c    0000007c     EPD_GUI.o (.text.EPD_ClearAll)
                  00003e98    00000078     EPD.o (.text.EPD_Display)
                  00003f10    00000078     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  00003f88    00000074     EPD.o (.text.EPD_Display_Clear)
                  00003ffc    00000004     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00004000    00000074     libclang_rt.builtins.a : truncdfsf2.S.obj (.text.__truncdfsf2)
                  00004074    00000070     main.o (.text.Show_menu)
                  000040e4    00000070     libclang_rt.builtins.a : comparedf2.c.obj (.text.__gedf2)
                  00004154    0000006c     EPD_GUI.o (.text.Paint_Clear)
                  000041c0    0000006c     libclang_rt.builtins.a : comparedf2.c.obj (.text.__ledf2)
                  0000422c    00000068     EPD.o (.text.EPD_FastMode2Init)
                  00004294    00000066     libc.a : _printfi.c.obj (.text._mcpy)
                  000042fa    00000002     --HOLE-- [fill = 0]
                  000042fc    00000064     ti_msp_dl_config.o (.text.SYSCFG_DL_PWM_0_init)
                  00004360    00000062     libclang_rt.builtins.a : aeabi_dcmp.S.obj (.text.__aeabi_dcmp)
                  000043c2    0000005e     EPD_GUI.o (.text.EPD_ShowString)
                  00004420    0000005c     libc.a : s_frexp.c.obj (.text.frexp)
                  0000447c    00000058            : _ltoa.c.obj (.text.__TI_ltoa)
                  000044d4    00000058            : _printfi.c.obj (.text._pconv_f)
                  0000452c    00000056     libclang_rt.builtins.a : aeabi_idivmod.S.obj (.text.__aeabi_idivmod)
                  00004582    00000002     --HOLE-- [fill = 0]
                  00004584    00000054     main.o (.text.TIMA0_IRQHandler)
                  000045d8    00000052     libc.a : _printfi.c.obj (.text._ecpy)
                  0000462a    00000002     --HOLE-- [fill = 0]
                  0000462c    0000004c     ti_msp_dl_config.o (.text.DL_ADC12_initSingleSample)
                  00004678    0000004c     driverlib.a : dl_dma.o (.text.DL_DMA_initChannel)
                  000046c4    0000004c     ti_msp_dl_config.o (.text.DL_UART_setBaudRateDivisor)
                  00004710    0000004c     main.o (.text.Init_All)
                  0000475c    0000004c     BSP_Spwm.o (.text.TIMG8_IRQHandler)
                  000047a8    0000004c     User_ADC.o (.text.User_ADC_Init)
                  000047f4    0000004a     ti_msp_dl_config.o (.text.DL_ADC12_configConversionMem)
                  0000483e    00000002     --HOLE-- [fill = 0]
                  00004840    0000004a     libclang_rt.builtins.a : fixdfsi.S.obj (.text.__fixdfsi)
                  0000488a    00000002     --HOLE-- [fill = 0]
                  0000488c    00000048     driverlib.a : dl_uart.o (.text.DL_UART_init)
                  000048d4    00000048     User_ADC.o (.text.Set_Fs)
                  0000491c    00000046     EPD_GUI.o (.text.EPD_ShowNum)
                  00004962    00000002     --HOLE-- [fill = 0]
                  00004964    00000044     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_0_init)
                  000049a8    00000042     libclang_rt.builtins.a : fixunsdfsi.S.obj (.text.__fixunsdfsi)
                  000049ea    00000002     --HOLE-- [fill = 0]
                  000049ec    00000040     driverlib.a : dl_adc12.o (.text.DL_ADC12_setClockConfig)
                  00004a2c    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_UART_0_init)
                  00004a6c    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00004aac    00000040     User_DAC.o (.text.User_DAC_Init)
                  00004aec    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  00004b2c    00000040                            : extendsfdf2.S.obj (.text.__extendsfdf2)
                  00004b6c    00000040     libc.a : atoi.c.obj (.text.atoi)
                  00004bac    0000003c     EPD.o (.text.EPD_Clear_R26H)
                  00004be8    0000003c     EPD.o (.text.EPD_HW_RESET)
                  00004c24    0000003c     libclang_rt.builtins.a : floatsisf.S.obj (.text.__floatsisf)
                  00004c60    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00004c9c    0000003a     libclang_rt.builtins.a : muldsi3.S.obj (.text.__muldsi3)
                  00004cd6    00000002     --HOLE-- [fill = 0]
                  00004cd8    00000038     ti_msp_dl_config.o (.text.DL_Timer_setPublisherChanID)
                  00004d10    00000038     AD9959.o (.text.IO_Update)
                  00004d48    00000038     AD9959.o (.text.IntReset)
                  00004d80    00000038     libclang_rt.builtins.a : fixsfsi.S.obj (.text.__fixsfsi)
                  00004db8    00000038     libc.a : sprintf.c.obj (.text.sprintf)
                  00004df0    00000034     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalInputFeatures)
                  00004e24    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00004e58    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_TIMER_9959_init)
                  00004e8c    00000032     libclang_rt.builtins.a : fixunssfsi.S.obj (.text.__fixunssfsi)
                  00004ebe    00000002     --HOLE-- [fill = 0]
                  00004ec0    00000030     User_ADC.o (.text.DL_DMA_setTransferSize)
                  00004ef0    00000030     User_DAC.o (.text.DL_DMA_setTransferSize)
                  00004f20    00000030     ti_msp_dl_config.o (.text.DL_DMA_setTransferSize)
                  00004f50    00000030     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutputFeatures)
                  00004f80    00000030     SPI_Init.o (.text.EPD_WR_DATA8)
                  00004fb0    00000030     SPI_Init.o (.text.EPD_WR_REG)
                  00004fe0    00000030     libc.a : _printfi.c.obj (.text._fcpy)
                  00005010    0000002c     User_ADC.o (.text.ADC0_IRQHandler)
                  0000503c    0000002c     ti_msp_dl_config.o (.text.DL_ADC12_setDMASamplesCnt)
                  00005068    0000002c     ti_msp_dl_config.o (.text.SYSCFG_DL_DAC12_init)
                  00005094    0000002c     User_ADC.o (.text.__NVIC_EnableIRQ)
                  000050c0    0000002c     main.o (.text.__NVIC_EnableIRQ)
                  000050ec    0000002c     libclang_rt.builtins.a : floatsidf.S.obj (.text.__floatsidf)
                  00005118    00000028     ti_msp_dl_config.o (.text.DL_Common_updateReg)
                  00005140    00000028     User_ADC.o (.text.DL_DMA_setDestAddr)
                  00005168    00000028     User_DAC.o (.text.DL_DMA_setDestAddr)
                  00005190    00000028     User_ADC.o (.text.DL_DMA_setSrcAddr)
                  000051b8    00000028     User_DAC.o (.text.DL_DMA_setSrcAddr)
                  000051e0    00000028     ti_msp_dl_config.o (.text.DL_Timer_enableEvent)
                  00005208    00000028     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH0_init)
                  00005230    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00005258    00000026     User_ADC.o (.text.DL_DMA_enableChannel)
                  0000527e    00000026     User_DAC.o (.text.DL_DMA_enableChannel)
                  000052a4    00000024     libclang_rt.builtins.a : floatunsidf.S.obj (.text.__floatunsidf)
                  000052c8    00000024                            : muldi3.S.obj (.text.__muldi3)
                  000052ec    00000022     Delay.o (.text.delay_ms)
                  0000530e    00000022     libc.a : memccpy.c.obj (.text.memccpy)
                  00005330    00000020     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralInputFunction)
                  00005350    00000020     ti_msp_dl_config.o (.text.DL_SYSCTL_setMFPCLKSource)
                  00005370    00000020     AD9959.o (.text.delay1)
                  00005390    00000020     main.o (.text.main)
                  000053b0    0000001e     ti_msp_dl_config.o (.text.DL_ADC12_setPowerDownMode)
                  000053ce    0000001e     ti_msp_dl_config.o (.text.DL_UART_setOversampling)
                  000053ec    0000001e     libclang_rt.builtins.a : ashldi3.S.obj (.text.__ashldi3)
                  0000540a    00000002     --HOLE-- [fill = 0]
                  0000540c    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_clearInterruptStatus)
                  00005428    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableDMA)
                  00005444    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableDMATrigger)
                  00005460    0000001c     ti_msp_dl_config.o (.text.DL_ADC12_enableInterrupt)
                  0000547c    0000001c     ti_msp_dl_config.o (.text.DL_DAC12_enableInterrupt)
                  00005498    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_clearInterruptStatus)
                  000054b4    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_enableInterrupt)
                  000054d0    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_initPeripheralOutputFunction)
                  000054ec    0000001c     ti_msp_dl_config.o (.text.DL_GPIO_setLowerPinsPolarity)
                  00005508    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setMCLKDivider)
                  00005524    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setSYSOSCFreq)
                  00005540    0000001c     ti_msp_dl_config.o (.text.DL_SYSCTL_setULPCLKDivider)
                  0000555c    0000001c     ti_msp_dl_config.o (.text.DL_Timer_enableInterrupt)
                  00005578    0000001c     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptCompUpdateMethod)
                  00005594    0000001c                 : dl_timer.o (.text.DL_Timer_setClockConfig)
                  000055b0    0000001c     EPD.o (.text.EPD_READBUSY)
                  000055cc    0000001a     ti_msp_dl_config.o (.text.DL_ADC12_setSubscriberChanID)
                  000055e6    0000001a     EPD.o (.text.EPD_FastUpdate)
                  00005600    0000001a     EPD.o (.text.EPD_PartUpdate)
                  0000561a    00000002     --HOLE-- [fill = 0]
                  0000561c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_enablePower)
                  00005634    00000018     ti_msp_dl_config.o (.text.DL_ADC12_reset)
                  0000564c    00000018     ti_msp_dl_config.o (.text.DL_ADC12_setSampleTime0)
                  00005664    00000018     ti_msp_dl_config.o (.text.DL_DAC12_enablePower)
                  0000567c    00000018     ti_msp_dl_config.o (.text.DL_DAC12_reset)
                  00005694    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enableOutput)
                  000056ac    00000018     ti_msp_dl_config.o (.text.DL_GPIO_enablePower)
                  000056c4    00000018     ti_msp_dl_config.o (.text.DL_GPIO_initDigitalOutput)
                  000056dc    00000018     ti_msp_dl_config.o (.text.DL_GPIO_reset)
                  000056f4    00000018     AD9959.o (.text.DL_GPIO_setPins)
                  0000570c    00000018     BSP_4x4KEY.o (.text.DL_GPIO_setPins)
                  00005724    00000018     EPD.o (.text.DL_GPIO_setPins)
                  0000573c    00000018     SPI_Init.o (.text.DL_GPIO_setPins)
                  00005754    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setPins)
                  0000576c    00000018     ti_msp_dl_config.o (.text.DL_GPIO_setUpperPinsPolarity)
                  00005784    00000018     ti_msp_dl_config.o (.text.DL_SYSCTL_setBORThreshold)
                  0000579c    00000018     ti_msp_dl_config.o (.text.DL_Timer_enablePower)
                  000057b4    00000018     ti_msp_dl_config.o (.text.DL_Timer_reset)
                  000057cc    00000018     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareOutCtl)
                  000057e4    00000018     User_ADC.o (.text.DL_Timer_setLoadValue)
                  000057fc    00000018     main.o (.text.DL_Timer_setLoadValue)
                  00005814    00000018     main.o (.text.DL_Timer_startCounter)
                  0000582c    00000018     User_ADC.o (.text.DL_Timer_stopCounter)
                  00005844    00000018     ti_msp_dl_config.o (.text.DL_UART_enablePower)
                  0000585c    00000018     ti_msp_dl_config.o (.text.DL_UART_reset)
                  00005874    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_CH1_init)
                  0000588c    00000018     libc.a : sprintf.c.obj (.text._outs)
                  000058a4    00000016     ti_msp_dl_config.o (.text.DL_ADC12_enableConversions)
                  000058ba    00000016     ti_msp_dl_config.o (.text.DL_DAC12_enable)
                  000058d0    00000016     BSP_4x4KEY.o (.text.DL_GPIO_readPins)
                  000058e6    00000016     EPD.o (.text.DL_GPIO_readPins)
                  000058fc    00000016     ti_msp_dl_config.o (.text.DL_UART_enable)
                  00005912    00000014     AD9959.o (.text.DL_GPIO_clearPins)
                  00005926    00000014     BSP_4x4KEY.o (.text.DL_GPIO_clearPins)
                  0000593a    00000014     EPD.o (.text.DL_GPIO_clearPins)
                  0000594e    00000014     SPI_Init.o (.text.DL_GPIO_clearPins)
                  00005962    00000014     ti_msp_dl_config.o (.text.DL_GPIO_clearPins)
                  00005976    00000002     --HOLE-- [fill = 0]
                  00005978    00000014     ti_msp_dl_config.o (.text.DL_SYSCTL_disableSYSPLL)
                  0000598c    00000014     ti_msp_dl_config.o (.text.DL_Timer_enableClock)
                  000059a0    00000014     ti_msp_dl_config.o (.text.DL_Timer_setCCPDirection)
                  000059b4    00000014     libclang_rt.builtins.a : aeabi_uldivmod.S.obj (.text.__aeabi_uldivmod)
                  000059c8    00000014     libc.a : _printfi.c.obj (.text.strchr)
                  000059dc    00000012     User_ADC.o (.text.DL_ADC12_getPendingInterrupt)
                  000059ee    00000012     BSP_Spwm.o (.text.DL_Timer_getPendingInterrupt)
                  00005a00    00000012     main.o (.text.DL_Timer_getPendingInterrupt)
                  00005a12    00000012     driverlib.a : dl_uart.o (.text.DL_UART_setClockConfig)
                  00005a24    00000012     libc.a : memcpy16.S.obj (.text:TI_memcpy_small)
                  00005a36    00000012            : copy_decompress_none.c.obj (.text:decompress:none)
                  00005a48    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_disableHFXT)
                  00005a58    00000010     ti_msp_dl_config.o (.text.DL_SYSCTL_enableMFPCLK)
                  00005a68    00000010     driverlib.a : dl_timer.o (.text.DL_Timer_setCaptureCompareValue)
                  00005a78    00000010     libc.a : wcslen.c.obj (.text.wcslen)
                  00005a88    00000010            : copy_zero_init.c.obj (.text:decompress:ZI)
                  00005a98    0000000e     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memset)
                  00005aa6    0000000e     libc.a : _printfi.c.obj (.text.strlen)
                  00005ab4    0000000e            : memset16.S.obj (.text:TI_memset_small)
                  00005ac2    0000000c     ti_msp_dl_config.o (.text.SYSCFG_DL_DMA_init)
                  00005ace    00000002     --HOLE-- [fill = 0]
                  00005ad0    0000000c     libclang_rt.builtins.a : aeabi_memset.S.obj (.text.__aeabi_memclr)
                  00005adc    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00005ae6    0000000a     libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005af0    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dsub.1)
                  00005b00    0000000a     libc.a : e_log10.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005b0a    00000002     --HOLE-- [fill = 0]
                  00005b0c    00000010     libclang_rt.builtins.a : muldf3.S.obj (.tramp.__aeabi_dmul.1)
                  00005b1c    0000000a     libc.a : e_pow.c.obj (.text.OUTLINED_FUNCTION_0)
                  00005b26    0000000a            : _printfi.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005b30    0000000a            : e_pow.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005b3a    0000000a            : sprintf.c.obj (.text._outc)
                  00005b44    00000008            : e_pow.c.obj (.text.OUTLINED_FUNCTION_2)
                  00005b4c    00000008            : aeabi_portable.c.obj (.text.__aeabi_errno_addr)
                  00005b54    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00005b5c    00000006     libc.a : e_log10.c.obj (.text.OUTLINED_FUNCTION_1)
                  00005b62    00000006            : e_pow.c.obj (.text.OUTLINED_FUNCTION_3)
                  00005b68    00000006            : e_pow.c.obj (.text.OUTLINED_FUNCTION_5)
                  00005b6e    00000002     --HOLE-- [fill = 0]
                  00005b70    00000010     libclang_rt.builtins.a : adddf3.S.obj (.tramp.__aeabi_dadd.1)
                  00005b80    00000006     libc.a : e_pow.c.obj (.text.OUTLINED_FUNCTION_6)
                  00005b86    00000004            : e_pow.c.obj (.text.OUTLINED_FUNCTION_4)
                  00005b8a    00000004            : e_pow.c.obj (.text.OUTLINED_FUNCTION_7)
                  00005b8e    00000004            : e_pow.c.obj (.text.OUTLINED_FUNCTION_8)
                  00005b92    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00005b96    00000002     --HOLE-- [fill = 0]
                  00005b98    00000010     libc.a : boot_cortex_m.c.obj (.tramp._c_int00_noargs.1)
                  00005ba8    00000004            : pre_init.c.obj (.text._system_pre_init)
                  00005bac    00000004            : exit.c.obj (.text:abort)

.cinit     0    00006480    00000038     
                  00006480    00000014     (.cinit..data.load) [load image, compression = lzss]
                  00006494    0000000c     (__TI_handler_table)
                  000064a0    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000064a8    00000010     (__TI_cinit_table)

.rodata    0    00005bb0    000008d0     
                  00005bb0    000005f0     EPD_GUI.o (.rodata.asc2_1608)
                  000061a0    00000101     libc.a : aeabi_ctype.S.obj (.rodata:__aeabi_ctype_table_)
                  000062a1    00000001     --HOLE-- [fill = 0]
                  000062a2    00000080     User_DAC.o (.rodata.DAC_Buff)
                  00006322    00000003     ti_msp_dl_config.o (.rodata.gPWM_0ClockConfig)
                  00006325    00000003     ti_msp_dl_config.o (.rodata.gTIMER_0ClockConfig)
                  00006328    00000030     libc.a : e_pow.c.obj (.rodata.cst16)
                  00006358    00000020     ti_msp_dl_config.o (.rodata.gDAC12Config)
                  00006378    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH0Config)
                  00006390    00000018     ti_msp_dl_config.o (.rodata.gDMA_CH1Config)
                  000063a8    00000014     ti_msp_dl_config.o (.rodata.gTIMER_0TimerConfig)
                  000063bc    00000014     ti_msp_dl_config.o (.rodata.gTIMER_9959TimerConfig)
                  000063d0    00000014     EPD_GUI.o (.rodata.str1.160496125614851016371)
                  000063e4    00000011     libc.a : _printfi.c.obj (.rodata.str1.11645776875810915891)
                  000063f5    00000011     main.o (.rodata.str1.120848099768473620991)
                  00006406    00000011     main.o (.rodata.str1.122786433597999586301)
                  00006417    00000011     libc.a : _printfi.c.obj (.rodata.str1.44690500295887128011)
                  00006428    00000011     main.o (.rodata.str1.57136986666369238191)
                  00006439    00000011     main.o (.rodata.str1.93435692222562013921)
                  0000644a    0000000a     ti_msp_dl_config.o (.rodata.gUART_0Config)
                  00006454    0000000a     main.o (.rodata.str1.182859457380636363961)
                  0000645e    00000009     main.o (.rodata.str1.123866747220708346941)
                  00006467    00000001     --HOLE-- [fill = 0]
                  00006468    00000008     ti_msp_dl_config.o (.rodata.gADC12_0ClockConfig)
                  00006470    00000008     ti_msp_dl_config.o (.rodata.gPWM_0Config)
                  00006478    00000003     ti_msp_dl_config.o (.rodata.gTIMER_9959ClockConfig)
                  0000647b    00000002     ti_msp_dl_config.o (.rodata.gUART_0ClockConfig)
                  0000647d    00000003     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.bss       0    20200000    00000ed4     UNINITIALIZED
                  20200000    00000b48     (.common:ImageBW)
                  20200b48    00000200     (.common:gADCSamples)
                  20200d48    000000bc     (.common:gTIMER_0Backup)
                  20200e04    000000bc     (.common:gTIMER_9959Backup)
                  20200ec0    00000014     (.common:Paint)

.data      0    20200ed4    00000273     UNINITIALIZED
                  20200ed4    00000190     AD9959.o (.data.SweepData)
                  20201064    000000c8     BSP_Spwm.o (.data.Pwm_Data)
                  2020112c    00000004     AD9959.o (.data.CFTW0_DATA)
                  20201130    00000004     libc.a : aeabi_portable.c.obj (.data.__aeabi_errno)
                  20201134    00000004     main.o (.data.count)
                  20201138    00000003     AD9959.o (.data.ACR_DATA)
                  2020113b    00000002     AD9959.o (.data.CPOW0_DATA0)
                  2020113d    00000001     AD9959.o (.data.CSR_DATA0)
                  2020113e    00000001     AD9959.o (.data.CSR_DATA1)
                  2020113f    00000001     AD9959.o (.data.CSR_DATA2)
                  20201140    00000001     AD9959.o (.data.CSR_DATA3)
                  20201141    00000001     AD9959.o (.data.CSR_DATAall)
                  20201142    00000001     BSP_Spwm.o (.data.Spwm_cnt)
                  20201143    00000001     User_ADC.o (.data.adc_flag)
                  20201144    00000001     main.o (.data.key_val)
                  20201145    00000001     main.o (.data.sweep_flag)
                  20201146    00000001     main.o (.data.t)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code    ro data   rw data
       ------                         ----    -------   -------
    .\
       ti_msp_dl_config.o             2926    157       376    
    +--+------------------------------+-------+---------+---------+
       Total:                         2926    157       376    
                                                               
    .\App\
       User_ADC.o                     468     0         513    
       User_DAC.o                     230     128       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         698     128       513    
                                                               
    .\BSP\
       AD9959.o                       2956    0         414    
       BSP_4x4KEY.o                   966     0         0      
       BSP_Spwm.o                     94      0         201    
    +--+------------------------------+-------+---------+---------+
       Total:                         4016    0         615    
                                                               
    .\EPD\
       EPD_GUI.o                      1132    1540      20     
       EPD.o                          606     0         0      
       SPI_Init.o                     272     0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2010    1540      20     
                                                               
    .\System\
       Delay.o                        34      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         34      0         0      
                                                               
    .\User\
       main.o                         714     87        2895   
       startup_mspm0g350x_ticlang.o   8       192       0      
    +--+------------------------------+-------+---------+---------+
       Total:                         722     279       2895   
                                                               
    C:/ti/mspm0_sdk_2_01_00_03/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_timer.o                     524     0         0      
       dl_dac12.o                     136     0         0      
       dl_uart.o                      90      0         0      
       dl_dma.o                       76      0         0      
       dl_adc12.o                     64      0         0      
       dl_common.o                    10      0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         900     0         0      
                                                               
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       _printfi.c.obj                 4558    34        0      
       e_pow.c.obj                    2730    48        0      
       e_log10.c.obj                  752     0         0      
       e_sqrt.c.obj                   388     0         0      
       aeabi_ctype.S.obj              0       257       0      
       s_scalbn.c.obj                 216     0         0      
       copy_decompress_lzss.c.obj     120     0         0      
       s_frexp.c.obj                  92      0         0      
       sprintf.c.obj                  90      0         0      
       _ltoa.c.obj                    88      0         0      
       atoi.c.obj                     64      0         0      
       autoinit.c.obj                 60      0         0      
       boot_cortex_m.c.obj            56      0         0      
       memccpy.c.obj                  34      0         0      
       copy_decompress_none.c.obj     18      0         0      
       memcpy16.S.obj                 18      0         0      
       copy_zero_init.c.obj           16      0         0      
       wcslen.c.obj                   16      0         0      
       memset16.S.obj                 14      0         0      
       aeabi_portable.c.obj           8       0         4      
       exit.c.obj                     4       0         0      
       pre_init.c.obj                 4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         9346    339       4      
                                                               
    C:\ti\ccstheia141\ccs\tools\compiler\ti-cgt-armllvm_3.2.2.LTS\lib\clang/15.0.7/lib/armv6m-ti-none-eabi/libclang_rt.builtins.a
       adddf3.S.obj                   434     0         0      
       divdf3.S.obj                   268     0         0      
       muldf3.S.obj                   244     0         0      
       comparedf2.c.obj               220     0         0      
       udivmoddi4.S.obj               162     0         0      
       mulsf3.S.obj                   140     0         0      
       divsf3.S.obj                   130     0         0      
       truncdfsf2.S.obj               116     0         0      
       aeabi_dcmp.S.obj               98      0         0      
       aeabi_idivmod.S.obj            86      0         0      
       fixdfsi.S.obj                  74      0         0      
       fixunsdfsi.S.obj               66      0         0      
       aeabi_uidivmod.S.obj           64      0         0      
       extendsfdf2.S.obj              64      0         0      
       floatsisf.S.obj                60      0         0      
       muldsi3.S.obj                  58      0         0      
       fixsfsi.S.obj                  56      0         0      
       fixunssfsi.S.obj               50      0         0      
       floatsidf.S.obj                44      0         0      
       floatunsidf.S.obj              36      0         0      
       muldi3.S.obj                   36      0         0      
       ashldi3.S.obj                  30      0         0      
       aeabi_memset.S.obj             26      0         0      
       aeabi_uldivmod.S.obj           20      0         0      
       aeabi_memcpy.S.obj             8       0         0      
       aeabi_div0.c.obj               4       0         0      
    +--+------------------------------+-------+---------+---------+
       Total:                         2594    0         0      
                                                               
       Stack:                         0       0         512    
       Linker Generated:              0       56        0      
    +--+------------------------------+-------+---------+---------+
       Grand Total:                   23246   2499      4935   


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000064a8 records: 2, size/record: 8, table size: 16
	.data: load addr=00006480, load size=00000014 bytes, run addr=20200ed4, run size=00000273 bytes, compression=lzss
	.bss: load addr=000064a0, load size=00000008 bytes, run addr=20200000, run size=00000ed4 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00006494 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


FAR CALL TRAMPOLINES

callee name               trampoline name
   callee addr  tramp addr   call addr  call info
--------------  -----------  ---------  ----------------
__aeabi_dsub              $Tramp$TT$L$PI$$__aeabi_dsub
   0000216d     00005af0     00005aee   libc.a : _printfi.c.obj (.text.OUTLINED_FUNCTION_0)
                             00005b4a          : e_pow.c.obj (.text.OUTLINED_FUNCTION_2)
__aeabi_dmul              $Tramp$TT$L$PI$$__aeabi_dmul
   00003691     00005b0c     00005b08   libc.a : e_log10.c.obj (.text.OUTLINED_FUNCTION_0)
                             00005b24          : e_pow.c.obj (.text.OUTLINED_FUNCTION_0)
                             00005b38          : e_pow.c.obj (.text.OUTLINED_FUNCTION_1)
                             00005b60          : e_log10.c.obj (.text.OUTLINED_FUNCTION_1)
                             00005b66          : e_pow.c.obj (.text.OUTLINED_FUNCTION_3)
                             00005b84          : e_pow.c.obj (.text.OUTLINED_FUNCTION_6)
                             00005b8c          : e_pow.c.obj (.text.OUTLINED_FUNCTION_7)
__aeabi_dadd              $Tramp$TT$L$PI$$__aeabi_dadd
   00002177     00005b70     00005b6c   libc.a : e_pow.c.obj (.text.OUTLINED_FUNCTION_5)
                             00005b88          : e_pow.c.obj (.text.OUTLINED_FUNCTION_4)
                             00005b90          : e_pow.c.obj (.text.OUTLINED_FUNCTION_8)
_c_int00_noargs           $Tramp$TT$L$PI$$_c_int00_noargs
   00005231     00005b98     00005b92   startup_mspm0g350x_ticlang.o (.text.Reset_Handler)

[4 trampolines]
[13 trampoline calls]


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                            
-------   ----                            
20201138  ACR_DATA                        
00005011  ADC0_IRQHandler                 
00003ffd  ADC1_IRQHandler                 
00003ffd  AES_IRQHandler                  
00005bac  C$$EXIT                         
00003ffd  CANFD0_IRQHandler               
2020112c  CFTW0_DATA                      
2020113b  CPOW0_DATA0                     
2020113d  CSR_DATA0                       
2020113e  CSR_DATA1                       
2020113f  CSR_DATA2                       
20201140  CSR_DATA3                       
20201141  CSR_DATAall                     
00003ffd  DAC0_IRQHandler                 
000062a2  DAC_Buff                        
000049ed  DL_ADC12_setClockConfig         
00005add  DL_Common_delayCycles           
00003c8d  DL_DAC12_init                   
00004679  DL_DMA_initChannel              
0000384d  DL_Timer_initPWMMode            
000035a9  DL_Timer_initTimerMode          
00005579  DL_Timer_setCaptCompUpdateMethod
000057cd  DL_Timer_setCaptureCompareOutCtl
00005a69  DL_Timer_setCaptureCompareValue 
00005595  DL_Timer_setClockConfig         
0000488d  DL_UART_init                    
00005a13  DL_UART_setClockConfig          
00003ffd  DMA_IRQHandler                  
00003ffd  Default_Handler                 
00003e1d  EPD_ClearAll                    
00004bad  EPD_Clear_R26H                  
00003e99  EPD_Display                     
00003f89  EPD_Display_Clear               
0000422d  EPD_FastMode2Init               
000055e7  EPD_FastUpdate                  
00004be9  EPD_HW_RESET                    
00005601  EPD_PartUpdate                  
000055b1  EPD_READBUSY                    
000025ed  EPD_ShowChar                    
0000491d  EPD_ShowNum                     
000043c3  EPD_ShowString                  
00003d15  EPD_WR_Bus                      
00004f81  EPD_WR_DATA8                    
00004fb1  EPD_WR_REG                      
00003ffd  GROUP0_IRQHandler               
00003ffd  GROUP1_IRQHandler               
00003ffd  HardFault_Handler               
00003ffd  I2C0_IRQHandler                 
00003ffd  I2C1_IRQHandler                 
00004d11  IO_Update                       
20200000  ImageBW                         
000032c1  Init_AD9959                     
00004711  Init_All                        
00004d49  IntReset                        
00003b75  Intserve                        
00003ffd  NMI_Handler                     
20200ec0  Paint                           
00004155  Paint_Clear                     
00003a51  Paint_NewImage                  
000034b5  Paint_SetPixel                  
00003ffd  PendSV_Handler                  
20201064  Pwm_Data                        
00003ffd  RTC_IRQHandler                  
00001531  Read4X4KEY                      
00005b93  Reset_Handler                   
00003ffd  SPI0_IRQHandler                 
00003ffd  SPI1_IRQHandler                 
00003ffd  SVC_Handler                     
00003ae5  SYSCFG_DL_ADC12_0_init          
00005069  SYSCFG_DL_DAC12_init            
00005209  SYSCFG_DL_DMA_CH0_init          
00005875  SYSCFG_DL_DMA_CH1_init          
00005ac3  SYSCFG_DL_DMA_init              
00001db5  SYSCFG_DL_GPIO_init             
000042fd  SYSCFG_DL_PWM_0_init            
00004e25  SYSCFG_DL_SYSCTL_init           
00004965  SYSCFG_DL_TIMER_0_init          
00004e59  SYSCFG_DL_TIMER_9959_init       
00004a2d  SYSCFG_DL_UART_0_init           
00004a6d  SYSCFG_DL_init                  
000039b5  SYSCFG_DL_initPower             
000048d5  Set_Fs                          
00004075  Show_menu                       
20201142  Spwm_cnt                        
20200ed4  SweepData                       
000033bd  SweepFre                        
00003ffd  SysTick_Handler                 
00004585  TIMA0_IRQHandler                
00003ffd  TIMA1_IRQHandler                
00003ffd  TIMG0_IRQHandler                
00003ffd  TIMG12_IRQHandler               
00003ffd  TIMG6_IRQHandler                
00003ffd  TIMG7_IRQHandler                
0000475d  TIMG8_IRQHandler                
00005a25  TI_memcpy_small                 
00005ab5  TI_memset_small                 
00003ffd  UART0_IRQHandler                
00003ffd  UART1_IRQHandler                
00003ffd  UART2_IRQHandler                
00003ffd  UART3_IRQHandler                
000047a9  User_ADC_Init                   
00004aad  User_DAC_Init                   
00002485  WriteData_AD9959                
00002f91  Write_Amplitude                 
00002e75  Write_Amplitude_no_update       
00002881  Write_Phase                     
000029b9  Write_Phase_no_update           
000030a9  Write_frequence                 
00002af1  Write_frequence_no_update       
20208000  __STACK_END                     
00000200  __STACK_SIZE                    
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
000064a8  __TI_CINIT_Base                 
000064b8  __TI_CINIT_Limit                
000064b8  __TI_CINIT_Warm                 
00006494  __TI_Handler_Table_Base         
000064a0  __TI_Handler_Table_Limit        
00004c61  __TI_auto_init_nobinit_nopinit  
00003f11  __TI_decompress_lzss            
00005a37  __TI_decompress_none            
0000447d  __TI_ltoa                       
ffffffff  __TI_pprof_out_hndl             
00000b31  __TI_printfi                    
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
00000000  __TI_static_base__              
00005a89  __TI_zero_init                  
00002177  __adddf3                        
000061a0  __aeabi_ctype_table_            
000061a0  __aeabi_ctype_table_C           
00004001  __aeabi_d2f                     
00004841  __aeabi_d2iz                    
000049a9  __aeabi_d2uiz                   
00002177  __aeabi_dadd                    
00004361  __aeabi_dcmpeq                  
0000439d  __aeabi_dcmpge                  
000043b1  __aeabi_dcmpgt                  
00004389  __aeabi_dcmple                  
00004375  __aeabi_dcmplt                  
000031b5  __aeabi_ddiv                    
00003691  __aeabi_dmul                    
0000216d  __aeabi_dsub                    
20201130  __aeabi_errno                   
00005b4d  __aeabi_errno_addr              
00004b2d  __aeabi_f2d                     
00004d81  __aeabi_f2iz                    
00004e8d  __aeabi_f2uiz                   
00003d99  __aeabi_fdiv                    
00003c01  __aeabi_fmul                    
000050ed  __aeabi_i2d                     
00004c25  __aeabi_i2f                     
0000452d  __aeabi_idiv                    
000022ff  __aeabi_idiv0                   
0000452d  __aeabi_idivmod                 
000039b3  __aeabi_ldiv0                   
000053ed  __aeabi_llsl                    
000052c9  __aeabi_lmul                    
00005ad1  __aeabi_memclr                  
00005ad1  __aeabi_memclr4                 
00005ad1  __aeabi_memclr8                 
00005b55  __aeabi_memcpy                  
00005b55  __aeabi_memcpy4                 
00005b55  __aeabi_memcpy8                 
00005a99  __aeabi_memset                  
00005a99  __aeabi_memset4                 
00005a99  __aeabi_memset8                 
000052a5  __aeabi_ui2d                    
00004aed  __aeabi_uidiv                   
00004aed  __aeabi_uidivmod                
000059b5  __aeabi_uldivmod                
000053ed  __ashldi3                       
ffffffff  __binit__                       
000041c1  __cmpdf2                        
000031b5  __divdf3                        
00003d99  __divsf3                        
000041c1  __eqdf2                         
00004b2d  __extendsfdf2                   
00004841  __fixdfsi                       
00004d81  __fixsfsi                       
000049a9  __fixunsdfsi                    
00004e8d  __fixunssfsi                    
000050ed  __floatsidf                     
00004c25  __floatsisf                     
000052a5  __floatunsidf                   
000040e5  __gedf2                         
000040e5  __gtdf2                         
000041c1  __ledf2                         
000041c1  __ltdf2                         
UNDEFED   __mpu_init                      
00003691  __muldf3                        
000052c9  __muldi3                        
00004c9d  __muldsi3                       
00003c01  __mulsf3                        
000041c1  __nedf2                         
20207e00  __stack                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
0000216d  __subdf3                        
00004001  __truncdfsf2                    
00003911  __udivmoddi4                    
00005231  _c_int00_noargs                 
UNDEFED   _system_post_cinit              
00005ba9  _system_pre_init                
00005bad  abort                           
20201143  adc_flag                        
00005bb0  asc2_1608                       
00004b6d  atoi                            
ffffffff  binit                           
20201134  count                           
00005371  delay1                          
000052ed  delay_ms                        
00004421  frexp                           
00004421  frexpl                          
20200b48  gADCSamples                     
20200d48  gTIMER_0Backup                  
20200e04  gTIMER_9959Backup               
00000000  interruptVectors                
20201144  key_val                         
00003775  ldexp                           
00003775  ldexpl                          
000018b5  log10                           
000018b5  log10l                          
00005391  main                            
0000530f  memccpy                         
000000c1  pow                             
000000c1  powl                            
00003775  scalbn                          
00003775  scalbnl                         
00004db9  sprintf                         
00002301  sqrt                            
00002301  sqrtl                           
20201145  sweep_flag                      
20201146  t                               
00005a79  wcslen                          


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                            
-------   ----                            
00000000  __TI_ATRegion0_region_sz        
00000000  __TI_ATRegion0_src_addr         
00000000  __TI_ATRegion0_trg_addr         
00000000  __TI_ATRegion1_region_sz        
00000000  __TI_ATRegion1_src_addr         
00000000  __TI_ATRegion1_trg_addr         
00000000  __TI_ATRegion2_region_sz        
00000000  __TI_ATRegion2_src_addr         
00000000  __TI_ATRegion2_trg_addr         
00000000  __TI_static_base__              
00000000  interruptVectors                
000000c1  pow                             
000000c1  powl                            
00000200  __STACK_SIZE                    
00000b31  __TI_printfi                    
00001531  Read4X4KEY                      
000018b5  log10                           
000018b5  log10l                          
00001db5  SYSCFG_DL_GPIO_init             
0000216d  __aeabi_dsub                    
0000216d  __subdf3                        
00002177  __adddf3                        
00002177  __aeabi_dadd                    
000022ff  __aeabi_idiv0                   
00002301  sqrt                            
00002301  sqrtl                           
00002485  WriteData_AD9959                
000025ed  EPD_ShowChar                    
00002881  Write_Phase                     
000029b9  Write_Phase_no_update           
00002af1  Write_frequence_no_update       
00002e75  Write_Amplitude_no_update       
00002f91  Write_Amplitude                 
000030a9  Write_frequence                 
000031b5  __aeabi_ddiv                    
000031b5  __divdf3                        
000032c1  Init_AD9959                     
000033bd  SweepFre                        
000034b5  Paint_SetPixel                  
000035a9  DL_Timer_initTimerMode          
00003691  __aeabi_dmul                    
00003691  __muldf3                        
00003775  ldexp                           
00003775  ldexpl                          
00003775  scalbn                          
00003775  scalbnl                         
0000384d  DL_Timer_initPWMMode            
00003911  __udivmoddi4                    
000039b3  __aeabi_ldiv0                   
000039b5  SYSCFG_DL_initPower             
00003a51  Paint_NewImage                  
00003ae5  SYSCFG_DL_ADC12_0_init          
00003b75  Intserve                        
00003c01  __aeabi_fmul                    
00003c01  __mulsf3                        
00003c8d  DL_DAC12_init                   
00003d15  EPD_WR_Bus                      
00003d99  __aeabi_fdiv                    
00003d99  __divsf3                        
00003e1d  EPD_ClearAll                    
00003e99  EPD_Display                     
00003f11  __TI_decompress_lzss            
00003f89  EPD_Display_Clear               
00003ffd  ADC1_IRQHandler                 
00003ffd  AES_IRQHandler                  
00003ffd  CANFD0_IRQHandler               
00003ffd  DAC0_IRQHandler                 
00003ffd  DMA_IRQHandler                  
00003ffd  Default_Handler                 
00003ffd  GROUP0_IRQHandler               
00003ffd  GROUP1_IRQHandler               
00003ffd  HardFault_Handler               
00003ffd  I2C0_IRQHandler                 
00003ffd  I2C1_IRQHandler                 
00003ffd  NMI_Handler                     
00003ffd  PendSV_Handler                  
00003ffd  RTC_IRQHandler                  
00003ffd  SPI0_IRQHandler                 
00003ffd  SPI1_IRQHandler                 
00003ffd  SVC_Handler                     
00003ffd  SysTick_Handler                 
00003ffd  TIMA1_IRQHandler                
00003ffd  TIMG0_IRQHandler                
00003ffd  TIMG12_IRQHandler               
00003ffd  TIMG6_IRQHandler                
00003ffd  TIMG7_IRQHandler                
00003ffd  UART0_IRQHandler                
00003ffd  UART1_IRQHandler                
00003ffd  UART2_IRQHandler                
00003ffd  UART3_IRQHandler                
00004001  __aeabi_d2f                     
00004001  __truncdfsf2                    
00004075  Show_menu                       
000040e5  __gedf2                         
000040e5  __gtdf2                         
00004155  Paint_Clear                     
000041c1  __cmpdf2                        
000041c1  __eqdf2                         
000041c1  __ledf2                         
000041c1  __ltdf2                         
000041c1  __nedf2                         
0000422d  EPD_FastMode2Init               
000042fd  SYSCFG_DL_PWM_0_init            
00004361  __aeabi_dcmpeq                  
00004375  __aeabi_dcmplt                  
00004389  __aeabi_dcmple                  
0000439d  __aeabi_dcmpge                  
000043b1  __aeabi_dcmpgt                  
000043c3  EPD_ShowString                  
00004421  frexp                           
00004421  frexpl                          
0000447d  __TI_ltoa                       
0000452d  __aeabi_idiv                    
0000452d  __aeabi_idivmod                 
00004585  TIMA0_IRQHandler                
00004679  DL_DMA_initChannel              
00004711  Init_All                        
0000475d  TIMG8_IRQHandler                
000047a9  User_ADC_Init                   
00004841  __aeabi_d2iz                    
00004841  __fixdfsi                       
0000488d  DL_UART_init                    
000048d5  Set_Fs                          
0000491d  EPD_ShowNum                     
00004965  SYSCFG_DL_TIMER_0_init          
000049a9  __aeabi_d2uiz                   
000049a9  __fixunsdfsi                    
000049ed  DL_ADC12_setClockConfig         
00004a2d  SYSCFG_DL_UART_0_init           
00004a6d  SYSCFG_DL_init                  
00004aad  User_DAC_Init                   
00004aed  __aeabi_uidiv                   
00004aed  __aeabi_uidivmod                
00004b2d  __aeabi_f2d                     
00004b2d  __extendsfdf2                   
00004b6d  atoi                            
00004bad  EPD_Clear_R26H                  
00004be9  EPD_HW_RESET                    
00004c25  __aeabi_i2f                     
00004c25  __floatsisf                     
00004c61  __TI_auto_init_nobinit_nopinit  
00004c9d  __muldsi3                       
00004d11  IO_Update                       
00004d49  IntReset                        
00004d81  __aeabi_f2iz                    
00004d81  __fixsfsi                       
00004db9  sprintf                         
00004e25  SYSCFG_DL_SYSCTL_init           
00004e59  SYSCFG_DL_TIMER_9959_init       
00004e8d  __aeabi_f2uiz                   
00004e8d  __fixunssfsi                    
00004f81  EPD_WR_DATA8                    
00004fb1  EPD_WR_REG                      
00005011  ADC0_IRQHandler                 
00005069  SYSCFG_DL_DAC12_init            
000050ed  __aeabi_i2d                     
000050ed  __floatsidf                     
00005209  SYSCFG_DL_DMA_CH0_init          
00005231  _c_int00_noargs                 
000052a5  __aeabi_ui2d                    
000052a5  __floatunsidf                   
000052c9  __aeabi_lmul                    
000052c9  __muldi3                        
000052ed  delay_ms                        
0000530f  memccpy                         
00005371  delay1                          
00005391  main                            
000053ed  __aeabi_llsl                    
000053ed  __ashldi3                       
00005579  DL_Timer_setCaptCompUpdateMethod
00005595  DL_Timer_setClockConfig         
000055b1  EPD_READBUSY                    
000055e7  EPD_FastUpdate                  
00005601  EPD_PartUpdate                  
000057cd  DL_Timer_setCaptureCompareOutCtl
00005875  SYSCFG_DL_DMA_CH1_init          
000059b5  __aeabi_uldivmod                
00005a13  DL_UART_setClockConfig          
00005a25  TI_memcpy_small                 
00005a37  __TI_decompress_none            
00005a69  DL_Timer_setCaptureCompareValue 
00005a79  wcslen                          
00005a89  __TI_zero_init                  
00005a99  __aeabi_memset                  
00005a99  __aeabi_memset4                 
00005a99  __aeabi_memset8                 
00005ab5  TI_memset_small                 
00005ac3  SYSCFG_DL_DMA_init              
00005ad1  __aeabi_memclr                  
00005ad1  __aeabi_memclr4                 
00005ad1  __aeabi_memclr8                 
00005add  DL_Common_delayCycles           
00005b4d  __aeabi_errno_addr              
00005b55  __aeabi_memcpy                  
00005b55  __aeabi_memcpy4                 
00005b55  __aeabi_memcpy8                 
00005b93  Reset_Handler                   
00005ba9  _system_pre_init                
00005bac  C$$EXIT                         
00005bad  abort                           
00005bb0  asc2_1608                       
000061a0  __aeabi_ctype_table_            
000061a0  __aeabi_ctype_table_C           
000062a2  DAC_Buff                        
00006494  __TI_Handler_Table_Base         
000064a0  __TI_Handler_Table_Limit        
000064a8  __TI_CINIT_Base                 
000064b8  __TI_CINIT_Limit                
000064b8  __TI_CINIT_Warm                 
20200000  ImageBW                         
20200000  __start___llvm_prf_bits         
20200000  __start___llvm_prf_cnts         
20200000  __stop___llvm_prf_bits          
20200000  __stop___llvm_prf_cnts          
20200b48  gADCSamples                     
20200d48  gTIMER_0Backup                  
20200e04  gTIMER_9959Backup               
20200ec0  Paint                           
20200ed4  SweepData                       
20201064  Pwm_Data                        
2020112c  CFTW0_DATA                      
20201130  __aeabi_errno                   
20201134  count                           
20201138  ACR_DATA                        
2020113b  CPOW0_DATA0                     
2020113d  CSR_DATA0                       
2020113e  CSR_DATA1                       
2020113f  CSR_DATA2                       
20201140  CSR_DATA3                       
20201141  CSR_DATAall                     
20201142  Spwm_cnt                        
20201143  adc_flag                        
20201144  key_val                         
20201145  sweep_flag                      
20201146  t                               
20207e00  __stack                         
20208000  __STACK_END                     
ffffffff  __TI_pprof_out_hndl             
ffffffff  __TI_prof_data_size             
ffffffff  __TI_prof_data_start            
ffffffff  __binit__                       
ffffffff  binit                           
UNDEFED   __mpu_init                      
UNDEFED   _system_post_cinit              

[244 symbols]

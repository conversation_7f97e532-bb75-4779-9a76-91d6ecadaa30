/**
 * These arguments were used when this file was generated. They will be automatically applied on subsequent loads
 * via the GUI or CLI. Run CLI with '--help' for additional information on how to override these arguments.
 * @cliArgs --device "MSPM0G350X" --part "Default" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @v2CliArgs --device "MSPM0G3505" --package "LQFP-64(PM)" --product "mspm0_sdk@**********"
 * @versions {"tool":"1.21.1+3772"}
 */

/**
 * Import the modules used in this configuration.
 */
const ADC12         = scripting.addModule("/ti/driverlib/ADC12", {}, false);
const ADC121        = ADC12.addInstance();
const DAC12         = scripting.addModule("/ti/driverlib/DAC12");
const GPIO          = scripting.addModule("/ti/driverlib/GPIO", {}, false);
const GPIO1         = GPIO.addInstance();
const GPIO2         = GPIO.addInstance();
const GPIO3         = GPIO.addInstance();
const GPIO4         = GPIO.addInstance();
const GPIO5         = GPIO.addInstance();
const GPIO6         = GPIO.addInstance();
const GPIO7         = GPIO.addInstance();
const PWM           = scripting.addModule("/ti/driverlib/PWM", {}, false);
const PWM1          = PWM.addInstance();
const SYSCTL        = scripting.addModule("/ti/driverlib/SYSCTL");
const TIMER         = scripting.addModule("/ti/driverlib/TIMER", {}, false);
const TIMER1        = TIMER.addInstance();
const TIMER2        = TIMER.addInstance();
const UART          = scripting.addModule("/ti/driverlib/UART", {}, false);
const UART1         = UART.addInstance();
const ProjectConfig = scripting.addModule("/ti/project_config/ProjectConfig");

/**
 * Write custom configuration values to the imported modules.
 */
ADC121.$name                             = "ADC12_0";
ADC121.powerDownMode                     = "DL_ADC12_POWER_DOWN_MODE_MANUAL";
ADC121.sampleTime0                       = "62.5ns";
ADC121.enabledInterrupts                 = ["DL_ADC12_INTERRUPT_DMA_DONE"];
ADC121.configureDMA                      = true;
ADC121.adcMem0chansel                    = "DL_ADC12_INPUT_CHAN_3";
ADC121.subChanID                         = 1;
ADC121.adcMem0trig                       = "DL_ADC12_TRIGGER_MODE_TRIGGER_NEXT";
ADC121.trigSrc                           = "DL_ADC12_TRIG_SRC_EVENT";
ADC121.repeatMode                        = true;
ADC121.enabledDMATriggers                = ["DL_ADC12_DMA_MEM0_RESULT_LOADED"];
ADC121.sampCnt                           = 1;
ADC121.peripheral.$assign                = "ADC0";
ADC121.peripheral.adcPin3.$assign        = "PA24";
ADC121.DMA_CHANNEL.$name                 = "DMA_CH0";
ADC121.DMA_CHANNEL.addressMode           = "f2b";
ADC121.DMA_CHANNEL.transferSize          = 1024;
ADC121.DMA_CHANNEL.transferMode          = "FULL_CH_REPEAT_SINGLE";
ADC121.DMA_CHANNEL.srcLength             = "HALF_WORD";
ADC121.DMA_CHANNEL.dstLength             = "HALF_WORD";
ADC121.DMA_CHANNEL.configureTransferSize = true;
ADC121.adcPin3Config.$name               = "ti_driverlib_gpio_GPIOPinGeneric0";

const Board = scripting.addModule("/ti/driverlib/Board", {}, false);

DAC12.dacFIFOEn                 = true;
DAC12.dacSampleTimerRate        = "1M";
DAC12.dacConfigureDMA           = true;
DAC12.dacFIFOThresh             = "TWO_QTRS_EMPTY";
DAC12.dacOutputPinEn            = true;
DAC12.dacEnabledInterrupts      = ["DMA_DONE"];
DAC12.dacAmplifier              = "ON";
DAC12.peripheral.$assign        = "DAC0";
DAC12.peripheral.OutPin.$assign = "PA15";
DAC12.DMA_CHANNEL.$name         = "DMA_CH1";
DAC12.DMA_CHANNEL.addressMode   = "b2f";
DAC12.DMA_CHANNEL.srcLength     = "HALF_WORD";
DAC12.DMA_CHANNEL.dstLength     = "HALF_WORD";
DAC12.DMA_CHANNEL.transferMode  = "FULL_CH_REPEAT_SINGLE";
DAC12.OutPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric1";

GPIO1.port                               = "PORTB";
GPIO1.$name                              = "KeyBoard";
GPIO1.associatedPins.create(8);
GPIO1.associatedPins[0].initialValue     = "SET";
GPIO1.associatedPins[0].assignedPin      = "12";
GPIO1.associatedPins[0].$name            = "COL1";
GPIO1.associatedPins[0].direction        = "INPUT";
GPIO1.associatedPins[0].internalResistor = "PULL_UP";
GPIO1.associatedPins[0].interruptEn      = true;
GPIO1.associatedPins[0].polarity         = "RISE_FALL";
GPIO1.associatedPins[1].initialValue     = "SET";
GPIO1.associatedPins[1].assignedPin      = "17";
GPIO1.associatedPins[1].$name            = "COL2";
GPIO1.associatedPins[1].direction        = "INPUT";
GPIO1.associatedPins[1].internalResistor = "PULL_UP";
GPIO1.associatedPins[1].interruptEn      = true;
GPIO1.associatedPins[1].polarity         = "RISE_FALL";
GPIO1.associatedPins[2].initialValue     = "SET";
GPIO1.associatedPins[2].assignedPin      = "15";
GPIO1.associatedPins[2].$name            = "COL3";
GPIO1.associatedPins[2].direction        = "INPUT";
GPIO1.associatedPins[2].internalResistor = "PULL_UP";
GPIO1.associatedPins[2].interruptEn      = true;
GPIO1.associatedPins[2].polarity         = "RISE_FALL";
GPIO1.associatedPins[3].initialValue     = "SET";
GPIO1.associatedPins[3].assignedPin      = "8";
GPIO1.associatedPins[3].$name            = "COL4";
GPIO1.associatedPins[3].direction        = "INPUT";
GPIO1.associatedPins[3].internalResistor = "PULL_UP";
GPIO1.associatedPins[3].interruptEn      = true;
GPIO1.associatedPins[3].polarity         = "RISE_FALL";
GPIO1.associatedPins[4].assignedPin      = "7";
GPIO1.associatedPins[4].$name            = "ROW1";
GPIO1.associatedPins[4].initialValue     = "SET";
GPIO1.associatedPins[4].internalResistor = "PULL_UP";
GPIO1.associatedPins[5].assignedPin      = "6";
GPIO1.associatedPins[5].$name            = "ROW2";
GPIO1.associatedPins[5].initialValue     = "SET";
GPIO1.associatedPins[5].internalResistor = "PULL_UP";
GPIO1.associatedPins[6].assignedPin      = "0";
GPIO1.associatedPins[6].$name            = "ROW3";
GPIO1.associatedPins[6].initialValue     = "SET";
GPIO1.associatedPins[6].internalResistor = "PULL_UP";
GPIO1.associatedPins[7].assignedPin      = "16";
GPIO1.associatedPins[7].$name            = "ROW4";
GPIO1.associatedPins[7].initialValue     = "SET";
GPIO1.associatedPins[7].internalResistor = "PULL_UP";

GPIO2.port                           = "PORTA";
GPIO2.$name                          = "MYI2C";
GPIO2.associatedPins.create(2);
GPIO2.associatedPins[0].initialValue = "SET";
GPIO2.associatedPins[0].$name        = "SCL";
GPIO2.associatedPins[0].assignedPin  = "0";
GPIO2.associatedPins[0].ioStructure  = "OD";
GPIO2.associatedPins[1].$name        = "SDA";
GPIO2.associatedPins[1].initialValue = "SET";
GPIO2.associatedPins[1].assignedPin  = "1";
GPIO2.associatedPins[1].ioStructure  = "OD";

GPIO3.$name                              = "EPD";
GPIO3.associatedPins.create(6);
GPIO3.associatedPins[0].$name            = "EPD_SCL";
GPIO3.associatedPins[0].assignedPort     = "PORTA";
GPIO3.associatedPins[0].assignedPin      = "8";
GPIO3.associatedPins[1].$name            = "EPD_SDA";
GPIO3.associatedPins[1].assignedPort     = "PORTA";
GPIO3.associatedPins[1].assignedPin      = "26";
GPIO3.associatedPins[2].$name            = "EPD_RES";
GPIO3.associatedPins[2].assignedPort     = "PORTB";
GPIO3.associatedPins[2].assignedPin      = "24";
GPIO3.associatedPins[3].$name            = "EPD_DC";
GPIO3.associatedPins[3].assignedPort     = "PORTB";
GPIO3.associatedPins[3].assignedPin      = "9";
GPIO3.associatedPins[4].$name            = "EPD_CS";
GPIO3.associatedPins[4].assignedPort     = "PORTB";
GPIO3.associatedPins[4].assignedPin      = "2";
GPIO3.associatedPins[5].$name            = "EPD_BUSY";
GPIO3.associatedPins[5].direction        = "INPUT";
GPIO3.associatedPins[5].internalResistor = "PULL_UP";
GPIO3.associatedPins[5].assignedPort     = "PORTB";
GPIO3.associatedPins[5].assignedPin      = "3";

GPIO4.$name                          = "LED";
GPIO4.associatedPins[0].$name        = "BULE";
GPIO4.associatedPins[0].assignedPort = "PORTB";
GPIO4.associatedPins[0].assignedPin  = "22";

GPIO5.$name                              = "MYSPI";
GPIO5.associatedPins.create(4);
GPIO5.associatedPins[0].$name            = "SS";
GPIO5.associatedPins[0].initialValue     = "SET";
GPIO5.associatedPins[0].assignedPort     = "PORTB";
GPIO5.associatedPins[0].assignedPin      = "1";
GPIO5.associatedPins[0].pin.$assign      = "PB1";
GPIO5.associatedPins[1].$name            = "SCK";
GPIO5.associatedPins[1].assignedPort     = "PORTA";
GPIO5.associatedPins[1].assignedPin      = "30";
GPIO5.associatedPins[1].pin.$assign      = "PA30";
GPIO5.associatedPins[2].$name            = "MOSI";
GPIO5.associatedPins[2].assignedPort     = "PORTB";
GPIO5.associatedPins[2].assignedPin      = "20";
GPIO5.associatedPins[3].$name            = "MISO";
GPIO5.associatedPins[3].internalResistor = "PULL_UP";
GPIO5.associatedPins[3].direction        = "INPUT";
GPIO5.associatedPins[3].assignedPort     = "PORTB";
GPIO5.associatedPins[3].assignedPin      = "11";
GPIO5.associatedPins[3].pin.$assign      = "PB11";

GPIO6.port                               = "PORTB";
GPIO6.$name                              = "Button";
GPIO6.associatedPins[0].direction        = "INPUT";
GPIO6.associatedPins[0].internalResistor = "PULL_UP";
GPIO6.associatedPins[0].interruptEn      = true;
GPIO6.associatedPins[0].polarity         = "FALL";
GPIO6.associatedPins[0].assignedPin      = "21";
GPIO6.associatedPins[0].$name            = "S2";

GPIO7.$name                           = "GPIO_9959";
GPIO7.associatedPins.create(13);
GPIO7.associatedPins[0].$name         = "PS0";
GPIO7.associatedPins[0].assignedPort  = "PORTA";
GPIO7.associatedPins[0].pin.$assign   = "PA2";
GPIO7.associatedPins[1].$name         = "PS1";
GPIO7.associatedPins[1].assignedPort  = "PORTB";
GPIO7.associatedPins[1].pin.$assign   = "PB26";
GPIO7.associatedPins[2].$name         = "PS2";
GPIO7.associatedPins[2].assignedPort  = "PORTB";
GPIO7.associatedPins[2].pin.$assign   = "PB27";
GPIO7.associatedPins[3].$name         = "PS3";
GPIO7.associatedPins[3].assignedPort  = "PORTB";
GPIO7.associatedPins[3].pin.$assign   = "PB25";
GPIO7.associatedPins[4].$name         = "SDIO0";
GPIO7.associatedPins[4].ioStructure   = "SD";
GPIO7.associatedPins[4].pin.$assign   = "PA16";
GPIO7.associatedPins[5].$name         = "SDIO1";
GPIO7.associatedPins[5].assignedPort  = "PORTB";
GPIO7.associatedPins[5].pin.$assign   = "PB23";
GPIO7.associatedPins[6].$name         = "SDIO2";
GPIO7.associatedPins[6].assignedPort  = "PORTB";
GPIO7.associatedPins[6].pin.$assign   = "PB19";
GPIO7.associatedPins[7].$name         = "SDIO3";
GPIO7.associatedPins[7].assignedPort  = "PORTB";
GPIO7.associatedPins[7].pin.$assign   = "PB18";
GPIO7.associatedPins[8].$name         = "SCLK";
GPIO7.associatedPins[8].assignedPort  = "PORTB";
GPIO7.associatedPins[8].pin.$assign   = "PB14";
GPIO7.associatedPins[9].$name         = "CS";
GPIO7.associatedPins[9].assignedPort  = "PORTB";
GPIO7.associatedPins[9].pin.$assign   = "PB13";
GPIO7.associatedPins[10].$name        = "UPDATA";
GPIO7.associatedPins[10].assignedPort = "PORTA";
GPIO7.associatedPins[10].pin.$assign  = "PA12";
GPIO7.associatedPins[11].$name        = "RESET";
GPIO7.associatedPins[11].assignedPort = "PORTA";
GPIO7.associatedPins[11].pin.$assign  = "PA28";
GPIO7.associatedPins[12].$name        = "PWR";
GPIO7.associatedPins[12].assignedPort = "PORTA";
GPIO7.associatedPins[12].pin.$assign  = "PA31";

PWM1.$name                      = "PWM_0";
PWM1.ccIndex                    = [0];
PWM1.timerStartTimer            = true;
PWM1.pwmMode                    = "EDGE_ALIGN_UP";
PWM1.timerCount                 = 32000;
PWM1.interrupts                 = ["LOAD_EVENT"];
PWM1.peripheral.$assign         = "TIMG8";
PWM1.peripheral.ccp0Pin.$assign = "PA7";
PWM1.PWM_CHANNEL_0.$name        = "ti_driverlib_pwm_PWMTimerCC0";
PWM1.PWM_CHANNEL_0.dutyCycle    = 50;
PWM1.ccp0PinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric6";

SYSCTL.forceDefaultClkConfig = true;
SYSCTL.MFPCLKEn              = true;

TIMER1.$name                       = "TIMER_0";
TIMER1.timerMode                   = "PERIODIC";
TIMER1.timerPeriod                 = "1us";
TIMER1.event1PublisherChannel      = 1;
TIMER1.event1ControllerInterruptEn = ["ZERO_EVENT"];

TIMER2.$name            = "TIMER_9959";
TIMER2.timerClkPrescale = 256;
TIMER2.timerMode        = "PERIODIC";
TIMER2.interrupts       = ["ZERO"];
TIMER2.timerPeriod      = "20ms";

UART1.$name                    = "UART_0";
UART1.peripheral.$assign       = "UART0";
UART1.peripheral.rxPin.$assign = "PA11";
UART1.peripheral.txPin.$assign = "PA10";
UART1.txPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric2";
UART1.rxPinConfig.$name        = "ti_driverlib_gpio_GPIOPinGeneric3";

ProjectConfig.genLibCMSIS = true;

/**
 * Pinmux solution for unlocked pins/peripherals. This ensures that minor changes to the automatic solver in a future
 * version of the tool will not impact the pinmux you originally saw.  These lines can be completely deleted in order to
 * re-solve from scratch.
 */
ADC121.DMA_CHANNEL.peripheral.$suggestSolution = "DMA_CH1";
Board.peripheral.$suggestSolution              = "DEBUGSS";
Board.peripheral.swclkPin.$suggestSolution     = "PA20";
Board.peripheral.swdioPin.$suggestSolution     = "PA19";
DAC12.DMA_CHANNEL.peripheral.$suggestSolution  = "DMA_CH0";
GPIO1.associatedPins[0].pin.$suggestSolution   = "PB12";
GPIO1.associatedPins[1].pin.$suggestSolution   = "PB17";
GPIO1.associatedPins[2].pin.$suggestSolution   = "PB15";
GPIO1.associatedPins[3].pin.$suggestSolution   = "PB8";
GPIO1.associatedPins[4].pin.$suggestSolution   = "PB7";
GPIO1.associatedPins[5].pin.$suggestSolution   = "PB6";
GPIO1.associatedPins[6].pin.$suggestSolution   = "PB0";
GPIO1.associatedPins[7].pin.$suggestSolution   = "PB16";
GPIO2.associatedPins[0].pin.$suggestSolution   = "PA0";
GPIO2.associatedPins[1].pin.$suggestSolution   = "PA1";
GPIO3.associatedPins[0].pin.$suggestSolution   = "PA8";
GPIO3.associatedPins[1].pin.$suggestSolution   = "PA26";
GPIO3.associatedPins[2].pin.$suggestSolution   = "PB24";
GPIO3.associatedPins[3].pin.$suggestSolution   = "PB9";
GPIO3.associatedPins[4].pin.$suggestSolution   = "PB2";
GPIO3.associatedPins[5].pin.$suggestSolution   = "PB3";
GPIO4.associatedPins[0].pin.$suggestSolution   = "PB22";
GPIO5.associatedPins[2].pin.$suggestSolution   = "PB20";
GPIO6.associatedPins[0].pin.$suggestSolution   = "PB21";
SYSCTL.peripheral.$suggestSolution             = "SYSCTL";
TIMER1.peripheral.$suggestSolution             = "TIMA1";
TIMER2.peripheral.$suggestSolution             = "TIMA0";
